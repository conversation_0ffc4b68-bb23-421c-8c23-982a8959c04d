ui:
  setup:
    embed:
      description: "Usa el modal para ingresar los detalles de tu hub."
    select:
      placeholder: "Selecciona un idioma"
      loadingLabel: "Cargando..."
      loadingDescription: "Por favor espera..."
      chooseOption: "Elige una opción para comenzar..."
    buttons:
      back: "Atrás"
      cancel: "Cancelar"
      refresh: "Actualizar"
      enterHubInfo: "Ingresar Información del Hub"
      hubInfoComplete: "Información del Hub Completa"
      createHub: "Crear Hub"
      completeInfoFirst: "Completa la Info del Hub Primero"
      discoverHubs: "Descubrir Hubs Públicos"
      supportServer: "Servidor de Soporte"
  common:
    titles:
      error: "¡Error!"
      success: "¡Éxito!"
      cancelled: '¡Cancelado!'
    noDescription: "Sin descripción"
    labels:
      date: "Fecha"
      create: 'Crear'
      cancel: 'Cancelar'
    messages:
      loading: "Cargando..."
      pleaseWait: "Por favor espera..."
      notImplemented: "Aún no implementado."
      hubNotFound: "Hub no encontrado. Por favor intenta nuevamente más tarde."
      serverNotFound: "Servidor no encontrado."
      userNotFound: "Usuario no encontrado."
      notSpecified: "No especificado"
      interfacePermission: "No puedes usar esta interfaz."
      hubUpdateFailed: "Error al actualizar la configuración del hub."
      hubConfigDescription: "Personaliza tu hub de InterChat para tu comunidad. Elige qué te gustaría configurar a continuación."
    pagination:
      previous: "Anterior"
      next: "Siguiente"
      page: "Página {current}/{total}"
    errors:
      notYourMenu: "Este no es tu menú."
      cannotControlPagination: "No puedes controlar esta paginación."
      notFound: "No Encontrado"
      invalidFilter: "Filtro Inválido"
    modal:
      hubCreation:
        title: "Crea Tu Hub"
        name:
          label: "Nombre del Hub"
          placeholder: "Ingresa un nombre único para tu hub"
        brief:
          label: "Descripción Breve"
          placeholder: "Un resumen rápido del propósito de tu hub"
        description:
          label: "Descripción Detallada"
          placeholder: "Cuenta a la gente de qué trata tu hub y qué pueden esperar"
        logoUrl:
          label: "URL del Logo del Hub"
          placeholder: "https://ejemplo.com/tu-logo.png"
    create:
      footer:
        getStarted: "¡Haz clic en 'Ingresar Información del Hub' abajo para comenzar!"
  help:
    select:
      placeholder: "Selecciona una categoría"
  preferences:
    title: "Preferencias de Usuario"
    description: "Edita tu configuración personal para hacer que InterChat funcione para ti. Estas son GLOBALES y funcionan en todos los servidores que compartes con InterChat."
    buttons:
      return: "Volver"
    replyMentions:
      title: "Preferencias: Menciones en Respuestas"
      description: "Ahora serás **{status}** cuando alguien responda a tus mensajes. Puedes alternar esto abajo usando el botón, o volver al menú con el botón de volver."
      currentDescription: "Actualmente estás **{status}** cuando te responden dentro de tus mensajes. Puedes alternar esto abajo usando el botón, o volver al menú con el botón de volver."
      mentioned: "mencionado"
      notMentioned: "no mencionado"
    errors:
      noPreferences: "Aún no tienes preferencias configuradas."
      hide: "Ocultar"
    general:
      title: "Preferencias Generales"
      description: "Configura preferencias generales dentro de InterChat. Esto asegura que tu experiencia sea la mejor que podemos ofrecer."
    locale:
      title: "Selección de Idioma"
      description: "Selecciona tu idioma preferido para InterChat."
    select:
      placeholder: "Selecciona una opción"
      category: "Selecciona una categoría"
    badges:
      title: "Preferencias: Insignias"
      description: "Tus insignias ahora son **{status}** dentro de tus mensajes. Puedes alternar esto abajo usando el botón, o volver al menú con el botón de volver."
      currentDescription: "Actualmente tus insignias son **{status}** dentro de tus mensajes. Puedes alternar esto abajo usando el botón, o volver al menú con el botón de volver."
      visible: "visibles"
      hidden: "ocultas"
      buttons:
        show: "Mostrar"
        hide: "Ocultar"
  appeal:
    buttons:
      previous: "Anterior"
      next: "Siguiente"
    select:
      placeholder: "Selecciona una infracción para apelar"
    errors:
      cannotControl: "No puedes controlar este menú de paginación."
      notYourMenu: "Este no es tu menú de apelación."
      nothingSelected: "No se seleccionó nada."
      invalidSelection: "Selección inválida."
      modalError: "Error al abrir el modal de apelación."
    modal:
      title: "Enviar Apelación"
      q1: "¿Por qué crees que esta acción fue incorrecta?"
      q2: "Cuéntanos sobre la situación"
      q3: "Información Adicional"
    success:
      submitted: "{tick} Tu apelación ha sido enviada para revisión."
    viewInfractions:
      title: "Infracciones Recientes de Usuario"
      empty: "No se encontraron infracciones para este usuario."
    status:
      acceptedBy: "Aceptado por @{name}"
      rejectedBy: "Rechazado por @{name}"
    actions:
      decisionTitle: "Decisión de Apelación"
      reasonOptional:
        label: "Razón (Opcional)"
        placeholder: "Explica tu decisión..."
  report:
    buttons:
      toStaff: "Reportar al Staff"
      toHub: "Reportar al Hub"
    modal:
      toStaff:
        title: "Reportar al Staff de InterChat"
      toHub:
        title: "Reportar a Moderadores del Hub"
      reason:
        label: "Razón del Reporte"
        placeholder: "Por favor describe por qué estás reportando este mensaje..."
    status:
      resolvedBy: "Resuelto por {name}"
      ignoredBy: "Ignorado por {name}"
  hubConfig:
    main:
      placeholder: "Selecciona una opción"
      loadingLabel: "Cargando..."
      loadingDescription: "Por favor espera."
    general:
      placeholder: "Choose a setting to configure..."
      editDescription:
        label: "Editar Descripción"
        description: "Actualiza el texto de descripción de tu hub"
      editName:
        label: "Editar Nombre del Hub"
        description: "Cambia el nombre de tu hub (enfriamiento de 10 días)"
      welcomeMessage:
        label: "Mensaje de Bienvenida"
        description: "Establece un mensaje mostrado a nuevos usuarios"
      toggleNsfw:
        label: "Toggle NSFW"
        description: "Mark your hub as NSFW or not"
      togglePrivate:
        label: "Toggle Private"
        description: "Mark your hub as private or not"
    permissions:
      remove:
        label: "Eliminar"
      moderator:
        label: "Moderador"
        description: "Gestionar mensajes, moderar usuarios, manejar reportes y apelaciones"
      manager:
        label: "Gestor"
        description: "Gestionar configuración del hub más todo lo que pueden hacer los moderadores"
      managerOnly:
        description: "Gestionar configuración del hub"
    modules:
      reactions:
        description: "Permitir interacciones basadas en reacciones en mensajes"
      hideLinks:
        description: "Ocultar vistas previas de enlaces en mensajes del hub"
      spamFilter:
        description: "Filtrar automáticamente spam y contenido no deseado"
      blockInvites:
        description: "Bloquear invitaciones a servidores de Discord en mensajes"
      useNicknames:
        description: "Mostrar apodos de usuario en lugar de nombres de usuario"
    logging:
      moderationLogs: "Registros de Moderación"
      joinLeaveLogs: "Registros de Entrada/Salida"
      appealsChannel: "Canal de Apelaciones"
      reportsChannel: "Canal de Reportes"
      networkAlerts: "Alertas de Red"
      messageModeration: "Moderación de Mensajes"
    invites:
      create:
        title: "Entrada Requerida"
        customCode:
          label: "Código Personalizado"
          placeholder: "abc123"
        uses:
          label: "Usos"
          placeholder: "Déjame en blanco para infinitos"
        expiry:
          label: "Expiración"
          placeholder: "1 semana"
      buttons:
        create: "Crear"
  moderation:
    actionNames:
      warned: "Advertido"
      muted: "Silenciado"
      banned: "Baneado"
    actionNouns:
      mute: "silenciado"
      ban: "baneado"
    prep:
      in: "en"
      from: "de"
    actions:
      warn:
        label: "Advertir"
        description: "Emitir una advertencia al objetivo seleccionado"
      mute:
        label: "Silenciar"
        description: "Silenciar un usuario/servidor del hub especificado"
      ban:
        label: "Banear"
        description: "Banear un usuario/servidor del hub especificado"
      unmute:
        label: "Quitar Silencio"
        description: "Revocar un silencio activo en este hub"
      unban:
        label: "Quitar Ban"
        description: "Revocar un ban activo en este hub"
      blacklist:
        label: "Lista Negra"
        description: "Emitir una lista negra global de interchat (usuario/servidor)"
      delete:
        label: "Eliminar"
        description: "Eliminar el mensaje seleccionado en todos los hubs conectados"
    modal:
      title: "Acción de Moderación"
      reason:
        label: "Razón"
        placeholder: "Ingresa una justificación concisa{optional}"
      duration:
        label: "Duración"
        placeholder: "ej. 30m, 2h, 1d"
    hubSelect:
      placeholder: "Selecciona un hub para moderar..."
    hubSelection:
      title: "Selección de Hub"
      description: "Selecciona el hub que quieres moderar."
      fieldHubLabel: "Hub"
      fieldHubPrompt: "Hub Seleccionado"
      prompt: "Selecciona un hub para moderar"
    targetSelection:
      title: "Selección de Objetivo"
      description: "¿A quién quieres {action}?"
      userField: "Usuario"
      serverField: "Servidor"
    actionSelect:
      placeholder: "Selecciona una acción de moderación..."
    fields:
      reason: "Razón"
  infractions:
    buttons:
      userInfractions: "Infracciones de Usuario"
      serverInfractions: "Infracciones de Servidor"
    titles:
      base: "Infracciones · {hubName}"
      userList: "Infracciones de Usuario · {hubName}"
      serverList: "Infracciones de Servidor · {hubName}"
      userSpecific: "Infracciones para {user} · {hubName}"
      serverSpecific: "Infracciones para Servidor {serverId} · {hubName}"
    descriptions:
      base: "Selecciona qué infracciones ver para este hub."
      userListEmpty: "No se encontraron infracciones de usuario para este hub."
      serverListEmpty: "No se encontraron infracciones de servidor para este hub."
      userSpecificEmpty: "No se encontraron infracciones para este usuario en este hub."
      serverSpecificEmpty: "No se encontraron infracciones para este servidor en este hub."
    labels:
      infraction: "Infracción"
    fields:
      userName: "Nombre de Usuario"
      userId: "ID de Usuario"
      serverName: "Nombre del Servidor"
      serverId: "ID del Servidor"
      reason: "Razón"
      moderator: "Moderador"
      issued: "Emitido"
      status: "Estado"
  staff:
    hub:
      section:
        status: "Estado"
        moderation: "Moderación"
      fields:
        name: "Nombre"
        messages: "Mensajes"
        connections: "Conexiones"
        created: "Creado"
        lastActive: "Última Actividad"
        upvotes: "Votos positivos"
        owner: "Dueño"
        location: "Ubicación"
        activity: "Actividad"
        appealCooldown: "Tiempo de Espera para Apelaciones"
        welcomeMessage: "Mensaje de Bienvenida"
        description: "Descripción"
      values:
        messagesThisWeek: "**{count}** esta semana"
        connectionsActive: "**{count}** activas"
        upvotesTotal: "**{count}** total"
      badges:
        verified: "Verificado"
        partnered: "Asociado"
        featured: "Destacado"
        private: "Privado"
        locked: "Bloqueado"
        nsfw: "NSFW"
    server:
      fields:
        name: "Nombre"
        serverId: "ID del Servidor"
        inviteCode: "Código de Invitación"
        messages: "Mensajes"
        connections: "Conexiones"
        status: "Estado"
        created: "Creado"
        lastMessage: "Último Mensaje"
        updated: "Actualizado"
      values:
        messagesTotal: "**{count}** total"
        connectionsActive: "**{count}** activas"
        premium: "Premium"
        standard: "Estándar"
    user:
      fields:
        name: "Nombre"
        userId: "ID de Usuario"
        status: "Estado"
        messages: "Mensajes"
        reputation: "Reputación"
        votes: "Votos"
        hubJoins: "Uniones a Hubs"
        engagement: "Compromiso"
        locale: "Idioma"
        created: "Creado"
        lastMessage: "Último Mensaje"
        lastVote: "Último Voto"
      values:
        staff: "Staff"
        member: "Miembro"
        messagesSent: "**{count}** enviados"
        reputationPoints: "**{count}** puntos"
        votesCast: "**{count}** emitidos"
        hubJoinsTotal: "**{count}** total"
        notSet: "No establecido"
        never: "Nunca"
      sections:
        preferences: "Preferencias"
        createdContent: "Contenido Creado"
        activity: "Actividad"
        donation: "Donación"
        badges: "Insignias"
      preferences:
        showBadges: "Mostrar insignias"
        mentionOnReply: "Mencionar al responder"
        showNsfwHubs: "Mostrar hubs NSFW"
      content:
        hubs: "{count} hubs"
        modPositions: "{count} posiciones de mod"
        blockedWords: "{count} palabras bloqueadas"
        antiSwearRules: "{count} reglas anti-insultos"
      moderation:
        infractionsReceived: "{count} infracciones recibidas"
        infractionsIssued: "{count} infracciones emitidas"
        blacklistsReceived: "{count} listas negras recibidas"
        blacklistsIssued: "{count} listas negras emitidas"
      activity:
        appeals: "{count} apelaciones"
        reviews: "{count} revisiones"
        reportsMade: "{count} reportes realizados"
        reportsReceived: "{count} reportes recibidos"
        achievements: "{count} logros"
      donation:
        tier: "Nivel: {tier}"
        expires: "Expira"
    blacklist:
      titles:
        list: "Entradas de Lista Negra"
        searchResults: "Resultados de Búsqueda de Lista Negra"
        userList: "Lista Negra de Usuarios"
        serverList: "Lista Negra de Servidores"
      descriptions:
        list: "Entradas totales de lista negra"
        noEntriesPage: "No hay entradas en esta página."
        userListEmpty: "No se encontraron usuarios en lista negra."
        serverListEmpty: "No se encontraron servidores en lista negra."
      fields:
        user: "Usuario"
        server: "Servidor"
        expires: "Expira"
        staff: "Staff"
        added: "Agregado"
      labels:
        user: "Usuario"
        server: "Servidor"
  hub:
    title: 'Creación de Hub'
    description: 'Crea tu propio hub de InterChat para una comunidad, tus amigos... o cualquiera - la lista podría continuar por años. Adelante, mira a dónde te lleva.'
    delete:
      description: 'Tu hub ha sido eliminado.'
    connect:
      success:
        title: '¡Conectado!'
        description: 'Conectado a **{hubName}** - ¡a chatear!'
        fieldValue: 'Canal Conectado: {channel}'
      errors:
        alreadyConnected: '¡Oh oh! Parece que este servidor ya tiene una conexión a este hub.'
    disconnect:
      title: '¡Desconectado!'
      description: 'Desconectado del hub.'
      fieldValue: 'Canal Asociado: {channel}'
    invites:
      title: 'Invitaciones Activas'
      inviteUses: '**Usos:**'
      inviteExpire: '**Expira:**'
      noneFound: '¡No se encontraron!'
    announcements:
      title: 'Anuncios del Hub'
      description: "Crea un anuncio **de una sola vez** que se envíe en todos los canales conectados. \n\n-# Para crear anuncios **recurrentes** por favor visita el menú de configuración del hub."
    creation:
      title: "¡Configuremos tu hub!"
      description: "Los hubs enlazan múltiples servidores usando webhooks. Son geniales para comunidades entre servidores."
      clickToBegin: "Haz clic en {emoji} Crear para comenzar."
      modal:
        title: 'Información del Hub'
        hubName: "Nombre del hub"
        shortLabel: "Descripción corta"
      errors:
        invalidInput: 'Entrada Inválida'
        name: '¡Oh oh! El nombre de tu hub debe estar compuesto por letras, números, espacios y guiones bajos. No permitimos ningún otro carácter.'
        shortDescription: "La descripción corta del hub debe tener entre 10 y 100 caracteres."
        unhandled: '¡Ups! Algo salió mal al intentar crear tu hub. Por favor intenta nuevamente, y si el problema persiste contacta a nuestro (equipo de soporte)[supportServer].'
        unique: '¡Oh oh! Ese nombre de hub ya está tomado. Debes elegir otro.'
      visibility:
        title: 'Visibilidad del Hub'
        description: '¿Debería tu hub ser **público**? Esto significa que será accesible para **todos** y se mostrará en la [página de descubrimiento]({discoverHubs}).'
        buttons:
          public: 'Público'
          private: 'Privado'
      preview:
        title: '¡Completado!'
        description: '¡Has completado la configuración del hub! Revisa tus elecciones a continuación para continuar.'
        footer: 'Puedes editar más campos después, tras la creación.'
        fields:
          title: 'Información'
          value: |
            > {nemoji} **Nombre:** {hubName}
            > {sdemoji} **Descripción corta:** {shortDescription}
            > {vemoji} **Visibilidad:** {visibility}
            > {ldemoji} **Descripción larga:** Ninguna - ¡Configúrala en el [panel]({dashboard}) más tarde!
      complete:
        title: '¡Creado!'
        description: "Tu hub ahora está listo - ¡a chatear! ¿Buscas más personalización? Dirígete al [panel]({dashboard}) y configura los ajustes de tu hub."
        private: '**¡Invita Usuarios!** Como tu hub es privado debes generar invitaciones de usuario. ¡Crea una con `/hub invites`!'
        public: '**¡Tu creación!** Como tu hub es público, puedes verlo en el explorador - ¡[échale un vistazo]({public})!'
      other:
        cancelled: 'Configuración cancelada. Esperamos verte de vuelta aquí pronto.'
    config:
      title: "Configuración del Hub"
      description: "Personaliza tu hub de InterChat para tu comunidad. Elige qué te gustaría configurar a continuación."
      options:
        general:
          label: "Configuración General"
          description: "Personaliza la apariencia, reglas y más de tu hub"
        rules:
          label: "Reglas del Hub"
          description: "Gestiona las reglas y pautas de tu comunidad del hub"
        team:
          label: "Gestión de Equipo"
          description: "Gestiona el equipo y permisos del staff de tu hub"
        modules:
          label: "Módulos"
          description: "Habilita o deshabilita módulos adicionales dentro de tu hub"
        logging:
          label: "Configuración de Registros"
          description: "Configura canales de registro y roles de notificación para tu hub"
        transfer:
          label: "Transferir Propiedad"
          description: "Transfiere la propiedad de tu hub a otra persona"
        announcements:
          label: "Anuncios Programados"
          description: "Configura anuncios automáticos programados para tu hub"
    all:
      title: "Hubs de InterChat"
      description: "¡Abajo podrás buscar a través de una lista de todos nuestros hubs! Échales un vistazo, mira a quién encuentras."
      filteredBy: 'Resultados filtrados por: {filter}'
      errors:
        notFound: "¡Oh oh! No pude encontrar ningún hub. Eso probablemente no debería pasar. Por favor contacta a nuestro [equipo de soporte]({supportServer})."
        invalidFilter: "Grillos... ningún hub coincide con tu filtro."
      modal:
        title: 'Filtro de Búsqueda'
        searchFilter:
          label: 'Filtro'
          placeholder: 'Central de InterChat'
    rules:
      title: "Gestión de Reglas del Hub"
      description: "Gestiona las reglas y pautas de tu comunidad del hub a continuación."
      noRules: "Aún no se han establecido reglas para este hub."
      selectRule:
        placeholder: "Selecciona una regla para editar o eliminar..."
      addRule:
        label: "Agregar Regla"
        modal:
          title: "Agregar Nueva Regla"
          placeholder: "Ingresa tu nueva regla aquí..."
      editRule:
        label: "Editar Regla"
        modal:
          title: "Editar Regla"
          placeholder: "Ingresa tu regla actualizada aquí..."
      deleteRule:
        label: "Eliminar Regla"
        confirmation: "¿Estás seguro de que quieres eliminar esta regla?"
      moveUp:
        label: "Mover Arriba"
      moveDown:
        label: "Mover Abajo"
      viewAll:
        label: "Ver Todas las Reglas"
      validation:
        empty: "La regla no puede estar vacía."
        tooLong: "La regla debe tener {maxLength} caracteres o menos."
        maxRules: "Máximo de {maxRules} reglas permitidas."
      success:
        added: "¡Regla agregada exitosamente!"
        updated: "¡Regla actualizada exitosamente!"
        deleted: "¡Regla eliminada exitosamente!"
        moved: "¡Regla movida exitosamente!"
      errors:
        maxRulesReached: "Máximo de Reglas Alcanzado ({maxRules})"
        noRulesAvailable: "No hay reglas disponibles para seleccionar"
        sessionExpired: "Sesión Expirada"
        sessionExpiredDescription: "Esta sesión de configuración ha expirado. Por favor ejecuta el comando nuevamente para continuar gestionando reglas."
        actionCancelled: "Acción Cancelada"
        actionCancelledDescription: "La acción de regla ha sido cancelada. No se realizaron cambios."
        ruleNotFound: "Selección de regla inválida. La regla puede haber sido eliminada por otro usuario."
        invalidRuleSelection: "Selección de regla inválida. Por favor intenta nuevamente."
        processingError: "Ocurrió un error al procesar tu solicitud. Por favor intenta nuevamente."
        refreshError: "Ocurrió un error al actualizar. Por favor intenta nuevamente."
        navigationError: "Ocurrió un error al navegar. Por favor intenta nuevamente."
        selectionError: "Ocurrió un error al seleccionar la regla. Por favor intenta nuevamente."
        deletionCancelled: "Cancelado"
        deletionCancelledDescription: "La eliminación de la regla ha sido cancelada. No se realizaron cambios."
      warnings:
        deleteWarning: "Advertencia"
        deleteWarningDescription: "Esta acción no se puede deshacer. La regla será eliminada permanentemente de tu hub."
      display:
        noRulesTitle: "No Hay Reglas Establecidas"
        noRulesDescription: "Aún no se han establecido reglas para este hub. Usa el botón \"Agregar Regla\" abajo para crear tu primera regla."
        currentRulesTitle: "Reglas Actuales ({current}/{max})"
        ruleSelected: "Regla {number} Seleccionada"
        currentRule: "Regla Actual:"
  connection:
    fixAll:
      label: "Reparar Todas las Conexiones"
    manage:
      placeholder: "Selecciona una conexión para gestionar"
