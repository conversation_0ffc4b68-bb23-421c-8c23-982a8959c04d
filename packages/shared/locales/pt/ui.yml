ui:
  setup:
    embed:
      description: "Use o modal para inserir os detalhes do seu hub."
    select:
      placeholder: "Selecione uma linguagem"
      loadingLabel: "Carregando..."
      loadingDescription: "Por favor, aguarde..."
      chooseOption: "Selecione uma opção para começar..."
    buttons:
      back: "Voltar"
      cancel: "Cancelar"
      refresh: "Atualizar"
      enterHubInfo: "Insira informações do hub"
      hubInfoComplete: "Informações do Hub completadas"
      createHub: "Criar Hub"
      completeInfoFirst: "Complete as informações do Hub primeiro"
      discoverHubs: "Descubra hubs públicos"
      supportServer: "Servidor de suporte"
  common:
    titles:
      error: "Erro!"
      success: "Sucesso!"
      cancelled: 'Cancelado!'
    noDescription: "Sem descrição"
    labels:
      date: "Data"
      create: 'Criar'
      cancel: 'Cancelar'
    messages:
      loading: "Carregando..."
      pleaseWait: "Por favor, aguarde..."
      notImplemented: "Ainda não implementado."
      hubNotFound: "Hub não encontrado. Por favor, tente novamente mais tarde."
      serverNotFound: "Servidor não encontrado."
      userNotFound: "Usuário não encontrado."
      notSpecified: "Não especificado"
      interfacePermission: "Você não pode usar esta interface."
      hubUpdateFailed: "Falha ao atualizar as configurações do Hub."
      hubConfigDescription: "Personalize seu hub do InterChat para sua comunidade. Escolha o que você gostaria de configurar abaixo."
    pagination:
      previous: "Voltar"
      next: "Próximo"
      page: "Página {current}/{total}"
    errors:
      notYourMenu: "Este não é o seu menu."
      cannotControlPagination: "Você não pode controlar esta paginação."
      notFound: "Não encontrado"
      invalidFilter: "Filtro inválido"
    modal:
      hubCreation:
        title: "Criar Hub"
        name:
          label: "Nome do hub"
          placeholder: "Digite um nome único para o seu hub"
        brief:
          label: "Descrição breve"
          placeholder: "Uma breve descrição do propósito do seu hub."
        description:
          label: "Descrição detalhada"
          placeholder: "Diga às pessoas sobre o que é o seu hub e o que podem esperar"
        logoUrl:
          label: "URL do logo do Hub"
          placeholder: "https://exemplo.com.br/logotipo.png"
    create:
      footer:
        getStarted: "Clique em 'Enter Hub Information' abaixo para começar!"
  help:
    select:
      placeholder: "Selecione uma categoria"
  preferences:
    title: "Preferências do usuário"
    description: "Edite as suas configurações pessoais para que o InterChat funcione para você. Estas são GLOBAIS e trabalham em todos os servidores que você compartilha com o InterChat."
    buttons:
      return: "Retornar"
    replyMentions:
      title: "Preferências: Responder menções"
      description: "Você agora estará **{status}** quando alguém responder para suas mensagens. Você consegue mudar isso abaixo usando o botão, ou retornando ao menu com o botão de Return."
      currentDescription: "Atualmente você está **{status}** quando respondido as suas mensagens. Você pode mudar isso abaixo usando o botão, ou retornando com o botão de Return."
      mentioned: "mencionado"
      notMentioned: "não mencionado"
    errors:
      noPreferences: "Você não tem preferências defenidas ainda."
      hide: "Esconder"
    general:
      title: "Preferências gerais"
      description: "Configure preferências gerais no InterChat. Isso garante que sua experiência é a melhor que conseguimos fazer."
    locale:
      title: "Seleção de Idioma"
      description: "Selecione o idioma de sua preferência para o InterChat."
    select:
      placeholder: "Selecione uma opção"
      category: "Selecione uma categoria"
    badges:
      title: "Preferências: Emblemas"
      description: "Seus emblemas agora são **{status}** dentro das suas mensagens. Você pode alternar isto abaixo usando o botão, ou retornar ao menu com o botão de retorno."
      currentDescription: "Atualmente seus emblemas estão **{status}** dentro de suas mensagens. Você pode alternar isto abaixo usando o botão, ou retornar ao menu com o botão de retorno."
      visible: "visível"
      hidden: "oculto"
      buttons:
        show: "Mostrar"
        hide: "Esconder"
  appeal:
    buttons:
      previous: "Voltar"
      next: "Próximo"
    select:
      placeholder: "Selecione uma infração para apelar"
    errors:
      cannotControl: "Você não pode controlar este menu de paginação."
      notYourMenu: "Isso não é"
      nothingSelected: "Nada foi selecionado."
      invalidSelection: "Seleção inválida."
      modalError: "Falha ao abrir modal do apelo."
    modal:
      title: "Pedir apelo"
      q1: "Por que você acredita que essa ação está incorreta?"
      q2: "Diga a nós sobre a situação"
      q3: "Informação adicional"
    success:
      submitted: "{tick} Seu apelo foi enviado para revisão."
    viewInfractions:
      title: "Infrações recentes do usuário"
      empty: "Sem infrações encontradas para este usuário."
    status:
      acceptedBy: "Aceito por @{name}"
      rejectedBy: "Rejeitado por @{name}"
    actions:
      decisionTitle: "Decisão do apelo"
      reasonOptional:
        label: "Motivo (Opcional)"
        placeholder: "Explique sua decisão..."
  report:
    buttons:
      toStaff: "Reportar para o InterChat"
      toHub: "Reportar para o hub"
    modal:
      toStaff:
        title: "Reportar para a Moderação do Interchat"
      toHub:
        title: "Reportar para Moderadores do Hub"
      reason:
        label: "Motivo para o reporte"
        placeholder: "Por favor, descreva por quê você está reportando essa mensagem..."
    status:
      resolvedBy: "Resolvido por {name}"
      ignoredBy: "Ignorado por {name}"
  hubConfig:
    main:
      placeholder: "Selecione uma opção"
      loadingLabel: "Carregando..."
      loadingDescription: "Por favor, aguarde."
    general:
      placeholder: "Escolha uma configuração para configurar..."
      editDescription:
        label: "Editar descrição"
        description: "Atualize o texto da descrição do seu hub."
      editName:
        label: "Editar nome do Hub"
        description: "Mude o nome do hub. (10 dias de cooldown)"
      welcomeMessage:
        label: "Mensagem de Boas-vindas"
        description: "Defina uma mensagem para novos usuários"
      toggleNsfw:
        label: "Alternar NSFW"
        description: "Marque seu hub como NSFW ou não"
      togglePrivate:
        label: "Alternar como Privado"
        description: "Marque seu hub como privado ou não"
    permissions:
      remove:
        label: "Remover"
      moderator:
        label: "Moderador(a)"
        description: "Gerenciar mensagens, usuários moderados, lidar com relatórios e recursos"
      manager:
        label: "Gerente"
        description: "Gerencia configurações do hub e tudo o que os moderadores podem fazer"
      managerOnly:
        description: "Gerenciar configurações de hub"
    modules:
      reactions:
        description: "Permitir interações com reações em mensagens"
      hideLinks:
        description: "Ocultar pré-visualizações de links nas mensagens do hub"
      spamFilter:
        description: "Automaticamente filtrar conteúdos de spam e indesejados"
      blockInvites:
        description: "Bloquear convites de servidor do Discord em mensagens"
      useNicknames:
        description: "Exibir apelidos do usuário ao invés do nome de usuário"
    logging:
      moderationLogs: "Registros de Moderação"
      joinLeaveLogs: "Entrar/Sair dos Logs"
      appealsChannel: "Canal de Apelos"
      reportsChannel: "Canal de Relatórios"
      networkAlerts: "Alerta da Rede"
      messageModeration: "Contatar moderação"
    invites:
      create:
        title: "Titulo necessária"
        customCode:
          label: "Código customizado"
          placeholder: "abc123"
        uses:
          label: "Usos"
          placeholder: "Deixe-me em branco para infinito"
        expiry:
          label: "Expiração"
          placeholder: "1 semana"
      buttons:
        create: "Criar"
  moderation:
    actionNames:
      warned: "Avisado"
      muted: "Silenciado"
      banned: "Banido"
    actionNouns:
      mute: "silenciar"
      ban: "banir"
    prep:
      in: "em"
      from: "de"
    actions:
      warn:
        label: "Avisar"
        description: "Emite um aviso para o alvo selecionado"
      mute:
        label: "Mute"
        description: "Silencia um usuário/servidor a partir do hub especificado"
      ban:
        label: "Banir"
        description: "Bane um usuário/servidor do hub especificado"
      unmute:
        label: "Desmutar"
        description: "Revogar um silenciamento ativo neste hub"
      unban:
        label: "Desbanir"
        description: "Revogar um banimento ativo neste hub"
      blacklist:
        label: "Blacklist"
        description: "Faz uma blacklist no InterChat (usuário/servidor)"
      delete:
        label: "Apagar"
        description: "Exclui a mensagem selecionada em todos os hubs conectados"
    modal:
      title: "Ação Moderativa"
      reason:
        label: "Motivo"
        placeholder: "Digite uma justificação concisa {optional}"
      duration:
        label: "Duração"
        placeholder: "ex. 30m, 2h, 1d"
    hubSelect:
      placeholder: "Selecione um hub para moderar..."
    hubSelection:
      title: "Seleção do Hub"
      description: "Selecione o hub que você deseja moderar."
      fieldHubLabel: "Hub"
      fieldHubPrompt: "Hub selecionado"
      prompt: "Selecione um hub para moderar"
    targetSelection:
      title: "Seleção de alvo"
      description: "Quem você quer {action}?"
      userField: "Usuário"
      serverField: "Servidor"
    actionSelect:
      placeholder: "Selecione uma ação de moderação..."
    fields:
      reason: "Motivo"
  infractions:
    buttons:
      userInfractions: "Infrações do usuário"
      serverInfractions: "Infrações do servidor"
    titles:
      base: "Infrações · {hubName}"
      userList: "Infrações do usuário · {hubName}"
      serverList: "Infrações do servidor · {hubName}"
      userSpecific: "Infrações de {user} · {hubName}"
      serverSpecific: "Infrações do servidor {serverId} · {hubName}"
    descriptions:
      base: "Selecione quais infrações deseja ver neste hub."
      userListEmpty: "Nenhuma infração de usuário encontrada para este hub."
      serverListEmpty: "Nenhuma infração de servidor encontrada para este hub."
      userSpecificEmpty: "Nenhuma infração encontrada para este usuário neste hub."
      serverSpecificEmpty: "Nenhuma infração encontrada para este servidor neste hub."
    labels:
      infraction: "Infrações"
    fields:
      userName: "Nome de Usuário"
      userId: "ID do usuário"
      serverName: "Nome do servidor"
      serverId: "ID do Servidor"
      reason: "Motivo"
      moderator: "Moderador(a)"
      issued: "Emitido"
      status: "Situação"
  staff:
    hub:
      section:
        status: "Situação"
        moderation: "Moderação"
      fields:
        name: "Nome"
        messages: "Mensagens"
        connections: "Conexões"
        created: "Criado"
        lastActive: "Ultima vez ativo"
        upvotes: "Upvotes"
        owner: "Dono"
        location: "Localização"
        activity: "Atividade"
        appealCooldown: "Cooldown do apelo"
        welcomeMessage: "Mensagem de Boas-vindas"
        description: "Descrição"
      values:
        messagesThisWeek: "**{count}** nesta semana"
        connectionsActive: "**{count}** ativas"
        upvotesTotal: "**{count}** no total"
      badges:
        verified: "Verificado"
        partnered: "Parceiro"
        featured: "Em destaque"
        private: "Privado"
        locked: "Bloqueado"
        nsfw: "NSFW"
    server:
      fields:
        name: "Nome"
        serverId: "ID do Servidor"
        inviteCode: "Código de Convite"
        messages: "Mensagens"
        connections: "Conexões"
        status: "Situação"
        created: "Criado"
        lastMessage: "Última mensagem"
        updated: "Atualizado"
      values:
        messagesTotal: "**{count}** no total"
        connectionsActive: "**{count}** ativos"
        premium: "Premium"
        standard: "Padrão"
    user:
      fields:
        name: "Nome"
        userId: "ID do usuário"
        status: "Situação"
        messages: "Mensagens"
        reputation: "Reputação"
        votes: "Votos"
        hubJoins: "Entradas no hub"
        engagement: "Engajamento"
        locale: "Local"
        created: "Criado"
        lastMessage: "Última mensagem"
        lastVote: "Último Voto"
      values:
        staff: "Moderação"
        member: "Membro"
        messagesSent: "**{count}** enviado"
        reputationPoints: "**{count}** pontos"
        votesCast: "**{count}** transmitidas"
        hubJoinsTotal: "**{count}** no total"
        notSet: "Não definido"
        never: "Nunca"
      sections:
        preferences: "Preferências"
        createdContent: "Conteúdo Criado"
        activity: "Atividade"
        donation: "Doação"
        badges: "Emblemas"
      preferences:
        showBadges: "Mostrar emblemas"
        mentionOnReply: "Mencionar na resposta"
        showNsfwHubs: "Mostrar Hubs NSFW"
      content:
        hubs: "{count} hubs"
        modPositions: "Posições de mods {count}"
        blockedWords: "{count} palavras bloqueadas"
        antiSwearRules: "{COUNT} regras de anti-palavrões"
      moderation:
        infractionsReceived: "{count} infraçoes recebidas"
        infractionsIssued: "{count} Infrações emitidas"
        blacklistsReceived: "{count} blacklists recebidas"
        blacklistsIssued: "{count} blacklists emitidas"
      activity:
        appeals: "{count} apelos"
        reviews: "{count} avaliações"
        reportsMade: "{count} reportagens feitas"
        reportsReceived: "{count} reportagens recebidas"
        achievements: "{count} conquistas"
      donation:
        tier: "Nível: {tier}"
        expires: "Expira em"
    blacklist:
      titles:
        list: "Entradas da blacklist"
        searchResults: "Resultados da pesquisa de Blacklist"
        userList: "Usuários blacklistados"
        serverList: "Servidores blacklistados"
      descriptions:
        list: "Total de entradas na blacklist"
        noEntriesPage: "Não há entradas nesta página."
        userListEmpty: "Nenhum usuário em blacklist encontrado."
        serverListEmpty: "Nenhum servidor em blacklist encontrado."
      fields:
        user: "Usuário"
        server: "Servidor"
        expires: "Expira em"
        staff: "Moderação"
        added: "Adicionado"
      labels:
        user: "Usuário"
        server: "Servidor"
  hub:
    title: 'Criação de Hub'
    description: 'Crie seu próprio hub de InterChat para uma comunidade, seus amigos... ou quem quer que seja - a lista pode continuar por eras. Vá em frente, veja onde você leva.'
    delete:
      description: 'Seu hub foi excluído.'
    connect:
      success:
        title: 'Conectado!'
        description: 'Conectado a **{hubName}** - vamos conversar!'
        fieldValue: 'Canal conectado: {channel}'
      errors:
        alreadyConnected: 'Uh oh! Parece que este servidor já tem uma conexão com este hub.'
    disconnect:
      title: 'Desconectado!'
      description: 'Desconectado do hub.'
      fieldValue: 'Canal associado: {channel}'
    invites:
      title: 'Convites ativos'
      inviteUses: '**Usos:**'
      inviteExpire: '**Expira:**'
      noneFound: 'Nenhum encontrado!'
    announcements:
      title: 'Anúncios do Hub'
      description: "Crie um anúncio **'únicos** que envia em todos os canais conectados. \n\n-# Para criar anúncios **recorrentes** por favor, visite o menu de configuração do Hub."
    creation:
      title: "Vamos configurar o seu Hub!"
      description: "Hubs linham vários servidores juntos usando webhooks. Eles são ótimos para comunidades entre servidores."
      clickToBegin: "Clique em {emoji} Criar para começar."
      modal:
        title: 'Informações do Hub'
        hubName: "Nome do Hub"
        shortLabel: "Descrição curta"
      errors:
        invalidInput: 'Dados inválidos'
        name: 'Uh oh! Seu nome do hub deve ser composto por letras, números, espaços e sublinhados. Não permitimos outros caracteres.'
        shortDescription: "A descrição curta do hub deve ter entre 10 e 100 caracteres."
        unhandled: 'Opa! Algo deu errado ao tentar criar seu hub. Tente novamente e, se o problema persistir, entre em contato com nossa (equipe de suporte)[{supportServer}].'
        unique: 'Uh oh! Esse nome de hub já foi usado. Você deve escolher outro.'
      visibility:
        title: 'Visibilidade do Hub'
        description: 'O seu hub deve ser **público**? Isso significa que estará acessível a **todos** e mostrado na [página de descoberta]({discoverHubs}).'
        buttons:
          public: 'Público'
          private: 'Privado'
      preview:
        title: 'Concluído!'
        description: 'Você completou a configuração do hub! Revise suas escolhas abaixo para continuar.'
        footer: 'Você pode editar mais campos depois, após a criação.'
        fields:
          title: 'Informação'
          value: |
            > {nemoji} **Nome:** {hubName}
            > {sdemoji} **Descrição curta:** {shortDescription}
            > {vemoji} **Visibilidade:** {visibility}
            > {ldemoji} **Descrição longa:** Nenhum - Defina isto na [dashboard]({dashboard}) depois!
      complete:
        title: 'Criado!'
        description: "Agora seu hub está pronto - vamos conversar! Procurando por mais personalização? Vá para [dashboard]({dashboard}) e configure as configurações de seu hub."
        private: '**Convide usuários!** Como o seu hub é privado, você deve gerar convites de usuário. Crie um com `/hub invites`!'
        public: '**Sua criação!** Como seu hub é público, você pode vê-lo no explorador - [confira]({public})!'
      other:
        cancelled: 'Instalação cancelada. Esperamos vê-lo de volta aqui em breve.'
    config:
      title: "Configuração do Hub"
      description: "Personalize seu hub do InterChat para sua comunidade. Escolha o que você gostaria de configurar abaixo."
      options:
        general:
          label: "Configurações Gerais"
          description: "Personalize a aparência do seu hub, regras e mais"
        rules:
          label: "Regras do Hub"
          description: "Gerencie as regras e orientações da comunidade do seu hub."
        team:
          label: "Gerenciamento de Equipes"
          description: "Gerenciar as permissões da equipe e moderação do seu hub."
        modules:
          label: "Módulos"
          description: "Ativar ou desativar módulos adicionais dentro do seu hub"
        logging:
          label: "Configurações de logging"
          description: "Configurar canais de logging e funções de notificação para o seu hub"
        transfer:
          label: "Transferir a posse"
          description: "Transfira a posse do seu hub para outra pessoa"
        announcements:
          label: "Anúncios agendados"
          description: "Configure os anúncios automáticos agendados para o seu Hub"
    all:
      title: "Hubs do InterChat"
      description: "Abaixo você poderá pesquisar através de uma lista de todos os nossos hubs! Confira-os, veja quem você encontra."
      filteredBy: 'Resultados filtrados por: {filter}'
      errors:
        notFound: "Uh oh! Eu não consegui encontrar nenhum hub. Isso provavelmente não deveria acontecer. Entre em contato com nossa [equipe de suporte]({supportServer})."
        invalidFilter: "Cri, cri, cri... nenhum hub corresponde ao seu filtro."
      modal:
        title: 'Filtro de busca:'
        searchFilter:
          label: 'Filtro'
          placeholder: 'Central do InterChat'
    rules:
      title: "Gerenciamento de Regras do Hub"
      description: "Gerencie as regras e diretrizes da comunidade de seu hub."
      noRules: "Nenhuma regra foi definida para este hub por agora."
      selectRule:
        placeholder: "Selecione uma regra para editar ou excluir..."
      addRule:
        label: "Adicionar regra"
        modal:
          title: "Adicionar nova regra "
          placeholder: "Insira sua nova regra aqui..."
      editRule:
        label: "Editar regra"
        modal:
          title: "Editar regra"
          placeholder: "Insira sua regra atualizada aqui..."
      deleteRule:
        label: "Excluir a regra"
        confirmation: "Deseja mesmo excluir essa regra?"
      moveUp:
        label: "Mover para cima"
      moveDown:
        label: "Mover para baixo"
      viewAll:
        label: "Ver todas as regras"
      validation:
        empty: "A regra não pode estar vazia."
        tooLong: "A regra deve ter {maxLength} caracteres ou menos."
        maxRules: "Máximo de regras {maxRules} permitido."
      success:
        added: "Regra adicionada com sucesso!"
        updated: "Regra atualizada com sucesso!"
        deleted: "Regra excluída com sucesso!"
        moved: "Regra movida com sucesso!"
      errors:
        maxRulesReached: "Máximo de regras atingidas ({maxRules})"
        noRulesAvailable: "Não há regras disponíveis para selecionar"
        sessionExpired: "Sessão expirada"
        sessionExpiredDescription: "Esta sessão de configuração expirou. Por favor, execute o comando novamente para continuar a gerenciar as regras."
        actionCancelled: "Ação cancelada"
        actionCancelledDescription: "A ação da regra foi cancelada. Nenhuma alteração foi feita."
        ruleNotFound: "Seleção de regra inválida. A regra pode ter sido excluída por outro usuário."
        invalidRuleSelection: "Seleção de regra inválida. Por favor, tente novamente."
        processingError: "Ocorreu um erro ao acessar os recursos. Por favor, tente novamente."
        refreshError: "Ocorreu um erro ao atualizar. Por favor, tente novamente."
        navigationError: "Ocorreu um erro durante a navegação. Por favor, tente novamente."
        selectionError: "Ocorreu um erro ao selecionar a regra. Por favor, tente novamente."
        deletionCancelled: "Cancelado"
        deletionCancelledDescription: "A exclusão da regra foi cancelada. Nenhuma alteração foi feita."
      warnings:
        deleteWarning: "Atenção"
        deleteWarningDescription: "Esta ação não pode ser desfeita. A regra será removida permanentemente do seu Hub."
      display:
        noRulesTitle: "Sem regras definidas"
        noRulesDescription: "Nenhuma regra foi definida para este hub ainda. Use o botão \"Adicionar Regra\" abaixo para criar sua primeira regra."
        currentRulesTitle: "Regras atuais ({current}/{max})"
        ruleSelected: "Regra {number} selecionada"
        currentRule: "Regra atual:"
  connection:
    fixAll:
      label: "Corrigir todas as conexões"
    manage:
      placeholder: "Selecione uma conexão para gerenciar"
