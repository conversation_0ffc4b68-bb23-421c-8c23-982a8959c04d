from __future__ import annotations

from typing import TYPE_CHECKING, Optional

import discord
from discord.ext import commands
from utils.modules.common.cogs import CogBase
from sqlalchemy import and_, func, select
from sqlalchemy.orm import selectinload

from utils.modules.core.db.models import Infraction
from utils.modules.core.i18n import t
from utils.modules.hub.constants import HubPermissionLevel
from utils.modules.services.permission_service import PermissionService
from utils.modules.services.hubService import HubService
from utils.modules.ui.AutoComplete import hubm_autocomplete

if TYPE_CHECKING:
    from main import Bot


class PaginatorView(discord.ui.View):
    def __init__(
        self, *, bot: Bot, build_embed, per_page: int, total: int, owner_id: int, locale: str
    ):
        super().__init__(timeout=180)
        self.bot = bot
        self.build_embed = build_embed
        self.per_page = per_page
        self.total = total
        self.page = 0
        self.owner_id = owner_id
        self.locale = locale

        # Controls
        self.prev_button = discord.ui.Button(
            emoji=bot.emotes.arrow_left, style=discord.ButtonStyle.secondary
        )
        self.next_button = discord.ui.Button(
            emoji=bot.emotes.arrow_right,
            style=discord.ButtonStyle.secondary,
        )
        self.page_button = discord.ui.Button(
            label=self._page_label(),
            style=discord.ButtonStyle.gray,
            disabled=True,
        )

        self.prev_button.callback = self.on_prev
        self.next_button.callback = self.on_next

        self.add_item(self.prev_button)
        self.add_item(self.page_button)
        self.add_item(self.next_button)
        self._update_buttons()

    def _page_label(self) -> str:
        last_page = max(0, (self.total - 1) // self.per_page)
        return t(
            'ui.common.pagination.page',
            locale=self.locale,
            current=self.page + 1,
            total=(last_page + 1 if self.total else 1),
        )

    def _update_buttons(self):
        last_page = max(0, (self.total - 1) // self.per_page)
        self.prev_button.disabled = self.page <= 0
        self.next_button.disabled = self.page >= last_page
        self.page_button.label = self._page_label()

    async def _edit(self, interaction: discord.Interaction):
        if interaction.user.id != self.owner_id:
            await interaction.response.send_message(
                f'{self.bot.emotes.x_icon} '
                + t('ui.common.errors.cannotControlPagination', locale=self.locale),
                ephemeral=True,
            )
            return

        self._update_buttons()
        embed = await self.build_embed(self.page)
        await interaction.response.edit_message(embed=embed, view=self)

    async def on_prev(self, interaction: discord.Interaction):
        if self.page > 0:
            self.page -= 1
        await self._edit(interaction)

    async def on_next(self, interaction: discord.Interaction):
        last_page = max(0, (self.total - 1) // self.per_page)
        if self.page < last_page:
            self.page += 1
        await self._edit(interaction)


class InfractionsMenuView(discord.ui.View):
    def __init__(
        self,
        *,
        bot: Bot,
        build_user_pages,
        build_server_pages,
        total_users: int,
        total_servers: int,
        requester_id: int,
        locale: str,
    ):
        super().__init__(timeout=180)
        self.bot = bot
        self.build_user_pages = build_user_pages
        self.build_server_pages = build_server_pages
        self.total_users = total_users
        self.total_servers = total_servers
        self.requester_id = requester_id
        self.constants = bot.constants
        self.locale = locale

        self.user_button = discord.ui.Button(
            label=t('ui.infractions.buttons.userInfractions', locale=self.locale),
            style=discord.ButtonStyle.primary,
            emoji=self.bot.emotes.person_icon,
        )
        self.server_button = discord.ui.Button(
            label=t('ui.infractions.buttons.serverInfractions', locale=self.locale),
            style=discord.ButtonStyle.secondary,
            emoji=self.bot.emotes.house_icon,
        )
        self.user_button.callback = self.on_user
        self.server_button.callback = self.on_server
        self.add_item(self.user_button)
        self.add_item(self.server_button)

    async def on_user(self, interaction: discord.Interaction):
        if interaction.user.id != self.requester_id:
            await interaction.response.send_message(
                f'{self.bot.emotes.x_icon} '
                + t('ui.common.errors.notYourMenu', locale=self.locale),
                ephemeral=True,
            )
            return
        paginator = PaginatorView(
            bot=self.bot,
            build_embed=self.build_user_pages,
            per_page=5,
            total=self.total_users,
            owner_id=self.requester_id,
            locale=self.locale,
        )
        embed = await paginator.build_embed(0)
        await interaction.response.edit_message(embed=embed, view=paginator)

    async def on_server(self, interaction: discord.Interaction):
        if interaction.user.id != self.requester_id:
            await interaction.response.send_message(
                f'{self.bot.emotes.x_icon} '
                + t('ui.common.errors.notYourMenu', locale=self.locale),
                ephemeral=True,
            )
            return
        paginator = PaginatorView(
            bot=self.bot,
            build_embed=self.build_server_pages,
            per_page=5,
            total=self.total_servers,
            owner_id=self.requester_id,
            locale=self.locale,
        )
        embed = await paginator.build_embed(0)
        await interaction.response.edit_message(embed=embed, view=paginator)


class Infractions(CogBase):
    def __init__(self, bot: Bot):
        self.bot = bot
        self.constants = bot.constants
        self.locale = 'en'

    def _format_infraction_field(self, inf: Infraction) -> tuple[str, str]:
        type_ = inf.type.value
        reason = inf.reason or t('responses.appeal.constants.noReason', locale=self.locale)
        mod = f'<@{inf.moderatorId}>'
        ts = f'<t:{int(inf.createdAt.timestamp())}:R>'
        status = inf.status.value

        # Build target details with localized labels and safe attribute access
        if inf.userId:
            user_name = getattr(getattr(inf, 'user', None), 'name', None)
            user_name = user_name or t('responses.common.unknown', locale=self.locale)
            target_details = (
                f'> {self.bot.emotes.hash_icon} {t("ui.infractions.fields.userName", locale=self.locale)}: `{user_name}`\n'
                f'> {self.bot.emotes.ID_icon} {t("ui.infractions.fields.userId", locale=self.locale)}: `{inf.userId}`\n'
            )
        else:
            # server
            server_name = getattr(getattr(inf, 'server', None), 'name', None) or inf.serverName
            server_name = server_name or t('responses.common.unknown', locale=self.locale)
            target_details = (
                f'> {self.bot.emotes.hash_icon} {t("ui.infractions.fields.serverName", locale=self.locale)}: `{server_name}`\n'
                f'> {self.bot.emotes.ID_icon} {t("ui.infractions.fields.serverId", locale=self.locale)}: `{inf.serverId}`\n'
            )

        name = f'{type_}'
        value = (
            f'{target_details}'
            f'> {self.bot.emotes.info_icon} {t("ui.infractions.fields.reason", locale=self.locale)}: {reason}\n'
            f'> {self.bot.emotes.person_icon} {t("ui.infractions.fields.moderator", locale=self.locale)}: {mod}\n'
            f'> {self.bot.emotes.calendar_icon} {t("ui.infractions.fields.issued", locale=self.locale)}: {ts}\n'
            f'> {self.bot.emotes.clock_icon} {t("ui.infractions.fields.status", locale=self.locale)}: {status}\n'
        )
        return name, value

    @commands.hybrid_command(
        name='infractions',
        description=t(
            'commands.infractions.description', locale='en'
        ),  # TODO: Dynamically update this
    )
    @hubm_autocomplete
    async def infractions(
        self,
        ctx: commands.Context[Bot],
        hub: str,
        user: Optional[discord.User] = None,
        server: Optional[str] = None,
    ):
        await ctx.defer(ephemeral=False)
        self.locale = await self.get_locale(ctx)

        # Permission check: require Moderator+ in the hub
        async with ctx.bot.db.get_session() as session:
            hub_service = HubService(session)
            hub_model = await hub_service.get_hub_by_name(hub)

            if not hub_model:
                await ctx.reply(
                    f'{self.bot.emotes.x_icon} '
                    + t('ui.common.messages.hubNotFound', locale=self.locale),
                    mention_author=False,
                )
                return

            perm_service = PermissionService(session)
            has_perm, _ = await perm_service.check_permission_from_hub(
                hub_model, str(ctx.author.id), HubPermissionLevel.MODERATOR
            )

            if not has_perm:
                await ctx.reply(
                    f'{self.bot.emotes.x_icon} '
                    + t('responses.infractions.errors.noPermission', locale=self.locale),
                    mention_author=False,
                )
                return

        # Guard against both filters
        if user and server:
            await ctx.reply(
                f'{self.bot.emotes.x_icon} '
                + t('responses.infractions.errors.bothSelection', locale=self.locale),
                mention_author=False,
            )
            return

        per_page = 5

        async with ctx.bot.db.get_session() as session:
            # Counts for menu view
            total_users_stmt = select(func.count(Infraction.id)).where(
                and_(Infraction.hubId == hub_model.id, Infraction.userId.is_not(None))
            )
            total_servers_stmt = select(func.count(Infraction.id)).where(
                and_(Infraction.hubId == hub_model.id, Infraction.serverId.is_not(None))
            )
            total_users = (await session.execute(total_users_stmt)).scalar_one()
            total_servers = (await session.execute(total_servers_stmt)).scalar_one()

            async def build_user_page(page: int):
                stmt = (
                    select(Infraction)
                    .where(and_(Infraction.hubId == hub_model.id, Infraction.userId.is_not(None)))
                    .options(selectinload(Infraction.user), selectinload(Infraction.moderator))
                    .order_by(Infraction.createdAt.desc())
                    .offset(page * per_page)
                    .limit(per_page)
                )
                res = await session.execute(stmt)
                rows = list(res.scalars().all())
                embed = discord.Embed(
                    title=f'{self.bot.emotes.hammer_icon} '
                    + t(
                        'ui.infractions.titles.userList', locale=self.locale, hubName=hub_model.name
                    ),
                    description=' ',
                    color=self.constants.color,
                )
                if not rows:
                    embed.description = t(
                        'ui.infractions.descriptions.userListEmpty', locale=self.locale
                    )
                    return embed
                for inf in rows:
                    name, value = self._format_infraction_field(inf)
                    embed.add_field(name=name, value=value, inline=False)
                return embed

            async def build_server_page(page: int):
                stmt = (
                    select(Infraction)
                    .where(and_(Infraction.hubId == hub_model.id, Infraction.serverId.is_not(None)))
                    .options(selectinload(Infraction.server), selectinload(Infraction.moderator))
                    .order_by(Infraction.createdAt.desc())
                    .offset(page * per_page)
                    .limit(per_page)
                )
                res = await session.execute(stmt)
                rows = list(res.scalars().all())
                embed = discord.Embed(
                    title=f'{self.bot.emotes.hammer_icon} '
                    + t(
                        'ui.infractions.titles.serverList',
                        locale=self.locale,
                        hubName=hub_model.name,
                    ),
                    description=' ',
                    color=self.constants.color,
                )
                if not rows:
                    embed.description = t(
                        'ui.infractions.descriptions.serverListEmpty', locale=self.locale
                    )
                    return embed
                for inf in rows:
                    name, value = self._format_infraction_field(inf)
                    embed.add_field(name=name, value=value, inline=False)
                return embed

            # Specific filters
            if user:
                uid = str(user.id)
                count_stmt = select(func.count(Infraction.id)).where(
                    and_(Infraction.hubId == hub_model.id, Infraction.userId == uid)
                )
                total = (await session.execute(count_stmt)).scalar_one()

                async def build_embed(page: int):
                    stmt = (
                        select(Infraction)
                        .where(and_(Infraction.hubId == hub_model.id, Infraction.userId == uid))
                        .options(selectinload(Infraction.moderator), selectinload(Infraction.user))
                        .order_by(Infraction.createdAt.desc())
                        .offset(page * per_page)
                        .limit(per_page)
                    )
                    res = await session.execute(stmt)
                    rows = list(res.scalars().all())
                    embed = discord.Embed(
                        title=f'{self.bot.emotes.hammer_icon} '
                        + t(
                            'ui.infractions.titles.userSpecific',
                            locale='en',
                            user=str(user),
                            hubName=hub_model.name,
                        ),
                        description=' ',
                        color=self.constants.color,
                    )
                    if not rows:
                        embed.description = t(
                            'ui.infractions.descriptions.userSpecificEmpty', locale=self.locale
                        )
                        return embed
                    for inf in rows:
                        name, value = self._format_infraction_field(inf)
                        embed.add_field(name=name, value=value, inline=False)
                    return embed

                paginator = PaginatorView(
                    bot=self.bot,
                    build_embed=build_embed,
                    per_page=per_page,
                    total=total,
                    owner_id=ctx.author.id,
                    locale=self.locale,
                )
                embed = await paginator.build_embed(0)
                await ctx.reply(embed=embed, view=paginator, mention_author=False)
                return

            if server:
                if not server.isdigit():
                    await ctx.reply(
                        f'{self.bot.emotes.x_icon} '
                        + t('responses.infractions.errors.invalidServerId', locale=self.locale),
                        mention_author=False,
                    )
                    return
                sid = server
                count_stmt = select(func.count(Infraction.id)).where(
                    and_(Infraction.hubId == hub_model.id, Infraction.serverId == sid)
                )
                total = (await session.execute(count_stmt)).scalar_one()

                async def build_embed(page: int):
                    stmt = (
                        select(Infraction)
                        .where(and_(Infraction.hubId == hub_model.id, Infraction.serverId == sid))
                        .options(
                            selectinload(Infraction.moderator), selectinload(Infraction.server)
                        )
                        .order_by(Infraction.createdAt.desc())
                        .offset(page * per_page)
                        .limit(per_page)
                    )
                    res = await session.execute(stmt)
                    rows = list(res.scalars().all())
                    embed = discord.Embed(
                        title=f'{self.bot.emotes.hammer_icon} '
                        + t(
                            'ui.infractions.titles.serverSpecific',
                            locale=self.locale,
                            serverId=sid,
                            hubName=hub_model.name,
                        ),
                        description=' ',
                        color=self.constants.color,
                    )
                    if not rows:
                        embed.description = t(
                            'ui.infractions.descriptions.serverSpecificEmpty', locale=self.locale
                        )
                        return embed
                    for inf in rows:
                        name, value = self._format_infraction_field(inf)
                        embed.add_field(name=name, value=value, inline=False)
                    return embed

                paginator = PaginatorView(
                    bot=self.bot,
                    build_embed=build_embed,
                    per_page=per_page,
                    total=total,
                    owner_id=ctx.author.id,
                    locale=self.locale,
                )
                embed = await paginator.build_embed(0)
                await ctx.reply(embed=embed, view=paginator, mention_author=False)
                return

            # Interactive menu when no filter provided
            menu = InfractionsMenuView(
                bot=self.bot,
                build_user_pages=build_user_page,
                build_server_pages=build_server_page,
                total_users=total_users,
                total_servers=total_servers,
                requester_id=ctx.author.id,
                locale=self.locale,
            )
            base = discord.Embed(
                title=f'{self.bot.emotes.hammer_icon} '
                + t('ui.infractions.titles.base', locale=self.locale, hubName=hub_model.name),
                description=t('ui.infractions.descriptions.base', locale=self.locale),
                color=self.constants.color,
            )
            await ctx.reply(embed=base, view=menu, mention_author=False)


async def setup(bot: Bot):
    await bot.add_cog(Infractions(bot))
