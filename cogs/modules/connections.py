from typing import TYPE_CHECKING

import discord
from discord.ext import commands
from utils.modules.common.cogs import CogBase

from utils.modules.common.database import DatabaseUtils
from utils.modules.ui.views.connectionManagement import ConnectionView
from utils.modules.core.i18n import t
from utils.utils import check_user
from utils.modules.core.webhookCore import fix_connections

if TYPE_CHECKING:
    from main import Bot


class Connections(CogBase):
    def __init__(self, bot):
        self.bot: 'Bot' = bot

    @commands.hybrid_group()
    async def manage(self, ctx: commands.Context['Bot']):
        pass

    @manage.command(
        name='connections',
        description='Manage your connections within this server.',
        extras={'category': 'Connections'},
    )
    @commands.has_permissions(manage_channels=True)
    @commands.guild_only()
    @check_user()
    async def manage_connections(self, ctx: commands.Context['Bot']):
        if not ctx.guild:
            return  # guild_only prevents it from being run elsewhere; no risks

        await ctx.defer(ephemeral=False)
        locale = await self.get_locale(ctx)
        result = await DatabaseUtils.get_server_connections(str(ctx.guild.id))

        if result:
            embed = discord.Embed(
                title=t('commands.connections.title', locale),
                description=t('commands.connections.description', locale),
                color=self.bot.constants.color,
            )
            for con, hub_name in result:
                embed.add_field(
                    name=f'<#{con.channelId}>',
                    value=(
                        f'> **{t("responses.common.hub", locale)}:** {hub_name}\n'
                        f'> **{t("commands.connections.fields.lastActive", locale)}:** <t:{int(con.lastActive.timestamp())}:R>'
                    ),
                    inline=True,
                )

            view = ConnectionView(self.bot, ctx.author, result, locale)
            await ctx.send(embed=embed, view=view)

        else:
            embed = discord.Embed(
                title=t('responses.errors.whoops', locale),
                description=t(
                    'responses.errors.notConnectedServer', locale, cross=self.bot.emotes.x_icon
                ),
                color=discord.Color.red(),
            )
            await ctx.send(embed=embed)

    @commands.hybrid_group()
    @check_user()
    async def fix(self, ctx: commands.Context):
        pass

    @fix.command(
        name='connections', description='Find and attempt to resolve all issues with connections.'
    )
    @commands.has_permissions(manage_channels=True)
    @commands.guild_only()
    @check_user()
    async def fix_connections(self, ctx: commands.Context['Bot']):
        await ctx.defer()
        if not ctx.guild:
            return

        locale = await self.get_locale(ctx)
        fixed_connections, user_error = await fix_connections(self.bot, ctx.guild, locale)
        embed = discord.Embed(
            title=f'{t("commands.connections.fix.title", locale)}',
            description=f'{t("commands.connections.fix.description", locale)}',
            color=discord.Color.green()
            if fixed_connections and not user_error
            else discord.Color.orange(),
        )
        for conn, status in fixed_connections:
            embed.add_field(name=f'<#{conn.channelId}>', value=status, inline=True)
        for conn, status in user_error:
            embed.add_field(name=f'<#{conn.channelId}>', value=status, inline=True)

        await ctx.send(embed=embed, ephemeral=True)


async def setup(bot: 'Bot'):
    await bot.add_cog(Connections(bot))
