from utils.constants import logger
from utils.modules.events.baseEventCog import BaseEventCog
from utils.modules.events.eventDecorator import hub_event_listener
from utils.modules.events.eventDispatcher import (
    HubEvent,
    HubEventType,
)
from utils.modules.hub.appealLogging import build_appeal_embed_and_view
from utils.modules.hub.hubLogging import (
    log_event,
    send_custom_log_to_hub,
    send_staff_log,
)
from utils.modules.hub.reportLogging import build_report_embed, get_report_jump_url
from utils.modules.ui.views.moderation.reportAction import ReportActionView


class HubMessageEvents(BaseEventCog):
    @hub_event_listener(HubEventType.MESSAGE_EDIT)
    async def on_message_edit(self, event: HubEvent):
        """Handle message edit events."""
        await log_event(self.bot, event)

    @hub_event_listener(HubEventType.MESSAGE_DELETE)
    async def on_message_delete(self, event: HubEvent):
        """Handle message deletion events."""
        await log_event(self.bot, event)

    @hub_event_listener(HubEventType.MESSAGE_REPORT)
    async def on_message_report(self, event: HubEvent):
        """Handle message report events."""
        if not event.report_id:
            logger.warning('Received MESSAGE_REPORT event without report_id')
            return
        if not event.target_server_id:
            logger.warning('Received MESSAGE_REPORT event without target_server_id')
            return

        scope = (event.extra_data or {}).get('scope')

        try:
            jump_url = await get_report_jump_url(self.bot, event.report_id, event.target_server_id)
        except Exception as e:
            logger.exception('Error fetching jump URL for report_id=%s: %s', event.report_id, e)
            jump_url = None

        embed = build_report_embed(self.bot, event)
        view = ReportActionView(self.bot, event.report_id, jump_url=jump_url)

        if scope == 'global':
            sent = await send_staff_log(self.bot, embed, view)
            if not sent:
                logger.warning('Failed to send report to staff; no fallback available')
        elif scope == 'hub':
            sent = await send_custom_log_to_hub(
                self.bot, event.hub_id, HubEventType.MESSAGE_REPORT, embed, view
            )
            if not sent:
                logger.warning(
                    f'Failed to send hub report to configured channel for hub {event.hub_id}'
                )

    @hub_event_listener(HubEventType.APPEAL_SUBMITTED)
    async def on_appeal_submitted(self, event: HubEvent):
        """Send appeal submissions to the Appeals log channel with action buttons."""
        embed, view = build_appeal_embed_and_view(self.bot, event)
        sent = await send_custom_log_to_hub(
            self.bot, event.hub_id, HubEventType.APPEAL_SUBMITTED, embed, view
        )
        if not sent:
            logger.warning(f'Failed to send appeal to configured channel for hub {event.hub_id}')

    @hub_event_listener(HubEventType.NSFW_DETECTED)
    async def on_nsfw_detected(self, event: HubEvent):
        """Handle NSFW detection events."""
        await log_event(self.bot, event)

    @hub_event_listener(HubEventType.PROFANITY_VIOLATION)
    async def on_profanity_violation(self, event: HubEvent):
        """Handle profanity violation events."""
        await log_event(self.bot, event)


async def setup(bot):
    await bot.add_cog(HubMessageEvents(bot))
