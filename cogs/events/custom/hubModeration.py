from utils.modules.hub.hubLogging import log_event
from utils.modules.events.baseEventCog import BaseEventCog
from utils.modules.events.eventDispatcher import HubEvent, HubEventType
from utils.modules.events.eventDecorator import hub_event_listener


class HubModEvents(BaseEventCog):
    @hub_event_listener(HubEventType.USER_WARN)
    async def on_user_warn(self, event: HubEvent):
        """Handle user warning events."""
        await log_event(self.bot, event)

    @hub_event_listener(HubEventType.USER_BAN)
    async def on_user_ban(self, event: HubEvent):
        """Handle user ban events."""
        await log_event(self.bot, event)

    @hub_event_listener(HubEventType.USER_UNBAN)
    async def on_user_unban(self, event: HubEvent):
        """Handle user unban events."""
        await log_event(self.bot, event)

    @hub_event_listener(HubEventType.USER_MUTE)
    async def on_user_mute(self, event: Hub<PERSON>vent):
        """Handle user mute events."""
        await log_event(self.bot, event)

    @hub_event_listener(HubEventType.USER_UNMUTE)
    async def on_user_unmute(self, event: HubEvent):
        """Handle user unmute events."""
        await log_event(self.bot, event)

    @hub_event_listener(HubEventType.SERVER_WARN)
    async def on_server_warn(self, event: HubEvent):
        """Handle server warning events."""
        await log_event(self.bot, event)

    @hub_event_listener(HubEventType.SERVER_BAN)
    async def on_server_ban(self, event: HubEvent):
        """Handle server ban events."""
        await log_event(self.bot, event)

    @hub_event_listener(HubEventType.SERVER_UNBAN)
    async def on_server_unban(self, event: HubEvent):
        """Handle server unban events."""
        await log_event(self.bot, event)

    @hub_event_listener(HubEventType.SERVER_MUTE)
    async def on_server_mute(self, event: HubEvent):
        """Handle server mute events."""
        await log_event(self.bot, event)

    @hub_event_listener(HubEventType.SERVER_UNMUTE)
    async def on_server_unmute(self, event: HubEvent):
        """Handle server unmute events."""
        await log_event(self.bot, event)


async def setup(bot):
    await bot.add_cog(HubModEvents(bot))
