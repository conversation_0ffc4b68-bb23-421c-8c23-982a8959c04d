import discord
from discord.ext import commands
from utils.modules.common.cogs import CogBase
from utils.modules.services.reactionService import ReactionService

from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from main import Bot


class OnReaction(CogBase):
    def __init__(self, bot: 'Bot'):
        self.bot = bot
        self.reaction_svc: 'ReactionService'
        self.bot.loop.create_task(self.setup())

    async def setup(self):
        async with self.bot.db.get_session() as session:
            self.reaction_svc = ReactionService(session)

    @commands.Cog.listener()
    async def on_reaction_add(self, reaction: discord.Reaction, user: discord.User):
        message = reaction.message
        emoji_id = (
            str(reaction.emoji) if isinstance(reaction.emoji, str) else str(reaction.emoji.id)
        )

        # This checks if the message is valid as well
        result = await self.reaction_svc.create_reaction_entry(self.bot, message, user, emoji_id)

        if result:
            await self.reaction_svc.update_reacted(self.bot, message, emoji_id, user)
            await self.reaction_svc.update_button_content(self.bot, message, emoji_id)


async def setup(bot):
    await bot.add_cog(OnReaction(bot))
