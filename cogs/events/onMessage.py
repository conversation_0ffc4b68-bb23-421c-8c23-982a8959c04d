import discord
from discord.ext import commands
from utils.modules.common.cogs import CogBase
from typing import TYPE_CHECKING

from utils.constants import logger
from utils.modules.broadcast.service import MessageBroadcastService

if TYPE_CHECKING:
    from main import Bot


class OnMessage(CogBase):
    def __init__(self, bot):
        self.bot: 'Bot' = bot

    @commands.Cog.listener()
    async def on_message(self, message: discord.Message):
        async with self.bot.db.get_session() as session:
            broadcastsvc = MessageBroadcastService(self.bot, session)
            try:
                processed = await broadcastsvc.process_message(message)
                if processed:
                    logger.debug(f'Message {message.id} processing completed successfully')
            except Exception as e:
                logger.error(
                    f'Error processing message {message.id} from user {message.author.id} '
                    f'in channel {message.channel.id}: {e}',
                    exc_info=True,
                )


async def setup(bot):
    await bot.add_cog(OnMessage(bot))
