from typing import TYPE_CHECKING

import discord
from discord.ext import commands
from utils.modules.common.cogs import CogBase

from utils.modules.common.service_utils import retry_with_backoff
from utils.modules.core.messageDelete import delete_interchat_message

if TYPE_CHECKING:
    from main import Bot


class onMessageDelete(CogBase):
    def __init__(self, bot: 'Bot'):
        self.bot = bot

    @commands.Cog.listener()
    async def on_message_delete(self, message: discord.Message):
        if message.author.bot or message.webhook_id:
            return

        @retry_with_backoff(retries=3, base_delay=1.0)
        async def _delete_with_retry():
            await delete_interchat_message(bot=self.bot, message_id=str(message.id))

        await _delete_with_retry()


async def setup(bot: 'Bot'):
    await bot.add_cog(onMessageDelete(bot))
