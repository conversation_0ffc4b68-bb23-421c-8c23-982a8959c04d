import asyncio
import discord
from discord.ext import commands
from utils.modules.common.cogs import CogBase
from sqlalchemy import select, update
from typing import TYPE_CHECKING

from utils.modules.core.db.models import Broadcast, Connection, Message
from utils.modules.core.cache import webhook_cache
from utils.constants import logger

if TYPE_CHECKING:
    from main import Bot


class onMessageEdit(CogBase):
    def __init__(self, bot: 'Bot'):
        self.bot = bot
        # (channel_id, webhook_message_id, new_content, is_thread)
        self._edit_queue: asyncio.Queue[tuple[str, int, str, bool]] = asyncio.Queue()
        self._worker_task = bot.loop.create_task(self._edit_worker())
        self.webhook_cache = webhook_cache

    async def cog_unload(self):
        self._worker_task.cancel()

    @commands.Cog.listener()
    async def on_message_edit(self, before: discord.Message, after: discord.Message):
        if before.author.bot or before.webhook_id or before.content == after.content:
            return

        async with self.bot.db.get_session() as session:
            result = await session.execute(
                select(Broadcast.channelId, Broadcast.id, Connection.parentId)
                .join(Connection, Connection.channelId == Broadcast.channelId)
                .where(Broadcast.messageId == str(before.id))
            )
            records = result.all()

            if not records:
                return

            # Queue all records at once
            for rec in records:
                is_thread = bool(rec.parentId)
                self._edit_queue.put_nowait((rec.channelId, int(rec.id), after.content, is_thread))

            # Update database record
            stmt = update(Message).where(Message.id == str(before.id)).values(content=after.content)

            await session.execute(stmt)
            await session.commit()
            logger.debug(f'Queued and updated {len(records)} broadcast record(s)')

    async def _get_webhook(self, channel_id: str) -> discord.Webhook:
        """Get webhook from Redis cache or database."""
        # Check Redis cache first
        webhook_url = await self.webhook_cache.get_webhook_url(channel_id)

        if not webhook_url:
            # Fetch from database if not cached
            async with self.bot.db.get_session() as session:
                webhook_url = await session.scalar(
                    select(Connection.webhookURL).where(Connection.channelId == channel_id)
                )

            if webhook_url:
                # Cache the webhook URL
                await self.webhook_cache.set_webhook_url(channel_id, webhook_url)

        if webhook_url:
            return discord.Webhook.from_url(webhook_url, session=self.bot.http_session)

        raise RuntimeError(f'Webhook not found for channel {channel_id}')

    async def _edit_worker(self):
        while True:
            try:
                channel_id, webhook_msg_id, new_content, is_thread = await self._edit_queue.get()
                try:
                    webhook = await self._get_webhook(channel_id)
                    if not webhook:
                        logger.warning(f'No webhook found for channel {channel_id}')
                    else:
                        if is_thread:
                            await webhook.edit_message(
                                webhook_msg_id,
                                content=new_content,
                                thread=discord.Object(id=int(channel_id)),
                            )
                        else:
                            await webhook.edit_message(webhook_msg_id, content=new_content)
                        logger.debug(f'Edited webhook msg {webhook_msg_id} in channel {channel_id}')
                except discord.NotFound:
                    logger.warning(f'Message {webhook_msg_id} not found in channel {channel_id}')
                    await self.webhook_cache.clear_webhook_url(channel_id)
                except Exception as e:
                    logger.exception(f'Error editing webhook msg {webhook_msg_id}: {e}')
                finally:
                    self._edit_queue.task_done()

            except asyncio.CancelledError:
                break


async def setup(bot: 'Bot'):
    await bot.add_cog(onMessageEdit(bot))
