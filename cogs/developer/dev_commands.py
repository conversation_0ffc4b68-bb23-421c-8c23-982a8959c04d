from typing import TYPE_CHECKING
import discord
from discord.ext import commands
from utils.modules.common.cogs import CogBase

from sqlalchemy.orm.attributes import flag_modified

from utils.modules.ui.views.devAnnounce import DeveloperAnnouncement
from utils.modules.core.db.models import Badges
from utils.utils import upsert_user

if TYPE_CHECKING:
    from main import Bot


class Developer(CogBase):
    def __init__(self, bot: 'Bot'):
        self.bot = bot

    @commands.hybrid_command(name='dev-announce', description='A developer only command.')
    @commands.is_owner()
    async def announce(self, ctx: commands.Context[commands.Bot]):
        modal = DeveloperAnnouncement(self.bot)
        if not ctx.interaction:
            return
        await ctx.interaction.response.send_modal(modal)

    @commands.command()
    @commands.is_owner()
    async def sync_staff(self, ctx: commands.Context[commands.Bot]):
        await self.bot.sync_staff_ids()
        await ctx.send(
            f'{self.bot.emotes.tick} Staff IDs synced! {len(self.bot.staff_ids)} staff IDs loaded.'
        )

    @commands.group()
    async def badge(self, ctx: commands.Context[commands.Bot]): ...

    @badge.command()
    @commands.is_owner()
    async def edit(
        self,
        ctx: commands.Context[commands.Bot],
        action: str,
        badge: str,
        user: discord.User,
    ):
        valid_actions = ['add', 'remove']
        valid_badges = ['TRANSLATOR', 'SUPPORTER', 'BETA_TESTER']

        if action.lower() not in valid_actions:
            return

        if badge.upper() not in valid_badges:
            return

        async with self.bot.db.get_session() as session:
            db_user = await upsert_user(user, session)

            current_badges = db_user.badges or []

            if action == 'add' and badge.upper() not in current_badges:
                current_badges.append(Badges[badge.upper()])
                db_user.badges = current_badges
                flag_modified(db_user, 'badges')

            elif action == 'remove' and badge.upper() in current_badges:
                current_badges.remove(Badges[badge.upper()])
                db_user.badges = current_badges
                flag_modified(db_user, 'badges')

            await session.commit()
            await ctx.send('User badges updated!')


async def setup(bot):
    await bot.add_cog(Developer(bot))
