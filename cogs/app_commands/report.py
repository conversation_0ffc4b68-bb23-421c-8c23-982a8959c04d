from datetime import datetime, timed<PERSON><PERSON>
from typing import TYPE_CHECKING

import discord
from discord.ui import TextInput, View, button
from sqlalchemy import select

from main import Bot
from utils.modules.common.cogs import CogBase, context_menu
from utils.modules.core.db.models import Hub, Message, User
from utils.modules.core.db.models import <PERSON><PERSON><PERSON><PERSON>ort as DbReport
from utils.modules.core.i18n import t
from utils.modules.core.moderation import fetch_original_msg_with_extra
from utils.modules.events.eventDispatcher import (
    HubEventType,
    create_hub_event,
    event_dispatcher,
)
from utils.modules.ui.CustomModal import CustomModal
from utils.modules.common.embeds import CommonErrors

if TYPE_CHECKING:
    from main import Bot


class Report(CogBase):
    @context_menu('Report Message')
    async def report_message(self, interaction: discord.Interaction, message: discord.Message):
        try:
            await interaction.response.defer(ephemeral=True)

            async with self.bot.db.get_session() as session:
                result = await fetch_original_msg_with_extra(session, str(message.id))
                user_locale = await self.get_locale(interaction)

            if not result:
                embed = CommonErrors.hub_message_only(self.bot, user_locale)
                return await interaction.followup.send(embed=embed, ephemeral=True)

            db_message, reported_user, hub_id = result

            if int(reported_user.id) == interaction.user.id:
                embed = CommonErrors.cannot_report_self(self.bot, user_locale)
                return await interaction.followup.send(embed=embed, ephemeral=True)

            embed = discord.Embed(
                title=t('commands.report.title', locale=user_locale),
                description=t(
                    'commands.report.description',
                    locale=user_locale,
                    user=f'<@{int(reported_user.id)}>',
                ),
                color=self.constants.color,
            )
            embed.set_author(
                name=f'@{interaction.user}', icon_url=interaction.user.display_avatar.url
            )
            embed.set_footer(text=t('commands.report.footer', locale=user_locale))

            view = ScopeView(
                self.bot, interaction.user, db_message, reported_user, hub_id, message, user_locale
            )
            await interaction.followup.send(embed=embed, view=view, ephemeral=True)
        except Exception as e:
            raise Exception('Error in report_message') from e


async def setup(bot: 'Bot'):
    await bot.add_cog(Report(bot))


class ScopeView(View):
    def __init__(
        self,
        bot: 'Bot',
        reporter: discord.User | discord.Member,
        db_message: Message,
        reported_user: User,
        hub_id: str,
        discord_message: discord.Message,
        user_locale: str,
    ):
        super().__init__(timeout=120)
        self.bot = bot
        self.reporter = reporter
        self.constants = bot.constants
        self.db_message = db_message
        self.reported_user = reported_user
        self.hub_id = hub_id
        self.discord_message = discord_message
        self.user_locale = user_locale
        self.setup_buttons()

    def setup_buttons(self):
        self.global_callback.label = t('ui.report.buttons.toStaff', locale=self.user_locale)
        self.global_callback.emoji = self.bot.emotes.globe_icon
        self.global_callback.disabled = False

        self.hub_callback.label = t('ui.report.buttons.toHub', locale=self.user_locale)
        self.hub_callback.emoji = self.bot.emotes.house_icon
        self.hub_callback.disabled = False

    async def _fetch_messages(self):
        async with self.bot.db.get_session() as session:
            ten_minutes_ago = datetime.now() - timedelta(minutes=10)

            stmt = (
                select(Message)
                .where(
                    Message.channelId == str(self.db_message.channelId),
                    Message.createdAt >= ten_minutes_ago,
                )
                .order_by(Message.createdAt.desc())
            )
            result = await session.execute(stmt)
            messages = result.scalars().all()

            if not messages:
                stmt = (
                    select(Message)
                    .where(Message.channelId == str(self.db_message.channelId))
                    .order_by(Message.createdAt.desc())
                    .limit(50)
                )
                result = await session.execute(stmt)
                messages = result.scalars().all()

            return messages

    async def _open_reason_modal(self, interaction: discord.Interaction, title: str) -> str | None:
        modal = CustomModal(
            title,
            [
                (
                    'reason',
                    TextInput(
                        label=t('ui.report.modal.reason.label', locale=self.user_locale),
                        placeholder=t(
                            'ui.report.modal.reason.placeholder', locale=self.user_locale
                        ),
                        required=True,
                        max_length=500,
                        style=discord.TextStyle.paragraph,
                    ),
                )
            ],
        )
        await interaction.response.send_modal(modal)
        if not await modal.wait():
            return modal.saved_items['reason'].value.strip()
        return None

    async def _dispatch_report_event(
        self,
        scope: str,
        reason: str,
    ):
        async with self.bot.db.get_session() as session:
            original_content = self.db_message.content

            # Get hub name
            hub_rec = await session.get(Hub, self.hub_id)
            hub_name = hub_rec.name if hub_rec else 'Unknown Hub'

            # Create the report
            report = DbReport(
                hubId=str(self.hub_id),
                reporterId=str(self.reporter.id),
                reportedServerId=str(self.discord_message.guild.id)
                if self.discord_message.guild
                else '',
                reportedUserId=self.reported_user.id,
                messageId=self.db_message.id,
                reason=reason,
            )
            session.add(report)
            await session.commit()

        # Prepare attachments
        attachment_meta = [
            {
                'url': a.url,
                'filename': a.filename,
                'content_type': a.content_type,
                'size': a.size,
            }
            for a in getattr(self.discord_message, 'attachments', [])
        ]

        target_guild_id = str(self.discord_message.guild.id) if self.discord_message.guild else ''
        target_guild_name = (
            self.discord_message.guild.name if self.discord_message.guild else 'Direct Message'
        )

        event = create_hub_event(
            event_type=HubEventType.MESSAGE_REPORT,
            hub_id=str(self.hub_id),
            hub_name=str(hub_name),
            moderator_id=str(self.reporter.id),
            moderator_name=str(self.reporter),
            target_user_id=str(self.reported_user.id),
            target_user_name=str(self.reported_user.name or 'Unknown User'),
            target_server_id=target_guild_id,
            target_server_name=target_guild_name,
            message_id=str(self.db_message.id),
            channel_id=str(self.discord_message.channel.id),
            original_content=original_content,
            report_id=report.id,
            reason=reason,
            extra_data={'scope': scope, 'attachments': attachment_meta},
        )
        await event_dispatcher.dispatch_hub_event(event)

    @button(label='Report to Hub', style=discord.ButtonStyle.primary, disabled=False)
    async def hub_callback(self, interaction: discord.Interaction, _button: discord.ui.Button):
        reason = await self._open_reason_modal(
            interaction, t('ui.report.modal.toHub.title', locale=self.user_locale)
        )
        if not reason:
            return

        await self._dispatch_report_event(scope='hub', reason=reason)
        embed = discord.Embed(
            title=t('commands.report.success.title', locale=self.user_locale),
            description=t(
                'commands.report.success.toHub', locale=self.user_locale, tick=self.bot.emotes.tick
            ),
            color=self.constants.color,
        )
        await interaction.followup.send(embed=embed, ephemeral=True)

    @button(label='Report to Staff', style=discord.ButtonStyle.grey, disabled=False)
    async def global_callback(self, interaction: discord.Interaction, _button: discord.ui.Button):
        reason = await self._open_reason_modal(
            interaction, t('ui.report.modal.toStaff.title', locale=self.user_locale)
        )
        if not reason:
            return

        await self._dispatch_report_event(scope='global', reason=reason)
        embed = discord.Embed(
            title=t('commands.report.success.title', locale=self.user_locale),
            description=t(
                'commands.report.success.toStaff',
                locale=self.user_locale,
                tick=self.bot.emotes.tick,
            ),
            color=self.constants.color,
        )
        await interaction.followup.send(embed=embed, ephemeral=True)
