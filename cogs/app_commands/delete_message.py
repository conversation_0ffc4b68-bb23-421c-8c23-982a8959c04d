from typing import TYPE_CHECKING

import discord

from utils.modules.common.cogs import CogBase, context_menu
from utils.modules.core.messageDelete import MessageDeletionError, MessageDeletionService

if TYPE_CHECKING:
    from main import Bot


class DeleteMessage(CogBase):
    @context_menu('Delete Message')
    async def delete_message_context(
        self, interaction: discord.Interaction['Bot'], message: discord.Message
    ):
        """Delete an InterChat message via context menu."""
        locale = await self.get_locale(interaction)
        deletion_service = MessageDeletionService(self.bot)

        # Defer the response since deletion might take time
        await interaction.response.defer(ephemeral=True)

        try:
            success = await deletion_service.delete_message_from_context(
                ctx=interaction,
                message=message,
                locale=locale,
            )

            if success:
                await deletion_service.send_success_message(
                    ctx=interaction,
                    message_id=str(message.id),
                    locale=locale,
                )

        except MessageDeletionError as e:
            await deletion_service.send_error_message(
                ctx=interaction,
                error=str(e),
                locale=locale,
            )


async def setup(bot):
    """Load the DeleteMessage cog."""
    await bot.add_cog(DeleteMessage(bot))
