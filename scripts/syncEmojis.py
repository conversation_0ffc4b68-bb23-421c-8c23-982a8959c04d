import argparse
import asyncio
import json
import os

import aiohttp
import discord
from dotenv import load_dotenv
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, BarColumn, TextColumn
from rich.table import Table
from scripts.generateEmojiStubs import generate_emoji_manager_stub

console = Console()


# --- Utility Functions ---
def print_intro():
    console.rule('[bold magenta]Discord Emoji Synchronizer[/bold magenta]', style='magenta')
    console.print()


def print_summary(actions: dict):
    table = Table(title='[bold]Synchronization Summary[/bold]')
    table.add_column('Action', style='cyan', justify='right')
    table.add_column('Count', style='bold green', justify='left')

    for action, count in actions.items():
        table.add_row(action, str(count))

    console.print(table)
    console.print()


# --- Main ---
load_dotenv()
TOKEN = os.getenv('DISCORD_TOKEN')

intents = discord.Intents.none()
bot = discord.Client(intents=intents)


async def sync_all(force: bool, dry_run: bool, unsafe: bool):
    # Load emoji links from JSON file
    emoji_links_path = os.path.join(
        os.path.dirname(os.path.dirname(__file__)), 'data', 'emojiLinks.json'
    )
    with open(emoji_links_path, 'r') as f:
        emoji_links = json.load(f)

    # Fetch existing emojis
    console.print('[cyan]›[/cyan] [bold]Fetching existing application emojis...[/bold]')
    existing = await bot.fetch_application_emojis()
    existing_map = {e.name: e for e in existing}
    console.print(f'[green]✓[/green] Found {len(existing_map)} existing emojis.')

    to_process = list(emoji_links.items())
    actions_summary = {'Created': 0, 'Skipped': 0, 'Deleted': 0, 'Errors': 0, 'Updated URLs': 0}

    # Track URLs that need updating and errors for detailed reporting
    updated_urls = {}
    errors = []

    async with aiohttp.ClientSession() as session:
        with Progress(
            SpinnerColumn(),
            TextColumn('[progress.description]{task.description}'),
            BarColumn(),
            TextColumn('[progress.percentage]{task.percentage:>3.0f}%'),
            console=console,
        ) as progress:
            task = progress.add_task('[cyan]Syncing emojis...', total=len(to_process))

            for name, url in to_process:
                progress.update(task, advance=1)

                if not force and name in existing_map:
                    # Even if we're skipping creation, update the URL with current Discord URL
                    existing_emoji = existing_map[name]
                    updated_urls[name] = existing_emoji.url
                    actions_summary['Skipped'] += 1
                    continue

                if name in existing_map:
                    if not dry_run:
                        await existing_map[name].delete()
                    actions_summary['Deleted'] += 1

                async with session.get(url) as resp:
                    if resp.status != 200:
                        error_msg = f'Failed to download {name} - HTTP {resp.status}'
                        errors.append(error_msg)
                        actions_summary['Errors'] += 1
                        continue
                    img = await resp.read()

                if dry_run:
                    actions_summary['Created'] += 1
                    continue

                try:
                    emoji = await bot.create_application_emoji(name=name, image=img)
                    new_url = emoji.url
                    updated_urls[name] = new_url
                    actions_summary['Created'] += 1
                except Exception as e:
                    error_msg = f'Failed to create {name}: {str(e)}'
                    errors.append(error_msg)
                    actions_summary['Errors'] += 1

        if unsafe:
            console.print(
                '\n[yellow]›[/yellow] [bold]Running in unsafe mode: checking for emojis to delete...[/bold]'
            )
            for name, emoji_obj in existing_map.items():
                if name not in dict(to_process):
                    console.print(f'[red]  - Deleting [bold]{name}[/bold] (not in source)[/red]')
                    if not dry_run:
                        await emoji_obj.delete()
                    actions_summary['Deleted'] += 1

    # Report any errors that occurred
    if errors:
        console.print()
        console.print('[red]›[/red] [bold]Errors encountered:[/bold]')
        for error in errors:
            console.print(f'[red]  ✗ {error}[/red]')

    console.print()

    # Update the JSON file with Discord emoji URLs (both existing and newly created)
    if updated_urls and not dry_run:
        # Update the emoji_links dictionary with current URLs
        for name, new_url in updated_urls.items():
            emoji_links[name] = new_url
            actions_summary['Updated URLs'] += 1

        # Save the updated JSON file
        with open(emoji_links_path, 'w', encoding='utf-8') as f:
            json.dump(emoji_links, f, indent=2, ensure_ascii=False)

        console.print(f'[green]✓[/green] Updated {len(updated_urls)} URLs in emojiLinks.json')
    elif updated_urls and dry_run:
        console.print(
            f'[cyan]›[/cyan] Would update {len(updated_urls)} URLs in emojiLinks.json (dry run)'
        )
        actions_summary['Updated URLs'] = len(updated_urls)

    return actions_summary


async def main():
    if not TOKEN:
        raise ValueError('No token found. Please set the DISCORD_TOKEN environment variable.')

    print_intro()
    await bot.login(TOKEN)

    parser = argparse.ArgumentParser()
    parser.add_argument(
        '--force', action='store_true', help='Delete and recreate all emojis even if they exist.'
    )
    parser.add_argument('--dry-run', action='store_true', help='Do not make any actual changes.')
    parser.add_argument('--unsafe', action='store_true', help='Allow deleting emojis during sync.')
    args = parser.parse_args()

    actions_summary = await sync_all(force=args.force, dry_run=args.dry_run, unsafe=args.unsafe)

    # Generate emoji manager stub
    generate_emoji_manager_stub()
    print_summary(actions_summary)

    await bot.close()
    console.rule('[bold green]Synchronization Complete[/bold green]', style='green')


if __name__ == '__main__':
    asyncio.run(main())
