import asyncio
import os
import yaml
from typing import Dict, Any, Set

import aiofiles
from rich.console import Console
from rich.panel import Panel
from rich.text import Text

console = Console()


def load_yaml_files(locales_path: str = 'locales/en') -> Dict[str, Any]:
    """Load all YAML files from the locales directory."""
    aggregated = {}
    if not os.path.isdir(locales_path):
        raise FileNotFoundError(f'Locales path not found: {locales_path}')

    for file_name in os.listdir(locales_path):
        file_path = os.path.join(locales_path, file_name)
        if os.path.isfile(file_path) and file_name.endswith(('.yaml', '.yml')):
            with open(file_path, 'r', encoding='utf-8') as file:
                file_content = yaml.safe_load(file) or {}
                for section, content in file_content.items():
                    if section not in aggregated:
                        aggregated[section] = {}
                    if isinstance(content, dict):
                        merge_dict(aggregated[section], content)
                    else:
                        aggregated[section] = content
    return aggregated


def merge_dict(base: Dict[str, Any], other: Dict[str, Any]) -> Dict[str, Any]:
    """Recursively merge two dictionaries."""
    for k, v in (other or {}).items():
        if isinstance(v, dict) and isinstance(base.get(k), dict):
            base[k] = merge_dict(base[k], v)
        else:
            base[k] = v
    return base


def extract_keys(data: Dict[str, Any], prefix: str = '') -> Set[str]:
    """Extract all dot-notation keys from nested dictionary."""
    keys = set()
    for key, value in data.items():
        current_key = f'{prefix}.{key}' if prefix else key
        if isinstance(value, dict):
            keys.update(extract_keys(value, current_key))
        else:
            keys.add(current_key)
    return keys


# --- Main  ---
async def generate_locale_stubs():
    """Generate type stubs for the t() function with all locale keys."""
    console.rule('[bold magenta]Locale Stub Generator[/bold magenta]', style='magenta')
    console.print()

    try:
        with console.status(
            '[bold cyan]Loading and processing YAML locale files...[/bold cyan]', spinner='dots'
        ):
            locale_data = load_yaml_files()
            all_keys = extract_keys(locale_data)
            sorted_keys = sorted(all_keys)

        console.print(
            f'[green]✅[/green] Found [bold green]{len(sorted_keys)}[/bold green] unique locale keys.'
        )

        output_path = 'utils/modules/core/i18n.pyi'

        with console.status(
            f'[bold cyan]Generating type stubs in [underline]{output_path}[/underline]...[/bold cyan]',
            spinner='dots',
        ):
            async with aiofiles.open(output_path, 'w', encoding='utf-8') as f:
                await f.write('"""Auto-generated locale key stubs for type checking.\n\n')
                await f.write('This file provides type hints for all available locale keys\n')
                await f.write('that can be used with the t() function.\n')
                await f.write('"""\n\n')
                await f.write('from typing import Literal\n\n')
                await f.write('# All available locale keys\n')
                await f.write('LocaleKey = Literal[\n')

                for i, key in enumerate(sorted_keys):
                    comma = ',' if i < len(sorted_keys) - 1 else ''
                    await f.write(f"    '{key}'{comma}\n")

                await f.write(']\n\n')
                await f.write('def t(key: LocaleKey, locale: str = "en", **kwargs) -> str: ...\n\n')

        # Create a summary panel
        summary_text = Text.assemble(
            ('Status: ', 'white'),
            ('Success\n', 'bold green'),
            ('Keys Processed: ', 'white'),
            (str(len(sorted_keys)), 'bold yellow'),
            ('\nOutput File: ', 'white'),
            (output_path, 'underline cyan'),
        )
        summary_panel = Panel(
            summary_text,
            title='[bold]Generation Complete[/bold]',
            border_style='green',
            expand=False,
        )
        console.print(summary_panel)

    except FileNotFoundError as e:
        console.print(f'[red]❌ Error: {e}[/red]')
        console.print(
            "[yellow]Please ensure the 'locales/en' directory exists and contains YAML files.[/yellow]"
        )
    except Exception:
        console.print_exception(show_locals=True)
        console.rule('[bold red]An unexpected error occurred[/bold red]', style='red')


async def main():
    await generate_locale_stubs()


if __name__ == '__main__':
    asyncio.run(main())
