import json
import os
from rich.console import Console

console = Console()


def generate_emoji_manager_stub(json_path: str = 'data/emojiLinks.json'):
    try:
        # Check for and load the JSON file
        if not os.path.exists(json_path):
            console.print(
                f'[bold red]✗ Error:[/bold red] JSON file not found at [underline]{json_path}[/underline]'
            )
            return

        with console.status(f'[cyan]Loading emoji data from [bold]{json_path}[/bold]...[/cyan]'):
            with open(json_path, 'r', encoding='utf-8') as f:
                emoji_data = json.load(f)

        emoji_names = sorted(emoji_data.keys())
        console.print(
            f'[green]✓[/green] Loaded [bold yellow]{len(emoji_names)}[/bold yellow] emoji definitions for stub generation.'
        )
        # Generate the stub content
        with console.status('[cyan]Generating stub file content...[/cyan]'):
            stub_parts = [
                """
import discord
from typing import Optional

class EmojiManager:
    def __init__(self) -> None: ...
    async def load(self, bot: discord.Client) -> None: ...
    def get(self, name: Optional[str]) -> Optional[discord.Emoji]: ...

    # Auto-generated emoji attributes from emojiLinks.json
"""
            ]
            for name in emoji_names:
                stub_parts.append(f'    {name}: discord.Emoji')

            stub_content = '\n'.join(stub_parts)

        # Write the content to the output file
        output_path = 'utils/modules/emojis/EmojiManager.pyi'
        with console.status(f'[cyan]Writing stub to [bold]{output_path}[/bold]...[/cyan]'):
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(stub_content)

        console.print('[green]✓[/green] Stub file generated successfully. Output path:')
        console.print(f'  [bold]{output_path}[/bold]')
        console.print()

    except json.JSONDecodeError:
        console.print(
            f'[bold red]✗ Error:[/bold red] Failed to decode JSON from [underline]{json_path}[/underline]. Please check for syntax errors.'
        )
    except Exception:
        console.print_exception(show_locals=False)


if __name__ == '__main__':
    generate_emoji_manager_stub()
