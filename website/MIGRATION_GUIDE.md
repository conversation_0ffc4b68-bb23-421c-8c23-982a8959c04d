# Migration Guide: Next-Auth → Auth.js + FastAPI Integration

## 🎯 **What Changed**

We've migrated from a traditional Next-Auth setup to a **hybrid approach** where:

- **Auth.js (Next-Auth v5)** handles OAuth only (no database)
- **FastAPI backend** handles all user data and database operations
- **JWT tokens** from FastAPI are used for API authentication

## 🔄 **Architecture Overview**

### **Before (Traditional Next-Auth)**

```
Next.js ← → Prisma Database
   ↓
Discord OAuth
```

### **After (Hybrid Auth.js + FastAPI)**

```
Next.js (Auth.js) ← → Discord OAuth
   ↓ (JWT Token)
FastAPI Backend ← → Database
```

## 🚀 **How It Works**

1. **User clicks "Login with Discord"** in Next.js
2. **Auth.js redirects** to Discord OAuth
3. **Discord returns** with access token
4. **Auth.js exchanges** Discord token for FastAPI JWT
5. **FastAPI creates/updates** user in database
6. **JWT token stored** in Auth.js session
7. **Frontend makes API calls** using FastAPI JWT

## 📝 **Key Files Changed**

### **Frontend (Next.js)**

- `src/auth.ts` - Simplified to OAuth-only, no database adapter
- `src/hooks/useAuth.ts` - New hook for FastAPI integration
- `src/components/auth-example.tsx` - Example component
- `.env.example` - Updated environment variables

### **Backend (FastAPI)**

- `packages/api/src/routers/auth.py` - Removed sync-user endpoint
- `packages/api/src/core/auth.py` - Already handles user creation properly

## 🔧 **Environment Variables**

### **Next.js (.env.local)**

```bash
# Auth.js Configuration
AUTH_SECRET="your-auth-secret-key"
NEXTAUTH_URL="http://localhost:3000"

# Discord OAuth2 (same as FastAPI)
NEXT_PUBLIC_DISCORD_CLIENT_ID="your_discord_client_id"
DISCORD_CLIENT_SECRET="your_discord_client_secret"

# FastAPI Backend URL
NEXT_PUBLIC_API_URL="http://localhost:8000"

# Database (if you still need Prisma for other features)
DATABASE_URL="postgresql://username:password@localhost:5432/interchat"
```

### **FastAPI (.env)**

```bash
# Same Discord OAuth2 credentials
DISCORD_CLIENT_ID="your_discord_client_id"
DISCORD_CLIENT_SECRET="your_discord_client_secret"
DISCORD_REDIRECT_URI="http://localhost:8000/auth/discord/callback"

# JWT Secret
SECRET_KEY="your-jwt-secret-key"
```

## 💻 **Usage Examples**

### **Using the new auth system:**

```tsx
import { useAuth } from "@/hooks/useAuth";

function MyComponent() {
    const { user, isAuthenticated, apiCall } = useAuth();

    const fetchUserData = async () => {
        try {
            // This automatically includes FastAPI JWT token
            const data = await apiCall("/auth/me");
            console.log("User data:", data);
        } catch (error) {
            console.error("API call failed:", error);
        }
    };

    if (!isAuthenticated) {
        return <div>Please log in</div>;
    }

    return (
        <div>
            <h1>Welcome, {user.name}!</h1>
            <button onClick={fetchUserData}>Fetch User Data</button>
        </div>
    );
}
```

### **Making API calls:**

```tsx
import { useApiCall } from "@/hooks/useAuth";

function HubManager() {
    const apiCall = useApiCall();

    const createHub = async (hubData) => {
        const response = await apiCall("/api/v1/hubs", {
            method: "POST",
            body: JSON.stringify(hubData),
        });
        return response;
    };

    // ... rest of component
}
```

## ✅ **Benefits of This Approach**

1. **Separation of Concerns**: Auth.js handles OAuth, FastAPI handles business logic
2. **Single Source of Truth**: All user data lives in FastAPI database
3. **Consistent API**: Frontend and other clients use the same FastAPI endpoints
4. **Scalable**: FastAPI can serve multiple frontends (web, mobile, etc.)
5. **Secure**: JWT tokens are properly validated by FastAPI

## 🔄 **Migration Steps**

1. **Update environment variables** in both Next.js and FastAPI
2. **Remove Prisma adapter** from Auth.js configuration (already done)
3. **Update components** to use `useAuth` hook instead of `useSession`
4. **Test authentication flow** with the example component
5. **Update existing API calls** to use the new `apiCall` function

## 🧪 **Testing**

Use the `AuthExample` component to test the integration:

```tsx
import { AuthExample } from "@/components/auth-example";

export default function TestPage() {
    return <AuthExample />;
}
```

This will show you:

- Authentication status
- FastAPI JWT token presence
- API call examples
- Step-by-step flow explanation
