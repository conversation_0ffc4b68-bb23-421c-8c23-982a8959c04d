# Session-Based Auth.js + FastAPI Integration Guide

## 🎯 **Clean Architecture Overview**

We've implemented a **session-based authentication system** that eliminates complex token exchanges:

- **Auth.js (Next-Auth v5)** handles OAuth and JWT session management
- **FastAPI backend** validates JW<PERSON> sessions and handles all business logic  
- **Shared JWT secret** enables seamless authentication between systems
- **No token exchange** - clean separation of concerns

## 🔄 **Architecture Flow**

```
┌─────────────┐    Discord OAuth    ┌─────────────┐
│   Next.js   │ ←─────────────────→ │   Discord   │
│  (Auth.js)  │                     │    OAuth    │
└─────────────┘                     └─────────────┘
       │
       │ JWT Session Cookies
       │ (Automatic)
       ↓
┌─────────────┐    Validates JWT    ┌─────────────┐
│   FastAPI   │ ←─────────────────→ │  Database   │
│   Backend   │   Creates/Updates   │ (PostgreSQL)│
└─────────────┘      Users          └─────────────┘
```

## 🚀 **How It Works**

1. **User authenticates** via Auth.js Discord OAuth
2. **Auth.js creates** JWT token with user data
3. **JWT stored** in secure HTTP-only session cookies
4. **Frontend API calls** automatically include session cookies
5. **FastAPI validates** JWT using shared secret
6. **FastAPI extracts** user data from validated JWT
7. **User created/updated** in database automatically

## 🔧 **Environment Configuration**

### **Next.js (.env.local)**
```bash
# CRITICAL: Must match FastAPI's AUTH_SECRET
AUTH_SECRET="your-super-secret-auth-key-min-32-chars"
NEXTAUTH_URL="http://localhost:3000"

# Discord OAuth2 (same credentials as FastAPI)
NEXT_PUBLIC_DISCORD_CLIENT_ID="your_discord_client_id"
DISCORD_CLIENT_SECRET="your_discord_client_secret"

# FastAPI Backend URL
NEXT_PUBLIC_API_URL="http://localhost:8002"
```

### **FastAPI (.env)**
```bash
# CRITICAL: Must match Next.js AUTH_SECRET
AUTH_SECRET="your-super-secret-auth-key-min-32-chars"

# Discord OAuth2 (same credentials as Next.js)
DISCORD_CLIENT_ID="your_discord_client_id"
DISCORD_CLIENT_SECRET="your_discord_client_secret"

# Database
DATABASE_URL="postgresql+asyncpg://username:password@localhost:5432/interchat"
```

## 💻 **Implementation Details**

### **Auth.js Configuration (website/src/auth.ts)**
```typescript
export const { handlers, auth, signIn, signOut } = NextAuth({
  session: { strategy: "jwt", maxAge: 30 * 24 * 60 * 60 },
  secret: process.env.AUTH_SECRET, // Shared with FastAPI
  providers: [Discord({ /* config */ })],
  callbacks: {
    jwt({ token, account, profile }) {
      // Store Discord user data in JWT
      if (account?.provider === "discord" && profile) {
        token.id = account.providerAccountId;
        token.name = profile.username;
        // ... other Discord data
      }
      return token;
    }
  }
});
```

### **FastAPI Session Validation (packages/api/src/core/session_auth.py)**
```python
class SessionAuth:
    def __init__(self):
        self.secret = os.getenv('AUTH_SECRET')  # Same as Next.js
        self.cookie_name = "next-auth.session-token"
    
    def validate_and_decode_token(self, token: str) -> Optional[AuthUser]:
        payload = jwt.decode(token, self.secret, algorithms=["HS256"])
        return AuthUser(id=payload.get("id"), name=payload.get("name"))

async def require_auth(request: Request) -> User:
    token = request.cookies.get("next-auth.session-token")
    auth_user = session_auth.validate_and_decode_token(token)
    # Get or create user in database
    return user
```

### **Frontend API Calls (website/src/hooks/useAuth.ts)**
```typescript
const apiCall = useCallback(async (url: string, options: RequestInit = {}) => {
  const response = await fetch(fullUrl, {
    ...options,
    credentials: "include", // Include session cookies
    headers: { "Content-Type": "application/json", ...options.headers },
  });
  return response.json();
}, [session]);
```

## ✅ **Benefits of This Architecture**

1. **No Token Exchange** - Eliminates complex token swapping logic
2. **Automatic Cookie Handling** - Browsers handle session cookies seamlessly  
3. **Shared Secret Validation** - Simple JWT validation with same secret
4. **Clean Separation** - Auth.js for OAuth, FastAPI for business logic
5. **Stateless Authentication** - JWT tokens contain all needed user data
6. **Cross-Origin Ready** - Works with frontend on Vercel, API on VPS

## 🧪 **Testing the Integration**

Use the `AuthExample` component to test:

```tsx
import { AuthExample } from '@/components/auth-example';

export default function TestPage() {
  return <AuthExample />;
}
```

This component will show:
- Authentication status
- Session cookie information  
- API call testing with `/auth/me` and `/auth/status`
- Step-by-step flow explanation

## 🔄 **Migration from Token Exchange**

If migrating from the previous token exchange approach:

1. **Remove** `/auth/token` endpoint from FastAPI
2. **Remove** Discord OAuth endpoints from FastAPI (`/login/discord`, `/auth/discord/callback`)
3. **Update** Auth.js to use JWT strategy (no database adapter)
4. **Add** `AUTH_SECRET` to both Next.js and FastAPI environments
5. **Update** API calls to use `credentials: "include"`
6. **Replace** authentication dependencies with `require_auth` from `session_auth.py`

## 🚨 **Important Notes**

- **AUTH_SECRET must be identical** in both Next.js and FastAPI
- **Session cookies** are HTTP-only and secure in production
- **CORS configuration** must allow credentials for cross-origin requests
- **User creation** happens automatically in FastAPI when JWT is validated
- **No database operations** in Auth.js - purely OAuth handling

This architecture provides a clean, maintainable, and secure authentication system! 🎉
