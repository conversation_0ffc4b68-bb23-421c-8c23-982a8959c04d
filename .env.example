# ===================================
# 🚀 BASIC CONFIG - CHANGE THESE
# ===================================
ENVIRONMENT=DEVELOPMENT # or PRODUCTION
INTERCHAT_VERSION=5.0.0

# Discord bot
CLIENT_ID=your_client_id_here
DISCORD_TOKEN=your_bot_token_here
PREFIX=!

# Database (PostgreSQL, async)
# Format: postgresql+asyncpg://user:password@host:port/dbname
DATABASE_URL=postgresql+asyncpg://user:pass@localhost:5432/interchat

# Redis
# Format: redis://host:port/db
REDIS_URI=redis://localhost:6379/0

# ===================================
# ⚙️ OPTIONAL STUFF
# ===================================
# Sentry
SENTRY_DSN=
SENTRY_PII=False

# NSFW Detector
ENABLE_NSFW=True
NSFW_DETECTOR_URL=

# App metadata / links
SUPPORT_INVITE=
DONATE_LINK=

# Access control & staff
# Python list of user IDs, e.g. [123456789012345678, 987654321098765432]
AUTH=[]
DEV_GUILD_ID=
STAFF_ROLE_ID=
STAFF_REPORT_CHANNEL_ID=

# ===================================
# ⚠️ ADVANCED SETTINGS - CHANGE ONLY IF YOU KNOW WHAT YOU’RE DOING
# ===================================
AUTO_CREATE_TABLES=False
DEBUG=True
