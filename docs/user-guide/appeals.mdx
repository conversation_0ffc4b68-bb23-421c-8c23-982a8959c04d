---
title: "Appeals"
description: "How to review and appeal hub moderation actions"
icon: "gavel"
---

## Why appeals exist

Mistakes happen. Appeals let you request a second look at serious moderation actions (like bans) applied to you in a hub.

<Info>
    Warnings normally aren't appealable; focus is on bans / blacklist-level
    actions.
</Info>

## What you can appeal

-   Active hub bans (user- or server-level)
-   Re-appeal after cooldown if a prior appeal was decided

<Warning>
    If a hub moderators already reviewed and rejected an appeal, you must wait
    for the hub's configured cooldown before trying again.
</Warning>

## Running the command

<Steps>
    <Step title="Open panel">Run `/appeal` (no options).</Step>
    <Step title="Review list">
        See all active, previously appealed and cooldown-locked infractions.
    </Step>
    <Step title="Select one">
        Pick an appealable infraction from the dropdown.
    </Step>
    <Step title="Fill modal">Answer the 3 short questions honestly.</Step>
    <Step title="Submit">
        You'll get a confirmation; status shows as Pending.
    </Step>
</Steps>

[IMAGE: Appeals panel showing infractions list with statuses]

## Status meanings

| Status           | Meaning                                                         |
| ---------------- | --------------------------------------------------------------- |
| Can Appeal       | You haven't appealed this yet – selectable.                     |
| Appeal Pending   | An open appeal awaits moderator review.                         |
| Cooldown         | You appealed; must wait until shown relative timestamp expires. |
| Can Appeal Again | Previous appeal resolved; cooldown passed.                      |

## Cooldowns

Each hub sets `appealCooldownHours` (default 7 days). Timer starts when you submit an appeal (not when a decision is made). While on cooldown you can still view the infraction but can't submit again.

<Info>
    The UI shows relative expiry (e.g. "in 3d"). Once expired the status flips
    to Can Appeal Again.
</Info>

Try again later; cooldown resets rolling after the specified duration set by the hub from your first submission.

## Moderator outcomes

| Decision           | Result                                 |
| ------------------ | -------------------------------------- |
| Accepted           | Infraction revoked (you regain access) |
| Rejected           | Infraction stays; cooldown applies     |
| Ignored / Deferred | Rare; usually during investigations    |

## Privacy & data

Appeal text is stored with the infraction record for audit/training of moderators. Personal info should not be included; redact sensitive details.

## Troubleshooting

| Issue                 | Fix                                                                           |
| --------------------- | ----------------------------------------------------------------------------- |
| No infractions listed | You have none active (or only warnings).                                      |
| Dropdown missing      | All infractions are pending or on cooldown.                                   |
| Cooldown timer wrong  | Hub changed its cooldown after you appealed; timer uses original submit time. |
| Modal didn't appear   | Re-run command; ensure you didn't dismiss the modal.                          |

<Info>
    Need clarification or believe there's systemic abuse? Contact InterChat
    staff via the support server.
</Info>

<CardGroup cols={2}>
    <Card title="Messaging" href="/user-guide/messaging" icon="message" />
    <Card
        title="Infractions"
        href="/hub-management/moderation/infractions"
        icon="gavel"
    />
</CardGroup>
