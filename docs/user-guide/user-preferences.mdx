---
title: "User Preferences"
description: "Customize your InterChat experience with global settings"
icon: "sliders"
---

## Access preferences
Run `/my preferences` to open your personal settings panel. These settings apply globally—they follow you to any server with InterChat.

<Frame>
[IMAGE: User preferences panel with sections for locale, badges, replies]
</Frame>

## Available settings

### Language & Locale
Choose your preferred language for bot responses and embeds. InterChat supports multiple locales including English, French, Arabic, Bulgarian, and Danish.

<Steps>
<Step title="Open locale selector">
Click the Language button in your preferences panel.
</Step>
<Step title="Choose from dropdown">
Select your preferred language from the available options.
</Step>
<Step title="Confirm change">
Settings update immediately; future bot interactions use your chosen language.
</Step>
</Steps>

### Badge Visibility
Control whether your earned badges (<PERSON><PERSON><PERSON>, Translator, Supporter, etc.) appear on your profile and in cross-server messages.

<Info>
Badges reflect your contributions to InterChat: voting, translating, supporting development, or staff roles.
</Info>

### Mention & Reply Behavior
Configure how InterChat handles mentions and replies in cross-server contexts:
- Show/hide mention notifications
- Reply threading behavior
- Cross-server ping preferences

<Tip>
Disable cross-server pings if you're in many active hubs to reduce notification noise.
</Tip>

## Data & Privacy
All preference data is stored securely and only affects how InterChat presents information to you. You can reset to defaults at any time.

## Troubleshooting preferences
If settings don't seem to apply:
1. Ensure you're using the same Discord account
2. Try refreshing the preferences panel
3. Check if the specific server has overrides

<Warning>
Some servers may have policies that override individual user preferences for safety or consistency.
</Warning>

<CardGroup cols={2}>
<Card title="Profile System" href="/user-guide/profile-system" icon="user">
View how profiles & achievements work.
</Card>
<Card title="Commands Reference" href="/user-guide/commands" icon="terminal">
Complete list of user commands.
</Card>
</CardGroup>
