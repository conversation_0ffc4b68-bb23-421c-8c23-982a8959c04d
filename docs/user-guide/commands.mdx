---
title: "Commands Reference"
description: "Complete list of InterChat commands organized by category"
icon: "terminal"
---

## General Commands
Available to all users in servers with InterChat.

### Profile & Stats
- `/profile [@user]` - View your or another user's InterChat profile
- `/leaderboard [filter]` - View top users by messages or votes
- `/stats` - Display bot statistics and metrics
- `/about` - Learn about InterChat's features and community impact

### Bot Information
- `/invite` - Get bot invite link and support server info
- `/help` - Display help menu with command categories

## Hub Commands
Commands for discovering and participating in hubs.

### Browsing Hubs
- `/hubs` - List available public hubs for joining

### Hub Management
- `/hub create` - Create a new hub (Owner)
- `/hub configure` - Configure hub settings (Owner/Manager)
- `/hub invites` - View or create invite codes for your hub
- `/connect hub:<name> [channel]` - Connect this channel to a public hub by name
- `/connect invite:<code> [channel]` - Connect using an invite code
- `/disconnect` - Remove this channel from its hub

## Personal Commands
Commands under the `/my` group for user-specific functions.

### User Management
- `/my hubs` - View hubs you own or moderate
- `/my preferences` - Configure your global InterChat settings

## Moderation Commands
Available to hub moderators and above.

### Infractions
- `/infractions` - View and manage hub infractions with filtering

## Setup Commands
Server administration commands.

### Initial Setup
- `/setup` - Quick server setup wizard

## Staff Commands
Available only to InterChat staff members.

### Global Moderation
- `/blacklist` group - Global user/server management (staff only)

<Info>
Commands marked with permission requirements will show an error if you lack the necessary role or permissions.
</Info>

## Command Syntax
- `[argument]` - Optional parameter
- `@user` - Mention a Discord user
- `filter` - Choose from provided options (dropdown or autocomplete)

## Getting Help
If a command isn't working:
1. Check your permissions (server & hub roles)
2. Verify the bot has necessary permissions in the channel
3. Join the support server for assistance

<Frame>
[IMAGE: Command usage example showing /profile command with response]
</Frame>

<Tip>
Use Tab completion in Discord to see available options for command parameters.
</Tip>

<CardGroup cols={2}>
<Card title="User Preferences" href="/user-guide/user-preferences" icon="settings">
Customize command behavior.
</Card>
<Card title="Joining Hubs" href="/user-guide/joining-hubs" icon="plus">
Start using hub commands.
</Card>
</CardGroup>
