---
title: "Messaging Across Servers"
description: "How cross-server messaging works in InterChat and how to use advanced features"
icon: "comments"
---

## Overview
InterChat transforms ordinary Discord channels into global communication hubs. This guide explains how cross-server messaging works, how to identify message origins, and how to use advanced messaging features effectively.

<Info>
Every message transmitted through InterChat retains its original author and server context to maintain transparency and accountability.
</Info>

## Message Structure

<Frame>
[IMAGE: Cross-server message showing server badge, username, and content layout]
</Frame>

Components:
- 🌐 Indicator that it's an InterChat message
- **Server name** where the message originated
- **Username** of the original sender (discriminator hidden or shown depending on config)
- **Message content**, attachments, and embeds

### Message Origin Indicators

<Tabs>
<Tab title="Server Badge">
Displays the originating server's name. Some hubs allow custom formatting or abbreviations.
</Tab>
<Tab title="User Identity">
Shows the sender's username and discriminator. Nicknames are not used to maintain consistency across servers.
</Tab>
<Tab title="Formatting Preservation">
Preserves bold, italics, code blocks, spoilers, and other markdown formatting.
</Tab>
<Tab title="Attachment Handling">
Images, files, and embeds are proxied when possible. Some large files may not transmit depending on hub policies.
</Tab>
</Tabs>

## Sending Messages

You send messages exactly like in any normal Discord text channel—InterChat handles the rest. However, keep these guidelines in mind:

<Tip>
Treat each message as if you're representing your entire server to other communities.
</Tip>

### Best Practices

- Be concise and clear—multiple servers are reading
- Avoid server-specific inside jokes without context
- Follow both your server's rules and hub rules
- Keep conversations on-topic for specialized hubs

## Replying & Threading

### Replying to Messages

<CodeGroup>
```markdown Native Reply
Use Discord's reply context action — InterChat attempts to preserve reply context if supported.
```
</CodeGroup>
- Indicates reply relationship across servers
- May truncate long original messages in the preview

<Warning>
Not all hubs or connections fully support threaded replies. Complex thread structures may collapse to linear conversations.
</Warning>

## Mentions & Notifications

### User Mentions

User mentions typically do NOT ping users in other servers unless:
- The user is present in both servers
- The hub has enhanced mention bridging enabled

### Role Mentions

Role mentions do NOT transmit to other servers to prevent abuse.

### @everyone / @here

<Warning>
Global mentions (@everyone / @here) are blocked or sanitized in most hubs to prevent mass ping abuse.
</Warning>

## Embeds & Rich Content

Supported content types:
- Standard embeds (titles, descriptions, fields)
- Image attachments (size limits apply)
- Links (with metadata when available)
- Code blocks (language highlighting preserved)

Unsupported / Restricted:
- Ephemeral messages
- Private thread content
- Stage channel interactions

## Reactions & Emoji

### Standard Reactions

Adding a reaction in your server does NOT automatically replicate to other servers in most configurations to prevent reaction spam.

### Emoji Rendering

<Tabs>
<Tab title="Unicode Emoji">
Always display properly across servers.
</Tab>
<Tab title="Custom Emoji">
May appear as text (:emoji_name:) if the destination server lacks access.
</Tab>
<Tab title="Animated Emoji">
Shown when supported, otherwise fallback to static or name representation.
</Tab>
</Tabs>

## Message Editing & Deletion

### Editing Messages

When you edit a message you've sent:
- Edit attempts are broadcast to other servers
- Some hubs show an (edited) indicator
- Major content changes may trigger moderation review

### Deleting Messages

If you delete a message:
- InterChat attempts to remove it from all connected servers
- Not guaranteed if destination server lost webhook context
- Moderation deletions show removal reason when enabled

<Warning>
Abusive deletion/edit cycles (sending then removing inflammatory content) may trigger anti-abuse systems.
</Warning>

## Attachments & Media

Attachment processing pipeline:

<Steps>
<Step title="Upload">
  You upload a file/image in your channel.
</Step>
<Step title="Scan (If Enabled)">
  Optional NSFW / malware scanning occurs.
</Step>
<Step title="Proxy or Reference">
  File is proxied or referenced depending on size and policy.
</Step>
<Step title="Delivery">
  Other servers receive message with media link or preview.
</Step>
</Steps>

<Tip>
File size limitations vary by hub policy (common range 8–25MB). Oversized media may be converted to links.
</Tip>

## Rate Limiting & Flood Control

To protect hub quality, InterChat implements rate limiting:

<AccordionGroup>
<Accordion title="Message Frequency">
Typical limits: 5–10 messages per 10 seconds per server (varies by hub).
</Accordion>
<Accordion title="Burst Protection">
Rapid bursts may queue or discard excess messages.
</Accordion>
<Accordion title="Duplicate Filtering">
Identical repeated messages may be suppressed.
</Accordion>
<Accordion title="Spam Heuristics">
Patterns like mass mentions or link floods trigger throttling.
</Accordion>
</AccordionGroup>

<Info>Rate limiting is automatic; there is no `/ratelimit` command.</Info>

## Moderation Interactions

Messages may be blocked before distribution if they:
- Match banned word filters
- Trigger NSFW detection
- Contain disallowed links

### User Feedback

When your message is blocked, you might see:
```
🚫 Your message was blocked by InterChat filters (Reason: NSFW detection)
```

<Info>Need to challenge a hub ban? Use `/appeal` for an interactive review panel.</Info>

## Troubleshooting Messaging Issues

<AccordionGroup>
<Accordion title="Messages not appearing">
Verify the channel is still connected and not hub-locked.
</Accordion>
<Accordion title="Delayed delivery">
High hub traffic or large attachment processing can cause short delays.
</Accordion>
<Accordion title="Formatting issues">
Some markdown (tables, complex embeds) may downgrade for compatibility.
</Accordion>
</AccordionGroup>

## Quick Reference Commands

<Info>Connection / rate status commands are not exposed; use `/hub info` and the channel activity itself.</Info>
<ParamField path="/my preferences" type="command">
Adjust personal message display preferences
</ParamField>


<Check>
You now understand how InterChat messaging works. Explore preferences next to customize your experience.
</Check>

<Card title="Customize Your Experience" icon="gear" href="/user-guide/user-preferences">
  Learn how to tailor InterChat to your personal workflow.
</Card>
