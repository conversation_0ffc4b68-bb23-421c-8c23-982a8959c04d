---
title: "Joining and Discovering Hubs"
description: "Complete guide to finding, exploring, and joining InterChat hubs that match your interests"
icon: "signs-post"
---

## Understanding Hub Discovery

InterChat offers community hubs that you can discover in Discord and on the web directory.

<Info>
Use `/hubs` in Discord to see public hubs, or browse the web directory at https://interchat.tech/hubs for search and filters.
</Info>

## How Hub Discovery Actually Works

Instead of using in-bot commands for searching, you:

<Steps>
<Step title="Open Hub Directory">
  Visit the official hub directory: https://interchat.tech/hubs
</Step>
<Step title="Filter & Search (On Website)">
  Use the website UI filters (topic, language, region, activity) — these are NOT slash commands.
</Step>
<Step title="Select a Hub">
  Open a hub detail page to review its description, rules, and connection requirements.
</Step>
<Step title="Copy Hub Identifier">
  Each hub shows a unique name (e.g. `my-hub-name`) — you'll use that in the /connect command.
</Step>
</Steps>

<Warning>
Discovery filters are available on the website. In Discord, `/hubs` shows a simple public list.
</Warning>

## Evaluating Hubs Before Joining

### Research Hub Information

Before connecting, thoroughly research any hub:

Review the hub’s page on the website for:
- Description and rules
- Owner/moderator team
- Activity level and size
- Any requirements or guidelines

### Key Evaluation Criteria

<Steps>
<Step title="Topic Alignment">
  **Does the hub match your community's interests?**
  - Read the description carefully
  - Check recent message samples
  - Verify topic focus
  - Consider cultural fit
</Step>

<Step title="Activity Level">
  **Is the hub active enough for engagement?**
  - Check daily message count
  - Look at peak activity times
  - Consider timezone compatibility
  - Evaluate growth trends
</Step>

<Step title="Community Quality">
  **Will your members enjoy this community?**
  - Review moderation policies
  - Check rule strictness
  - Observe conversation quality
  - Consider language requirements
</Step>

<Step title="Size Considerations">
  **Is the hub size appropriate?**
  - Small hubs (10-50 servers): intimate, personal
  - Medium hubs (50-200 servers): balanced activity
  - Large hubs (200+ servers): high activity, diverse
</Step>
</Steps>

### Red Flags to Avoid

<Warning>
Be cautious of hubs with these characteristics:
</Warning>

- **No clear rules** or moderation policies
- **Extremely high activity** that might overwhelm your channel
- **Poor moderation** with frequent inappropriate content
- **Toxic community culture** or unwelcoming atmosphere
- **Spam or advertising focus** rather than genuine community
- **Conflicting values** with your server's community guidelines

## The Hub Joining Process

### Standard Public Hub Connection (by name)

<Steps>
<Step title="Pick a Channel">
  Create or choose a channel (e.g. `#interchat`) that will relay messages.
</Step>
<Step title="Get Hub Name From Website">
  Copy the hub name from its page, e.g. `example-hub`.
</Step>
<Step title="Connect">
```bash
/connect hub:example-hub channel:#interchat
```
</Step>
<Step title="Verify">
  ```bash
  /connections list
  ```
</Step>
</Steps>

## Multiple Hub Management

### Connecting to Multiple Hubs

Most servers benefit from connecting to 2-4 different hubs:

<Tabs>
<Tab title="Topic-Based Strategy">
```bash
# General discussions
/connect hub:example-hub channel:#global-bridge

# Gaming focus
/connect hub:gaming-central channel:#gaming-interchat

# Tech discussions
/connect hub:programming-help channel:#tech-interchat
```

**Benefits:**
- Clear topic separation
- Targeted discussions
- Easier moderation
- Member choice
</Tab>

<Tab title="Single Channel Strategy">
```bash
# One channel, multiple hubs (advanced)
/connect hub:global-chat channel:#interchat
/connect hub:gaming-general channel:#interchat
```

**Considerations:**
- Higher message volume
- Mixed conversations
- Requires active moderation
- May overwhelm smaller communities
</Tab>
</Tabs>

### Managing Hub Connections

Use `/connect` to add, `/disconnect` to remove. Track which channels are connected in your server structure.

## Community Integration Tips

### Preparing Your Server

Before joining new hubs:

<Steps>
<Step title="Inform Your Members">
  Announce new connections and explain cross-server communication:
  
  ```markdown
  📢 Announcement: New InterChat Connection!
  
  We've joined the "gaming-general" hub, connecting us with 
  300+ other gaming servers worldwide!
  
  Messages in #interchat will now reach gamers globally.
  Let's represent our community well! 🎮
  ```
</Step>

<Step title="Update Channel Descriptions">
  Clearly identify InterChat channels:
  
  ```
  🌐 Connected to: gaming-general hub
  💬 Your messages reach 300+ gaming servers
  📋 Follow hub rules: /hub info gaming-general
  ```
</Step>

<Step title="Set Clear Expectations">
  Establish guidelines for cross-server behavior:
  - Represent your server positively
  - Follow both server and hub rules
  - Be respectful to all community members
  - Report issues to moderators
</Step>
</Steps>

### Engagement Best Practices

<Tip>
Make the most of your hub connections with these strategies:
</Tip>

- **Introduce your server** uniquely and memorably
- **Share relevant content** that adds value to discussions
- **Ask engaging questions** to start conversations
- **Support other communities** and their events
- **Participate regularly** to build relationships
- **Help newcomers** understand hub culture

## Troubleshooting Hub Discovery

<AccordionGroup>
<Accordion title="No hubs match my interests">
**Solutions:**
- Try broader search terms
- Check different languages if applicable
- Consider creating your own hub
- Join general hubs and find like-minded people
- Ask in support server for recommendations
</Accordion>

<Accordion title="Hub search returns no results">
**Possible causes:**
- Typo in search terms
- Hub temporarily unavailable
- Search filters too restrictive

**Solutions:**
- Try alternative keywords
- Remove filters and browse manually
- Check for spelling errors
</Accordion>

<Accordion title="Can't join invite-only hub">
**Common reasons:**
- Application doesn't meet requirements
- Server doesn't align with hub focus
- Previous moderation issues
- Hub is currently full

**Solutions:**
- Improve application with more detail
- Build server reputation first
- Try similar public hubs
- Contact hub moderators for guidance
</Accordion>
</AccordionGroup>

## Next Steps

<CardGroup cols={2}>
<Card title="💬 Master Messaging" icon="comment" href="/user-guide/messaging">
  Learn advanced messaging features and cross-server communication
</Card>

<Card title="⚙️ User Preferences" icon="gear" href="/user-guide/user-preferences">
  Customize your InterChat experience
</Card>

<Card title="🏗️ Create Your Own Hub" icon="hammer" href="/hub-management/creating-hubs">
  Start your own community hub
</Card>

<Card title="🔧 Advanced Commands" icon="terminal" href="/user-guide/commands">
  Master all InterChat commands and features
</Card>
</CardGroup>

---

<Note>
Remember: The best hub connections come from understanding your community's needs and finding hubs that genuinely align with your members' interests. Take time to research and choose wisely!
</Note>
