{"$schema": "https://mintlify.com/docs.json", "theme": "mint", "name": "InterChat Documentation", "logo": {"dark": "/logo/dark.svg", "light": "/logo/light.svg"}, "colors": {"primary": "#9172D8", "light": "#9172D8", "dark": "#9172D8"}, "favicon": "/favicon.png", "navigation": {"dropdowns": [{"dropdown": "Guide", "icon": "book", "description": "Learn how to use InterChat effectively.", "groups": [{"group": "Getting Started", "pages": ["index", "getting-started/quickstart", "getting-started/basic-setup", "getting-started/first-connection"]}, {"group": "Messaging & Hubs", "pages": ["user-guide/joining-hubs", "user-guide/messaging", "user-guide/user-preferences", "user-guide/profile-system", "user-guide/appeals", "user-guide/commands"]}, {"group": "Hub Management", "pages": ["hub-management/creating-hubs", "hub-management/hub-configuration", "hub-management/hub-modules", "hub-management/moderation/overview", "hub-management/moderation/moderator-commands", "hub-management/moderation/filtering", "hub-management/moderation/infractions", "hub-management/logging-configuration", "hub-management/general-settings", "hub-management/staff-management"]}, {"group": "Troubleshooting", "pages": ["troubleshooting/common-issues", "troubleshooting/faq"]}]}, {"dropdown": "Development", "icon": "code", "description": "Getting started with InterChat development and contribution.", "groups": [{"group": "Developer", "pages": ["development/introduction", "development/setup", "development/architecture", "development/message-validation", "development/infractions-model", "development/filtering-system"]}]}], "global": {"anchors": [{"anchor": "Discord Server", "href": "https://www.interchat.tech/support", "icon": "discord"}, {"anchor": "GitHub", "href": "https://github.com/interchatapp/InterChat.py", "icon": "github"}, {"anchor": "Website", "href": "https://interchat.tech", "icon": "globe"}]}}, "navbar": {"links": [{"label": "Support", "href": "https://www.interchat.tech/support"}], "primary": {"type": "button", "label": "Add to Discord", "href": "https://discord.com/oauth2/authorize?client_id=769921109209907241&permissions=8&scope=bot%20applications.commands"}}, "contextual": {"options": ["copy", "view", "chatgpt", "claude", "cursor", "vscode"]}, "footer": {"socials": {"discord": "https://www.interchat.tech/support", "github": "https://github.com/interchatapp/InterChat.py", "website": "https://interchat.tech"}}}