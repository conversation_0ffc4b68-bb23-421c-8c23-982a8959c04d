---
title: "General Hub Settings"
description: "Name, visibility, rating, and welcome—everything you need to set up your hub"
icon: "wrench"
---

General settings define how your hub looks, who can find it, and how new folks are greeted.

## TL;DR (at a glance)

| Setting             | What it controls               |      Limit | Who can edit | Notes                                         |
| ------------------- | ------------------------------ | ---------: | ------------ | --------------------------------------------- |
| **Hub Name**        | Display name across InterChat  |  100 chars | Manager+     | Must be unique • 10-day rename cooldown       |
| **Description**     | What your hub is about         | 1000 chars | Manager+     | Shown in discovery, join prompts, info panels |
| **Welcome Message** | First impression for new users |  500 chars | Manager+     | Discord Markdown + variables • can be cleared |
| **NSFW Rating**     | Content maturity flag          |          — | Manager+     | Affects search/discoverability filters        |
| **Visibility**      | Public vs Private              |          — | Manager+     | Public = listed • Private = invite-only       |

---

## Open the settings

<Steps>
    <Step title="Open Hub Configuration">
        Run `/hub configure` in any connected server where you have
        **Manager+**.
    </Step>
    <Step title="Pick General Settings">
        Choose **General Settings** from the dropdown.
    </Step>
    <Step title="Review Current Values">
        You'll see all current values and their status.
    </Step>
</Steps>

---

## Settings detail

### 🏷️ Hub Name

**What it is:** Your hub's display name everywhere.

-   Must be **unique** across InterChat
-   **100 chars** max
-   **10-day cooldown** after each change

<Warning>
    Renames are locked for 10 days after a change. Pick something you can live
    with.
</Warning>

**Good patterns**

-   Clear + specific: `Epic Gamers — FPS & MMOs`
-   Avoid spammy symbols or look-alikes

---

### 📝 Short Description

**What it is:** The short pitch for your hub.

Appears in:

-   Discovery/search cards
-   Join confirmations
-   Info panels

**Keep it tight**

-   Aim for **1-2 lines** (≤ **200 chars**)
-   Say what happens here briefly

**Example**

```
Fast, friendly cross-server chat for FPS/MMO squads. Family-friendly, no NSFW.
```

---

### 📝 Description

**What it is:** The short pitch for your hub.

Appears in:

-   Hub page
-   Join confirmations
-   Info panels

**Keep it tight**

-   Aim for **2–4 lines** (≤ **1000 chars**)
-   Say what happens here + key rules/expectations
-   Mention themes or requirements if any

**Example**

```
Fast, friendly cross-server chat for FPS/MMO squads.
Daily LFG threads, weekly scrims, and coaching requests.
Family-friendly, no NSFW. Read #rules before posting.
```

---

### 👋 Welcome Message

**What it is:** Auto-message shown to new joiners / when learning about the hub.

-   **500 chars** max
-   Supports **Discord Markdown**
-   You can **disable** it by clearing the text

**Variables you can use**

-   `{user}` `{hubName}` `{serverName}` `{memberCount}` `{totalConnections}`

**Examples**

```
{user} welcome to **{hubName}**! 🎉
See /hub rules for do's & don'ts.
```

```
Hey {user}! You're now connected to {hubName}.
We're {memberCount}+ strong across {totalConnections} servers.
Jump into #general or type **/topics** to browse threads.
```

<Tip>
    Keep a single call-to-action (introduce, read rules, or start a thread). Too
    many = no action.
</Tip>

---

### 🔞 NSFW Content Rating

Flags your hub as adult content.

**If enabled**

-   Marked as NSFW in listings
-   Hidden for users who filter NSFW
-   Extra warnings before joining

**If disabled**

-   Fully visible to all audiences

<Info>
    Be accurate. Wrong ratings hurt discoverability and can trigger moderation.
</Info>

---

### 🔒 Visibility

Who can find and join your hub.

**Public (🌐)**

-   Listed in discovery & search
-   Anyone can join
-   Best for growth

**Private (🔒)**

-   Hidden from listings
-   Invite or join link required
-   Best for closed/early-stage groups

<Tip>
    Common flow: start **Private** while setting up → switch to **Public** when
    ready.
</Tip>

---

## Make a change (how-to)

### Edit text fields (Name, Description, Welcome)

<Steps>
    <Step title="Choose the action">
        Pick **Edit Hub Name**, **Edit Description**, or **Welcome Message**.
    </Step>
    <Step title="Update the text">
        A modal opens with the current value prefilled.
    </Step>
    <Step title="Save">Submit to apply. The UI updates right away.</Step>
</Steps>

### Toggle switches (NSFW, Visibility)

<Steps>
    <Step title="Choose the toggle">
        Select **Toggle NSFW** or **Toggle Private**.
    </Step>
    <Step title="Confirm">You'll get a confirmation of the new state.</Step>
    <Step title="Check impact">
        The panel explains what the change means (search, access, etc.).
    </Step>
</Steps>

---

## Troubleshooting

<AccordionGroup>
<Accordion title="Can't change hub name">
- You need **Manager+**
- 10-day rename cooldown might be active
- Name must be **unique** and ≤ 100 chars
</Accordion>

<Accordion title="Description won't save">
- ≤ **1000 chars** 
- Remove odd control characters 
- Recheck your permissions
- Refresh and try again
</Accordion>

<Accordion title="Welcome message not showing">
- Confirm it's saved (not empty) 
- Test with a fresh server join - Keep it ≤ **500 chars** 
- Ensure the hub isn't locked
</Accordion>

<Accordion title="NSFW/Private toggle not working">
- Confirm **Manager+**
- Wait a minute for propagation
- Refresh the config panel
</Accordion>
</AccordionGroup>

---

## Advanced (dashboard)

Some settings live on the web dashboard:

-   **Icon & Banner** — branding
-   **Tags & Language** — categorization/locale
-   **Analytics** — activity & growth
-   **Moderation Rules** — filters & automations

<CardGroup cols={2}>
    <Card
        title="Web Dashboard"
        href="https://www.interchat.tech/dashboard"
        icon="globe"
    >
        Advanced settings & analytics.
    </Card>
    <Card title="Hub Modules" href="/hub-management/hub-modules" icon="sliders">
        Enable/disable features by module.
    </Card>
</CardGroup>

---

## Security & Privacy

### Ratings

-   NSFW impacts legal/compliance in some regions
-   Know your audience age mix
-   Keep clear community guidelines

<Warning>
    Changes affect all connected servers. Align updates with partner
    communities.
</Warning>
