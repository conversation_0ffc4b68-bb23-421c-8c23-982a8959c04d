---
title: "Hub Permissions & Staff Management"
description: "Manage hub staff roles, permissions, and ownership transfer"
icon: "users"
---

Hub permissions control who can configure, moderate, and manage your hub. InterChat uses a tiered permission system that allows you to delegate responsibilities while maintaining control over your community.

## Permission Levels

### 👑 Owner

**Ultimate authority over the hub**

**Capabilities**:

-   Full configuration access to all hub settings
-   Can promote/demote managers and moderators
-   Can transfer hub ownership to another user
-   Can delete or permanently modify the hub
-   Access to all moderation tools and overrides
-   Can manage advanced settings and integrations

**Limitations**:

-   Only one owner per hub
-   Cannot be demoted by other users

### 🛠️ Manager

**Trusted administrators with extensive permissions**

**Capabilities**:

-   Configure most hub settings (modules, logging, general settings)
-   Promote/demote moderators (cannot manage other managers)
-   Access advanced moderation tools
-   Manage channel connections
-   Configure hub rules and automation
-   View hub analytics and logs

**Limitations**:

-   Cannot manage other managers or the owner
-   Cannot transfer ownership
-   Cannot delete the hub
-   Some advanced settings may be owner-only

### 🛡️ Moderator

**Focused on community moderation and safety**

**Capabilities**:

-   Issue warnings, mutes, and bans within the hub
-   Delete inappropriate messages
-   View moderation logs and user infractions
-   Handle user appeals and reports
-   Moderate cross-server interactions

**Limitations**:

-   Cannot change hub configuration
-   Cannot manage other staff members
-   Cannot access sensitive settings or analytics

## Managing Staff

### Accessing Staff Management

<Steps>
<Step title="Open Hub Configuration">
Run `/hub configure` in a connected server where you have Manager+ permissions.
</Step>

<Step title="Select Staff Management">
    Choose **"Hub Team Management"** from the configuration dropdown.
</Step>

<Step title="View Current Team">
See all current staff members with their roles and permissions.
</Step>
</Steps>

### Adding Staff Members

<Steps>
<Step title="Select User">
Use the user dropdown to select someone from the current server or type their User ID.
</Step>

<Step title="Choose Role">
    Select the appropriate permission level: - **Moderator**: For community
    moderation - **Manager**: For hub administration (Owner only)
</Step>

<Step title="Confirm Addition">
Click "Confirm" to add the user to your hub staff.
</Step>
</Steps>

<Frame>
    [IMAGE: Staff management interface showing user selection and role
    assignment]
</Frame>

### Removing Staff Members

<Steps>
<Step title="Select Existing Staff">
Choose the staff member you want to modify from the user dropdown.
</Step>

<Step title="Choose Remove">
    Select "Remove" from the role dropdown to revoke their permissions.
</Step>

<Step title="Confirm Removal">
Click "Confirm" to remove them from the hub staff.
</Step>
</Steps>

### Promoting/Demoting Staff

<Steps>
<Step title="Select Staff Member">
Choose an existing staff member from the dropdown.
</Step>

<Step title="Select New Role">
    Choose their new permission level: - **Promote to Manager** (Owner only) -
    **Demote to Moderator** (Owner only) - **Remove from team** (Manager+)
</Step>

<Step title="Confirm Change">
The user's permissions will update immediately.
</Step>
</Steps>

## Permission Matrix

| Action                       | Owner | Manager | Moderator |
| ---------------------------- | ----- | ------- | --------- |
| **Configuration**            |
| Edit hub name/description    | ✅    | ✅      | ❌        |
| Toggle NSFW/Private settings | ✅    | ✅      | ❌        |
| Configure modules            | ✅    | ✅      | ❌        |
| Setup logging                | ✅    | ✅      | ❌        |
| **Staff Management**         |
| Add/remove moderators        | ✅    | ✅      | ❌        |
| Add/remove managers          | ✅    | ❌      | ❌        |
| Transfer ownership           | ✅    | ❌      | ❌        |
| **Moderation**               |
| Warn users                   | ✅    | ✅      | ✅        |
| Mute users                   | ✅    | ✅      | ✅        |
| Ban users                    | ✅    | ✅      | ✅        |
| Delete messages              | ✅    | ✅      | ✅        |
| Handle appeals               | ✅    | ✅      | ✅        |
| **Advanced**                 |
| View analytics               | ✅    | ✅      | ❌        |
| Manage integrations          | ✅    | ❌      | ❌        |
| Delete hub                   | ✅    | ❌      | ❌        |

## Ownership Transfer

<Warning>
    **Ownership transfer is permanent and irreversible!** The new owner will
    have complete control over the hub, and you will lose all owner privileges.
</Warning>

### Transfer Process

<Steps>
<Step title="Access Transfer Option">
In hub configuration, select **"Ownership Transfer"** (Owner only).
</Step>

<Step title="Review Warning">
    Read the permanent warning about the irreversible nature of the transfer.
</Step>

<Step title="Select New Owner">
    Choose the user who will become the new hub owner from the dropdown.
</Step>

<Step title="Double Confirmation">
    Confirm twice that you want to permanently transfer ownership.
</Step>

<Step title="Transfer Complete">
The selected user immediately becomes the owner, and you lose owner privileges.
</Step>
</Steps>

### When to Transfer Ownership

**Good reasons to transfer**:

-   Stepping down from community leadership
-   Passing hub to a more active leader
-   Original owner becoming inactive
-   Community voted for new leadership

**Consider alternatives first**:

-   Adding more managers to share workload
-   Taking a temporary break while keeping ownership
-   Promoting a manager to handle daily operations

## Common Scenarios

### Handling Staff Issues

**Inactive Staff**:

-   Set activity expectations
-   Regular check-ins required
-   Remove after extended absence

**Overstepping Permissions**:

-   Clear communication about boundaries
-   Temporary demotion if necessary
-   Retraining on proper procedures

**Staff Conflicts**:

-   Owner/Manager mediation
-   Focus on hub health over personal issues

<Tip>
    Start with a small, trusted team and grow gradually. It's easier to add
    staff than to deal with problems from over-staffing too quickly.
</Tip>

<Warning>
    Staff members have significant power over your hub. Choose carefully and
    monitor their actions, especially during their initial period.
</Warning>

## Troubleshooting

<AccordionGroup>
<Accordion title="Can't promote someone to Manager">
    - Only Owners can promote users to Manager
    - Ensure you have Owner permissions
    - Check that the user isn't already a Manager
    - Verify the user is active on Discord
</Accordion>

<Accordion title="Staff member not showing in list">
    - Refresh the hub configuration panel
    - Ensure they've accepted the role
    - Check if they left all connected servers
    - Verify they didn't block the bot
</Accordion>

<Accordion title="Permissions not working properly">
    - Wait a few minutes for permissions to sync - Check for any server-specific
    permission overrides - Contact support if issues persist
</Accordion>

<Accordion title="Accidental staff removal">
- Re-add the user following the normal process
- Their previous actions and history remain
- You may need to communicate the temporary change
- Consider reviewing your selection process
</Accordion>
</AccordionGroup>
