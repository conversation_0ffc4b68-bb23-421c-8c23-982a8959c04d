---
title: "Logging Configuration"
description: "Configure logging channels to monitor hub activity, track moderation actions, and stay informed about your community"
icon: hashtag
---

Logging configuration allows hub managers and owners to monitor their community's activity by sending automated notifications to designated channels. This helps maintain transparency, track moderation actions, and stay informed about important events.

## Available Logging Types

<AccordionGroup>
    <Accordion title="Moderation Logs" icon="shield">
        Track all moderation actions taken within your hub, including:
        - Warns and infractions issued to users
        - Bans and unbans (both hub-specific and server-specific)
        - Message deletions and edits
        - Moderator actions and decisions
        
        **Best for**: Maintaining moderation transparency and accountability
    </Accordion>

    <Accordion title="Join/Leave Events" icon="users">
        Monitor server connections and network changes:
        - New servers joining your hub
        - Servers leaving or being removed from the hub
        - Connection status changes
        - Network membership updates
        
        **Best for**: Tracking community growth and network health
    </Accordion>

    <Accordion title="Appeals Handling" icon="scale-balanced">
        Receive notifications for community feedback:
        - New appeals submitted by users
        - Appeal status updates and decisions
        - Appeal review requests
        - Resolution notifications
        
        **Best for**: Managing user appeals and maintaining fair processes
    </Accordion>

    <Accordion title="Report Management" icon="flag">
        Stay informed about reported content and violations:
        - New reports submitted against messages or users
        - Report investigation updates
        - Report resolution outcomes
        - False report notifications
        
        **Best for**: Quickly addressing community violations
    </Accordion>

    <Accordion title="Network Alerts" icon="bell-ring">
        Important notifications from the hub:
        - Messages blocked by profanity filters
        - Filter bypass attempts
        - Content moderation statistics
        - NSFW content detection

        **Best for**: Staying informed about hub-specific issues
    </Accordion>
</AccordionGroup>

## Setting Up Logging

<Steps>
    <Step title="Access Hub Configuration">
        Use `/hub configure` to open the hub settings panel.
        
        <Note>
            You must have Owner or Manager permissions to configure logging settings.
        </Note>
    </Step>

    <Step title="Navigate to Logging Settings">
        Click the **"Logging Configuration"** button in the hub configuration panel.
        
        [IMAGE: Hub configuration panel with Logging Configuration button highlighted]
    </Step>

    <Step title="Select Logging Type">
        Choose which type of logging you want to configure from the dropdown menu:
        - Moderation Logs
        - Join/Leave Events  
        - Appeals
        - Reports
        - Network Alerts
        - Profanity Filters
        
        [IMAGE: Logging type selection dropdown menu]
    </Step>

    <Step title="Configure Channel">
        Select the channel where you want to receive logs:
        - Text channels and thread channels are supported
        - The bot must have permission to send messages in the selected channel
        - Consider using dedicated logging channels for organization
        
        <Tip>
            Create separate channels for different log types (e.g., #mod-logs, #join-logs) to keep information organized.
        </Tip>
    </Step>

    <Step title="Set Notification Role (Optional)">
        Choose a role to be mentioned when important logs are posted:
        - Moderator roles for moderation logs
        - Admin roles for network alerts
        - Community management roles for appeals/reports
        
        <Warning>
            Be mindful of role permissions to avoid unnecessary pings for routine events.
        </Warning>
    </Step>

    <Step title="Save Configuration">
        Click **"Save Settings"** to apply your logging configuration.
        
        <Check>
            You'll see a confirmation message when settings are successfully saved.
        </Check>
    </Step>
</Steps>

## Managing Logging Configuration

### Updating Existing Settings

To modify your logging configuration:

1. Use `/hub configure` and select **"Logging Configuration"**
2. Choose the logging type you want to update
3. Select new channels or roles as needed
4. Save your changes

### Disabling Logging

To disable a specific logging type:

1. Access the logging configuration for that type
2. Select **"None"** for both channel and role
3. Save the configuration

<Info>
    Disabling logging won't delete previous log messages, but new events won't be logged to that channel.
</Info>

### Multiple Logging Channels

You can configure different channels for different logging types:

<Tabs>
    <Tab title="Recommended Setup">
        ```
        #mod-logs          → Moderation Logs
        #server-activity   → Join/Leave Events
        #reports           → Reports & Appeals
        #announcements     → Network Alerts
        ```
    </Tab>
    
    <Tab title="Minimal Setup">
        ```
        #hub-logs          → All logging types
        ```
    </Tab>
    
    <Tab title="Advanced Setup">
        ```
        #mod-actions       → Moderation Logs
        #network-changes   → Join/Leave Events
        #community-reports → Reports
        #appeal-queue      → Appeals
        #hub-alerts     → Network Alerts
        #content-filter    → Profanity Filters
        ```
    </Tab>
</Tabs>

## Best Practices

<CardGroup cols={2}>
    <Card title="Channel Organization" icon="folder">
        - Use descriptive channel names (#mod-logs, #reports)
        - Create channel categories for logging channels
        - Set appropriate permissions for logging channels
        - Consider using threads for detailed discussions
    </Card>
    
    <Card title="Role Management" icon="users-gear">
        - Assign notification roles thoughtfully
        - Use different roles for different log types
        - Avoid pinging @everyone for routine logs
        - Create dedicated logging review roles
    </Card>
    
    <Card title="Permission Setup" icon="lock">
        - Ensure bot has Send Messages permission
        - Grant bot Embed Links permission for rich logs
        - Restrict logging channel access to staff
        - Review permissions regularly
    </Card>
    
    <Card title="Maintenance" icon="wrench">
        - Review logging effectiveness regularly
        - Archive old logging channels when needed
        - Update notification roles as staff changes
        - Monitor for permission issues
    </Card>
</CardGroup>

## Troubleshooting

<AccordionGroup>
    <Accordion title="Logs not appearing in channel">
        **Possible causes and solutions:**
        
        - **Bot permissions**: Ensure the bot has Send Messages and Embed Links permissions
        - **Channel selection**: Verify the correct channel is selected in configuration
        - **Channel deletion**: If the channel was deleted, reconfigure logging
        - **Server permissions**: Check that the bot hasn't been restricted by server settings
        
        **Solution**: Re-run the logging configuration and verify all permissions.
    </Accordion>

    <Accordion title="Role mentions not working">
        **Possible causes and solutions:**
        
        - **Role permissions**: Ensure the bot can mention the selected role
        - **Role position**: Bot's role must be higher than the role it's trying to mention
        - **Role deletion**: If the role was deleted, reconfigure notification settings
        
        **Solution**: Check role hierarchy and permissions, then update configuration.
    </Accordion>

    <Accordion title="Too many notifications">
        **Managing notification volume:**
        
        - Use specific channels for different log types
        - Adjust notification roles to reduce pings
        - Consider disabling low-priority logging types
        - Use threads for detailed follow-up discussions
        
        **Solution**: Review and adjust your logging configuration based on your community's needs.
    </Accordion>
</AccordionGroup>

<Note>
    Logging configuration changes take effect immediately. There's no delay or restart required for new settings to apply.
</Note>
