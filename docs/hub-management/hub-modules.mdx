---
title: "Hub Modules"
description: "Configure and manage hub feature modules for enhanced functionality"
icon: "sliders"
---

Hub modules are configurable features that enhance your hub's functionality and provide additional moderation tools. Each module can be independently enabled or disabled based on your community's needs.

## Available Modules

### 🙂 Reactions
**Purpose**: Allow reaction-based interactions on cross-server messages

When enabled, users can react to messages sent through your hub, and these reactions will be synchronized across all connected channels.

**Use cases**:
- Community feedback and voting
- Fun interactions between servers
- Message acknowledgment across hubs

**Considerations**: 
- Increases bot API usage due to reaction synchronization
- May create notification noise in very active hubs

---

### 🔗 Hide Links
**Purpose**: Hide link previews in hub messages to reduce visual clutter

When enabled, Discord's automatic link previews are suppressed in cross-server messages, creating a cleaner chat experience.

**Use cases**:
- Reducing spam from link previews
- Maintaining cleaner message appearance
- Focusing on text-based communication

**Note**: Users can still click links; only the automatic preview is hidden.

---

### 🛡️ Spam Filter
**Purpose**: Automatically filter spam and unwanted content

Enables advanced content filtering that goes beyond basic validation checks to detect and block suspicious patterns.

**Features**:
- Repetitive message detection
- Suspicious link filtering
- Pattern-based spam recognition
- Automatic temporary restrictions for repeat offenders

**Recommended for**: Large hubs or communities with frequent spam issues

---

### 🚫 Block Invites
**Purpose**: Block Discord server invites in messages

Prevents users from sending Discord server invitation links through your hub.

**What gets blocked**:
- `discord.gg/` links
- `discord.com/invite/` links
- `discordapp.com/invite/` links

**Use cases**:
- Preventing server raiding
- Maintaining focus on your community
- Reducing spam invitations

**Note**: Staff members may still send invites depending on hub permissions

---

### 👤 Use Nicknames
**Purpose**: Display user nicknames instead of usernames

When enabled, the bot will display users' server nicknames (if they have one) instead of their Discord username in cross-server messages.

**Benefits**:
- More personal identification
- Respects server-specific naming conventions
- Helps with role-playing communities

**Considerations**: May cause confusion if users have very different nicknames across servers

---

### 🔞 Block NSFW
**Purpose**: Block NSFW content even in NSFW-marked hubs

Provides an additional layer of content filtering even for hubs marked as NSFW-friendly.

**What it blocks**:
- Explicit images and attachments
- NSFW links and content
- Adult-oriented messaging patterns

**What it doesn't block**:
- Explicit GIFs or Videos
- Explicit messages

**Use cases**:
- Mixed-audience communities
- Temporary content restrictions
- Additional safety measures

---

### 🎥 Allow Videos
**Purpose**: Allow video attachments in messages

Controls whether users can send video files through your hub.

**When disabled**:
- Video attachments are blocked
- Only images and other file types are allowed
- Reduces bandwidth usage

**When enabled**:
- Full video sharing capabilities
- Higher engagement potential
- Increased server resource usage

## Configuring Modules

<Steps>
<Step title="Access Module Settings">
Run `/hub configure` and select **"Module Settings"** from the dropdown menu.
</Step>

<Step title="View Current Status">
The embed will show all available modules with their current status:
- ✅ **Enabled** modules are actively working
- ❌ **Disabled** modules are inactive
</Step>

<Step title="Select Modules to Toggle">
Use the dropdown menu to select one or more modules you want to change. You can select multiple modules to toggle them all at once.
</Step>

<Step title="Apply Changes">
Click the **"Apply Changes"** button to save your settings. The bot will confirm which modules were enabled or disabled.
</Step>
</Steps>

<Frame>
[IMAGE: Hub modules configuration interface showing toggleable options]
</Frame>

## Module Combinations

### Recommended for New Hubs
```
✅ Spam Filter
✅ Block Invites  
❌ Use Nicknames
✅ Hide Links
✅ Block NSFW
✅ Allow Videos
✅ Reactions
```

## Best Practices

### Performance Considerations
- **Reactions**: Increases API usage; consider for smaller hubs
- **Spam Filter**: Slight processing delay but worth it for large communities
- **Video Uploads**: Monitor bandwidth usage in very active hubs

### Community Guidelines
- **Block Invites**: Essential for preventing hub raids
- **Use Nicknames**: Great for tight-knit communities
- **NSFW Controls**: Always err on the side of caution

## Troubleshooting

<AccordionGroup>
<Accordion title="Module changes not taking effect">
- Verify you have Manager+ permissions in the hub
- Check that you clicked "Apply Changes" 
- Wait a few minutes for changes to propagate across all channels
- Try sending a test message to verify the change
</Accordion>

<Accordion title="Spam filter too aggressive">
- Consider temporarily disabling Spam Filter during events
- Review recent false positives with your moderation team
- Use manual moderation tools for edge cases
- Whitelist trusted users if the feature becomes available
</Accordion>

<Accordion title="Reactions not syncing">
- Ensure Reactions module is enabled in hub settings
- Check that the bot has reaction permissions in all connected channels
- Large reaction counts may take time to sync across servers
</Accordion>

<Accordion title="Video uploads still blocked">
- Verify Allow Videos module is enabled
- Check individual server upload limits and permissions
- Ensure the bot has attachment permissions in target channels
- File size limits still apply per Discord's restrictions
</Accordion>
</AccordionGroup>

## Advanced Module Usage

### Integration with Logging
Several modules work well with [logging configuration](/hub-management/logging-configuration):
- **Spam Filter** → Log to moderation channels
- **Block Invites** → Track blocked invitations  
- **NSFW Filter** → Monitor content filtering actions

### Future Enhancements
Keep an eye out for upcoming module features:
- Custom keyword filtering
- Time-based module schedules
- Channel-specific overrides
- Advanced reaction controls

<Tip>
Start with a conservative module setup and gradually enable features as your community grows and needs evolve.
</Tip>

<Warning>
Some modules may conflict with custom Discord bots in your servers. Test thoroughly and coordinate with your server administrators.
</Warning>

