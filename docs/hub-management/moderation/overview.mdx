---
title: "Moderation Overview"
description: "How moderation works across hubs: roles, infractions, and validation pipeline"
icon: "shield"
---

## Layers
1. Preventive checks (basic safety before messages spread)
2. Moderator actions (warnings & bans)
3. Appeals review layer (users can request reconsideration via `/appeal`)

## Roles
| Role | Key Abilities |
| ---- | ------------- |
| Owner | Full control, manage staff |
| Manager | Configure hub + moderate |
| Moderator | Issue warnings & bans |

## Infraction types (user view)
- WARNING: Recorded notice
- MUTE: Temporarily silence (cannot send, can read)
- BAN: Blocks participation

(Deprecated internal types are hidden from normal users.)

## Good habits
- [x] Warn first for minor issues
- [x] Keep reasons short & factual
- [x] Revoke mistakes quickly
- [x] Review patterns, not single incidents



<CardGroup cols={2}>
<Card title="Moderator Commands" href="/hub-management/moderation/moderator-commands" icon="terminal" />
<Card title="Developer: Infractions Model" href="/development/infractions-model" icon="list" />
<Card title="Appeals" href="/user-guide/appeals" icon="undo" />
</CardGroup>
