---
title: "Filtering"
description: "How automated word & content filters help keep hubs safe."
icon: "filter"
---

## Purpose
Filters catch obvious rule violations (slurs, disallowed terms, scam links) before they spread to all connected servers.

## What happens on a match
Depending on configuration a message may:
- Be blocked
- Generate a moderator alert
- Trigger a warning
- (Severe) Trigger a ban

<Tip>
Keep filter lists focused—too many broad terms cause false positives and extra moderator load.
</Tip>

## Handling false positives
| Situation | What to do |
| --------- | ---------- |
| Legitimate message blocked | Remove or narrow the offending term |
| Excessive alerts | Downgrade action level |
| Sudden spike in bans | Review recent filter edits |

## Moderator workflow
1. User reports or system blocks a message
2. Moderator reviews context
3. Adjust filter list if needed
4. Issue or revoke infractions where appropriate

<Info>
Technical implementation (enums, escalation logic, performance considerations) is documented separately.
</Info>

<CardGroup cols={2}>
<Card title="Moderator Commands" href="/hub-management/moderation/moderator-commands" icon="terminal" />
<Card title="Developer: Filtering System" href="/development/filtering-system" icon="filter" />
</CardGroup>
