---
title: "Infractions Lifecycle"
description: "Data model, statuses, and best practices for hub infractions"
icon: "triangle-exclamation"
---

## What an infraction does
- WARNING: Recorded note about behavior; future issues can escalate
- BAN: Blocks the user or server from sending messages in the hub

## Typical flow
1. Moderator issues WARNING or BAN using `/infractions`
2. Action becomes visible in the infractions panel
3. Moderators can revoke if mistaken
4. User may submit an appeal via `/appeal` (bans / mutes)
5. Moderators review and accept/reject; status updates accordingly

<Tip>
Use warnings for first minor issues; reserve bans for repeated or severe violations.
</Tip>

## Managing infractions
- Open `/infractions` for a paginated list
- Select an entry to view details or revoke
- Keep reasons concise and clear (e.g. "Spam links", "Harassment")

## Good practices
<Checklist>
- Start with a warning when appropriate
- Escalate only after pattern of abuse
- Revoke mistakes quickly
- Review long-term bans occasionally
</Checklist>

<Info>
Need the technical data model (indexes, enums, lifecycle diagram)? See the developer reference.
</Info>

<CardGroup cols={2}>
<Card title="Moderator Commands" href="/hub-management/moderation/moderator-commands" icon="terminal" />
<Card title="Developer: Infractions Model" href="/development/infractions-model" icon="list" />
<Card title="Appeals" href="/user-guide/appeals" icon="undo" />
</CardGroup>
