---
title: "Moderator Commands"
description: "Using the infractions interface to warn and ban users or servers"
icon: "gavel"
---

## Access
You need a hub moderator (or higher) role.

## Using /infractions
Open the panel to:
- View recent actions
- Filter by user or server (UI options provided)
- Issue WARNING or BAN
- Revoke a mistaken action

## Issuing
<Steps>
<Step title="Open panel">Run `/infractions`.</Step>
<Step title="Select target">Choose user or server.</Step>
<Step title="Pick type & reason">Keep reason short.</Step>
<Step title="Confirm">Action appears in list.</Step>
</Steps>

## Revoking
Select an entry and choose revoke. The user/server can participate again immediately if it was a ban.

## Tips
- Prefer warning first for low severity
- Escalate only on repeat behavior
- Consistent wording helps later review

<Info>
Technical storage & indexing details: see developer Infractions Model.
</Info>

<CardGroup cols={2}>
<Card title="Overview" href="/hub-management/moderation/overview" icon="shield" />
<Card title="Developer: Infractions Model" href="/development/infractions-model" icon="list" />
</CardGroup>
