---
title: "Create a Hub"
description: "How to create a new InterChat hub and connect your first channels"
icon: "square-plus"
---

<Info>
A hub is a collection of Discord channels across multiple servers. Messages sent in one connected channel are broadcast to all others using webhooks.
</Info>

## What you need
- A Discord server where you have Manage Guild permission
- InterChat bot invited to that server
- A channel dedicated (or suitable) for cross-server messages

## Permission model
Owners can configure everything. They can promote Managers (broad config) and Moderators (infractions & basic controls). These map to internal permission checks in the code (`HubPermissionLevel`).

<Tip>
Keep a private staff channel for moderation actions & logs separate from the public hub channel.
</Tip>

## Steps: Create your hub (two paths)
<Steps>
<Step title="Guided: Use /setup">
Run `/setup` and choose Create a Hub. Fill in name, brief and description, then confirm. You’ll get next steps and tips.
</Step>
<Step title="Direct: Use /hub create">
Run `/hub create` to open the creation panel and follow the on-screen prompts.
</Step>
<Step title="Connect your first channel">
Use `/connect hub:<name>` in your chosen channel to create the webhook and link the hub.
</Step>
</Steps>

## Connecting additional channels
Repeat the configure/connect step in other servers (you need appropriate permissions + bot invited). Each channel gets its own webhook for outbound messages.

<Warning>
Do NOT reuse a single hub for unrelated topics. Fragmented moderation and noise will reduce quality.
</Warning>

## Verifying broadcast works
1. Send a short test message (under 50 chars)
2. Confirm it appears in the other connected channel(s) with the same author name & avatar (via webhook)
3. If it fails, check Troubleshooting > Webhook issues

<Frame>
[IMAGE: Hub creation dialog showing form fields]
</Frame>

## Next: Configure settings
Move on to Hub Configuration to adjust moderation, locks, and presentation.

<CardGroup cols={2}>
<Card title="Configure Hub" href="/hub-management/hub-configuration" icon="gear">
Manage locks, descriptions, and permissions.
</Card>
<Card title="Hub Settings Deep Dive" href="/hub-management/hub-settings" icon="sliders">
Fine-tune advanced behavior & safety.
</Card>
</CardGroup>
