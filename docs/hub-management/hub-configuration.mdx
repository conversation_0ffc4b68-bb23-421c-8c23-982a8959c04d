---
title: "Hub Configuration"
description: "Manage core hub settings, channel connections, and access controls"
icon: "gear"
---

## Accessing configuration

Run `/hub configure` for a hub where you have sufficient hub permission (Owner or Manager). The bot returns an interactive embed message.

## Editable properties

-   **General Settings**: Name, description, NSFW rating, visibility, and welcome messages
-   **Staff Management**: Add managers and moderators, configure permissions
-   **Module Settings**: Enable/disable hub features like spam filtering, reaction support, etc.
-   **Logging Configuration**: Set up logging channels and notification roles for different hub events

<CardGroup cols={2}>
    <Card title="General Settings" href="/hub-management/general-settings" icon="gear">
        Configure basic hub properties and welcome messages.
    </Card>
    <Card title="Staff Management" href="/hub-management/staff-management" icon="users">
        Manage permissions, add moderators, and transfer ownership.
    </Card>
</CardGroup>

<Tip>
    Lock the hub before large-scale changes to prevent inconsistent state during
    channel removal.
</Tip>

## Permissions & roles

Hub permission tiers are enforced in the backend. Attempting restricted actions returns a permissions error embed.

| Level     | Can Configure | Can Moderate | Can Manage Staff                     |
| --------- | ------------- | ------------ | ------------------------------------ |
| Owner     | Yes           | Yes          | Yes                                  |
| Manager   | Most          | Yes          | Limited (cant manage other Managers) |
| Moderator | No            | Yes          | No                                   |

<Frame>[IMAGE: Hub configuration panel with editable fields]</Frame>

## Locking the hub

Temporarily pause outbound messages (maintenance, raids, large configuration changes). Unlock to resume normal chatting.

<Steps>
    <Step title="Lock">
        Use `/hub configure` and click on `General Settings`.
    </Step>
    <Step title="Lock Hub">
        Select "Lock Hub" from the dropdown. This will announce the lock to all
        connected channels and block messages from everyone aside from hub
        staff.
    </Step>
    <Step title="Unlock">Toggle again when ready.</Step>
</Steps>

<Warning>Locking doesn't delete already-sent messages.</Warning>

## Hub Modules

Hubs can have various modules enabled or disabled to customize their functionality:

- **Spam Filtering**: Automatically detect and filter out spam messages
- **Reaction Support**: Allow users to react to cross-server messages
- **Link Controls**: Hide previews or block certain types of links
- **Content Filtering**: Block NSFW content or Discord invites
- **Display Options**: Use nicknames instead of usernames

<CardGroup cols={3}>
    <Card
        title="Available Modules"
        href="/hub-management/hub-modules#available-modules"
        icon="sliders"
    >
        Learn about all hub modules and their purposes.
    </Card>
    <Card
        title="Configure Modules"
        href="/hub-management/hub-modules#configuring-modules"
        icon="gear"
    >
        Step-by-step guide to enable and disable modules.
    </Card>
    <Card
        title="Best Practices"
        href="/hub-management/hub-modules#best-practices"
        icon="lightbulb"
    >
        Recommended module combinations for different community types.
    </Card>
</CardGroup>

## Logging Configuration

Hub managers and owners can configure dedicated logging channels to monitor hub activity and receive notifications for important events:

- **Moderation Logs**: Track warns, bans, and other moderation actions
- **Join/Leave Events**: Monitor server connections and disconnections  
- **Appeals & Reports**: Handle community feedback and violations
- **Network Alerts**: Stay informed about platform-wide issues

<CardGroup cols={3}>
    <Card
        title="Logging Types"
        href="/hub-management/logging-configuration#available-logging-types"
        icon="list"
    >
        Learn about all available logging channels.
    </Card>
    <Card
        title="Setup Logging"
        href="/hub-management/logging-configuration#setting-up-logging"
        icon="gear"
    >
        Configure channels and notification roles.
    </Card>
    <Card
        title="Best Practices"
        href="/hub-management/logging-configuration#best-practices"
        icon="lightbulb"
    >
        Tips for organizing and maintaining logs.
    </Card>
</CardGroup>

<Tip>
    Use dedicated logging channels to keep your main channels organized.
    Consider creating separate channels for different types of logs (e.g.,
    #mod-logs, #join-logs, #reports).
</Tip>

<Warning>
    Make sure the bot has permission to send messages and embeds in your logging
    channels, or logs will fail to deliver.
</Warning>

## Best practices

-   **Clear Descriptions**: Use clear descriptions to aid discovery and set expectations
-   **Lean Staff**: Keep staff list focused on active, trusted members
-   **Channel Management**: Remove inactive channels to reduce broadcast overhead
-   **Regular Testing**: Periodically test messages to ensure all webhooks are live
-   **Security**: Review permissions and settings regularly

<CardGroup cols={3}>
    <Card title="Create a Hub" href="/hub-management/creating-hubs" icon="plus">
        Starting from scratch? Read the creation guide.
    </Card>
    <Card
        title="Advanced Settings"
        href="/hub-management/hub-settings"
        icon="sliders"
    >
        Fine-tune locking, safety, and performance.
    </Card>
    <Card
        title="Moderation Overview"
        href="/hub-management/moderation/overview"
        icon="shield"
    >
        Understand infractions, filters, and safety tools.
    </Card>
</CardGroup>
