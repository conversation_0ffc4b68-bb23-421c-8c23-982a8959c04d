---
title: "Developer Introduction"
description: "Getting started with InterChat development and contribution"
icon: "code"
---

## Project Overview

InterChat is a Discord bot written in Python that enables cross-server communication through "hubs"—federated channels where messages are broadcast via webhooks. The bot handles millions of messages across thousands of servers with a focus on performance, reliability, and user safety.

### Key Technologies
- **discord.py**: Modern Discord API wrapper with hybrid commands
- **SQLAlchemy**: Async ORM for PostgreSQL data persistence  
- **Redis**: Caching and rate limiting
- **Sentry**: Error tracking and performance monitoring
- **Alembic**: Database migrations
- **Playwright**: Web scraping and HTML generation

## Architecture Philosophy

### Modular Design
Commands are organized into cogs by function:
- `modules/` - Core user features (hubs, users, moderation)
- `events/` - Discord event handlers (messages, guild joins)
- `public/` - General commands (help, stats, about)
- `staff/` - Administrative tools
- `developer/` - Development utilities

### Database-First Approach
All state is persisted in PostgreSQL with proper indexing for performance. The bot is stateless and can restart without losing data or functionality.

### Webhook-Centric Broadcasting
Messages are delivered via Discord webhooks to preserve sender identity. Webhook lifecycle management is critical for reliability.

## Development Values

### Safety & Reliability
- Account age restrictions (7+ days)
- Multi-layer validation before broadcast
- Comprehensive error handling with Sentry integration
- Rate limiting to prevent abuse

### Performance at Scale
- Async throughout with proper session management
- Indexed database queries with pagination
- Efficient webhook reuse and cleanup
- Redis caching for hot paths

### User Experience
- Hybrid commands work in both slash and prefix modes
- Consistent UI patterns with Discord Views/Selects
- Localization support (YAML-based)
- Clear error messages and feedback

## Contribution Areas

### Code Contributions
- Bug fixes and performance improvements
- New features aligned with the roadmap
- Testing and quality assurance
- Documentation improvements

### Localization
- Translation of bot messages to new languages
- Cultural adaptation of features
- Testing translated interfaces

### Community Support
- Help in the support server
- Documentation feedback
- Feature suggestions and use case validation

## Getting Started

Ready to contribute? The setup guide covers environment configuration, database initialization, and development workflow.

<CardGroup cols={2}>
<Card title="Development Setup" href="/development/setup" icon="code">
Configure your development environment.
</Card>
<Card title="Architecture Deep Dive" href="/development/architecture" icon="building">
Understand the system design.
</Card>
</CardGroup>

## Resources

### External Links
- [GitHub Repository](https://github.com/interchatapp/InterChat.py)
- [Discord Support Server](https://www.interchat.tech/support)
- [Bot Invite Link](https://discord.com/oauth2/authorize?client_id=769921109209907241&permissions=8&scope=bot%20applications.commands)

### Community Guidelines
- Follow the code of conduct in all interactions
- Test thoroughly before submitting pull requests
- Document new features and breaking changes
- Respect user privacy and data protection requirements

<Info>
InterChat is open source but requires approval for major architectural changes. Join the support server to discuss significant contributions.
</Info>
