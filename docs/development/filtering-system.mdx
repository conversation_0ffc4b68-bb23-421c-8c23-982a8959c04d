---
title: "Filtering System"
description: "Blocked word actions, escalation logic, and performance notes"
icon: "filter"
---

## Block Word Actions Enum
| Action | Effect |
| ------ | ------ |
| BLOCK_MESSAGE | Prevents broadcast; user sees failure notice |
| SEND_ALERT | Notifies moderators (no block) |
| WARN | Issues automatic WARNING infraction |
| MUTE | Temporary communication block (requires expiry processing) |
| BAN | Immediate BAN infraction |
| BLACKLIST | Legacy synonym retained for older rows |

## Execution Flow
1. Tokenize / normalize message
2. Match against blocklist (exact / pattern)
3. Resolve first matching rule (priority ordering)
4. Execute action (may create infraction)
5. Log event & optionally notify staff

## Performance Considerations
- Cache compiled patterns in memory / Redis
- Batch lookups to reduce DB round trips
- Short-circuit on first definitive block action

## Escalation Strategy
| Violations in Window | Action |
| -------------------- | ------ |
| 1 (minor) | WARN |
| 2-3 | WARN + notify staff |
| 4+ | BAN (subject to moderator review) |

## Data Storage
- Blocklist definitions stored centrally (table or config file)
- Actions executed atomically to prevent race conditions

## Testing
Create synthetic messages covering:
- No match path
- Multiple matches (ensure priority)
- High-frequency spam scenario
- Legacy BLACKLIST handling

<CardGroup cols={2}>
<Card title="Infractions Model" href="/development/infractions-model" icon="list" />
<Card title="Message Validation" href="/development/message-validation" icon="shield" />
</CardGroup>
