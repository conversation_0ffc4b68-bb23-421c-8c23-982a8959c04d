---
title: "Message Validation Pipeline"
description: "Pre-broadcast validation stages and failure semantics"
icon: "shield-halved"
---

## Stages
| Order | Check | Failure Result |
| ----- | ----- | -------------- |
| 1 | Length ≤ 2000 | Reject message with length notice |
| 2 | Hub not locked | Inform sender hub under maintenance |
| 3 | Account age ≥ 7 days | Block & instruct to wait |
| 4 | Active user/server BAN | Block silently or with ban notice |
| 5 | Global blacklist | Block and flag for review |
| 6 | Filtering system | Invoke block word / escalation logic |

## Data Inputs
- Hub state (locked flag)
- User & server infraction status
- Account creation timestamp
- Cached blacklist/filter artifacts

## ValidationResult Structure
Relevant fields returned:
- is_valid (bool)
- reason (string for logging)
- should_notify (bool)
- notification_message (audience: staff)
- infraction_id (linked when auto-issued)

## Concurrency & Ordering
- Checks execute sequentially; early failure short-circuits remaining stages
- Expensive operations (DB lookups) cached when safe
- Time-based rules (account age) computed once per message

## Error Handling
- Unexpected exceptions default to safe-fail (block + log)
- Rate limited endpoints retried with exponential backoff if needed

## Metrics
Track per stage:
- Pass / fail counts
- Average latency
- Top failure reasons

## Optimization Opportunities
- Introduce parallel fetch for user + server infraction state
- Bloom filter for fast blacklist pre-check
- Structured caching for account age & hub lock snapshot

<CardGroup cols={2}>
<Card title="Filtering System" href="/development/filtering-system" icon="filter" />
<Card title="Infractions Model" href="/development/infractions-model" icon="list" />
</CardGroup>
