---
title: "Infractions Data Model"
description: "Schema, indexing strategy, and lifecycle for infractions"
icon: "database"
---

## Table Structure

Essential fields:

-   id (string nanoid)
-   hubId (FK Hub)
-   moderatorId (FK User)
-   userId / serverId (one populated)
-   type (ENUM: BAN | WARNING | BLACKLIST legacy)
-   status (ENUM: ACTIVE | REVOKED | APPEALED)
-   reason (string)
-   expiresAt (nullable DateTime)
-   notified (boolean)
-   createdAt / updatedAt (timestamps)

## Enum Notes

-   BLACKLIST kept for backward compatibility, treat as BAN in logic.
-   WARNING does not block broadcast, BAN does.

## Indexes

| Index                                | Purpose                             |
| ------------------------------------ | ----------------------------------- |
| status+hubId                         | Filter active infractions for a hub |
| userId_hubId_status_type_expiresAt   | Fast user scope pagination          |
| serverId_hubId_status_type_expiresAt | Fast server scope pagination        |
| type                                 | Analytics / breakdown               |
| expiresAt                            | Expiry queue scans                  |

<Info>
    Partial composite indexes optimize lookups only when userId/serverId is not
    NULL.
</Info>

## Lifecycle

1. Insert ACTIVE row
2. Enforcement checks on every message
3. Status updated (REVOKED/APPEALED)
4. Optional expiry logic triggers

## Query Patterns

-   Recent infractions: filter hubId + status + order by createdAt DESC limit N
-   Active bans for user: hubId + userId + status=ACTIVE + type=BAN
-   Expiring soon: expiresAt < now()+interval

## Migration Strategy

Additive changes use Alembic autogenerate; review index creation manually for performance alignment.

## Best Practices

-   Keep reasons short (under 120 chars)
-   Revoke erroneous bans quickly
-   Periodically prune expired rows

<CardGroup cols={2}>
    <Card
        title="Filtering System"
        href="/development/filtering-system"
        icon="filter"
    />
    <Card
        title="Message Validation"
        href="/development/message-validation"
        icon="shield"
    />
</CardGroup>
