---
title: "Architecture Overview"
description: "Deep dive into InterChat's system design and core components"
icon: "layer-group"
---

## System Overview

InterChat follows a modular, event-driven architecture designed for high availability and horizontal scaling.

<Frame>
    [IMAGE: Architecture diagram showing Discord → Bot → Validation → Database →
    Webhook Broadcasting]
</Frame>

## Core Components

### Bot Instance (`main.py`)

The central `Bot` class extends `commands.AutoShardedBot`:

-   **Initialization**: Sets up intents, database connections, and emoji manager
-   **Lifecycle Management**: Handles startup, shutdown, and automatic reconnection
-   **Staff Sync**: Loads staff IDs from the development guild for permission checks
-   **Hot Reloading**: Uses `cogwatch` for development-time cog reloading

```python
class Bot(commands.AutoShardedBot):
    def __init__(self):
        # Intent configuration for message content and member access
        intent = discord.Intents.default()
        intent.message_content = True
        intent.members = True
```

### Message Broadcasting Pipeline

#### 1. Event Reception (`cogs/events/onMessage.py`)

-   Listens to `on_message` events
-   Filters bot messages and validates channel connections
-   Initiates validation and broadcasting flow

#### 2. Validation Layer (`utils/modules/broadcast/checks.py`)

Multi-stage validation ensures message safety:

```python
class ValidationResult(NamedTuple):
    is_valid: bool
    reason: Optional[str] = None
    should_notify: bool = False
    notification_message: Optional[str] = None
```

**Validation Stages**:

-   **Length Check**: Maximum 2000 characters (Discord limit)
-   **Hub Status**: Verify hub is not locked
-   **Account Age**: Minimum 7 days to prevent throwaway abuse
-   **Infractions**: Check for active bans (user and server level)
-   **Global Blacklists**: System-wide blocks for severe violations

#### 3. Webhook Management (`utils/modules/core/webhookCore.py`)

-   **Creation**: Auto-creates webhooks in connected channels
-   **Validation**: Verifies webhook integrity before broadcast
-   **Cleanup**: Removes stale webhooks when connections are deleted
-   **Thread Support**: Handles parent channel webhook resolution for threads

#### 4. Broadcasting Engine

-   **Parallel Dispatch**: Sends to all connected channels simultaneously
-   **Rate Limiting**: Respects Discord API limits per webhook
-   **Error Handling**: Gracefully handles failed deliveries
-   **Audit Trail**: Records all broadcasts in database

## Database Architecture

### Core Models (`utils/modules/core/db/models.py`)

#### Hub Ecosystem

```python
class Hub(Base):
    # Hub metadata and configuration
    name: str
    description: str
    ownerId: str
    locked: bool = False

    # Relationships
    connections: List['Connection']
    infractions: List['Infraction']
    moderators: List['HubModerator']
```

#### Connection Management

```python
class Connection(Base):
    # Links Discord channels to hubs
    channelId: str
    hubId: str
    webhookUrl: str  # For message broadcasting
    serverId: str

    # Thread support
    parentId: Optional[str]  # For thread channels
```

#### User & Server Tracking

```python
class User(Base):
    # User profiles and statistics
    id: str  # Discord user ID
    messageCount: int
    voteCount: int

    # Privacy and preferences
    showBadges: bool = True
    locale: str = 'en'
```

### Indexing Strategy

Critical indexes for performance:

-   **Infraction Lookups**: `(userId, hubId, status, type, expiresAt)`
-   **Connection Queries**: `(channelId)`, `(hubId)`
-   **Message Pagination**: `(createdAt)` with DESC ordering

## Rate Limiting & Performance

### Multi-Layer Rate Limiting

1. **User Commands**: Prevents command spam per user
2. **Webhook Broadcasting**: Respects Discord API limits
3. **Database Operations**: Connection pooling and query optimization
4. **Redis Caching**: Hot path data and validation results

### Async Patterns

-   **Session Management**: Proper async context managers for database
-   **Concurrent Broadcasting**: Parallel webhook dispatch
-   **Background Tasks**: Non-blocking operations where possible

```python
async with self.bot.db.get_session() as session:
    # All database operations in session scope
    await session.commit()
```

## Error Handling & Observability

### Sentry Integration

Production deployments use Sentry for:

-   Exception tracking with context
-   Performance monitoring
-   User experience insights

### Logging Strategy

```python
logger.info('Normal operation')
logger.warning('Recoverable issue')
logger.error('Intervention needed')
logger.critical('System failure')
```

### Graceful Degradation

-   Webhook failures don't break other channels
-   Database timeouts use cached data when possible
-   Bot restarts preserve connection state

## Security Model

### Permission Layers

1. **Discord Permissions**: Server-level role checks
2. **Hub Permissions**: Owner/Manager/Moderator hierarchy
3. **Global Staff**: InterChat team overrides

### Data Protection

-   **PII Handling**: Minimal storage, encryption in transit
-   **Rate Limiting**: Prevents abuse and DoS
-   **Validation Pipeline**: Multi-stage content filtering

### Audit Trail

All moderation actions are logged with:

-   Actor identification
-   Target (user/server)
-   Timestamp and reason
-   Action type and status

<CardGroup cols={2}>
    <Card title="Development Setup" href="/development/setup" icon="code">
        Get your environment configured.
    </Card>
    <Card title="Introduction" href="/development/introduction" icon="book">
        Learn about project goals.
    </Card>
</CardGroup>

## Extension Points

### Adding New Commands

1. Create cog in appropriate `cogs/` subdirectory
2. Use `@commands.hybrid_command` for dual functionality
3. Include proper permission checks and error handling
4. Add to documentation

### Custom Validation Rules

Extend `MessageValidator` class for new content filtering requirements.

### Event System Integration

Use `event_dispatcher` for hub-specific notifications and logging.

<Info>
    The codebase prioritizes maintainability and safety over performance
    micro-optimizations. Always consider the impact on user experience and
    system reliability.
</Info>
