---
title: "Development Setup"
description: "Complete environment setup for InterChat development"
icon: "box-open"
---

## Prerequisites

### Required Software
- **Python 3.11+**: InterChat uses modern async features
- **PostgreSQL 14+**: Primary database
- **Dragonfly 1.32+**: Caching and rate limiting
- **Git**: Version control

<Info>
If you prefer an alternative Redis-compatible cache, you can substitute it, but Dragonfly is the default tested target.
</Info>

### Discord Application
1. Create a Discord application at [Discord Developer Portal](https://discord.com/developers/applications)
2. Create a bot user and copy the token
3. Enable all Privileged Gateway Intents (Message Content, Server Members, Presence)
4. Generate an OAuth2 invite URL with `bot` and `applications.commands` scopes

## Environment Configuration

### Clone Repository
```bash
git clone https://github.com/interchatapp/InterChat.py.git
cd InterChat.py
```

### Python Environment
<Tabs>
<Tab title="Using venv">
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# venv\Scripts\activate    # Windows
pip install -r requirements.txt
```
</Tab>

<Tab title="Using conda">
```bash
conda create -n interchat python=3.11
conda activate interchat
pip install -r requirements.txt
```
</Tab>
</Tabs>

### Environment Variables
Create `.env` file in the project root. Copy over the contents of `.env.example` and fill in the values.

<Warning>
Never commit your `.env` file to version control. Keep your Discord token secure.
</Warning>

## Database Setup

### Database Installation via Docker
```bash
# Use the provided Docker Compose for development
docker-compose -f dev/docker-compose.yml up -d postgres dragonfly
```

### Database Initialization
```bash
# Create development database
createdb interchat_dev

# Run migrations to create tables
alembic upgrade head
```

## Development Workflow

### Running the Bot
```bash
# Start in development mode with hot reloading
python main.py
```

The bot will:
- Load all cogs automatically
- Enable cogwatch for hot-reloading during development
- Set presence to "In development"
- Skip Sentry initialization (unless in production)

### Creating Database Migrations
When you modify SQLAlchemy models:

```bash
# Generate migration file
alembic revision --autogenerate -m "Description of changes"

# Review the generated migration in alembic/versions/
# Apply the migration
alembic upgrade head
```

### Testing Changes
<Steps>
<Step title="Invite test bot">
Use your development bot invite URL in a test server where you have admin permissions.
</Step>
<Step title="Test commands">
Verify slash commands register and function correctly.
</Step>
<Step title="Test message broadcasting">
Create a test hub and connect channels to verify the core functionality.
</Step>
</Steps>

## Development Tools

### Code Quality
```bash
# Format code (configured in pyproject.toml)
ruff format .

# Lint code
ruff check .
```

### Debugging
- Use `jishaku` cog for live debugging (`>jsk` commands)
- Check logs in console for errors and performance info
- Use Discord's Developer Mode to copy IDs easily

### Hot Reloading
The `cogwatch` decorator automatically reloads cogs when files change:
```python
@watch(path='cogs', preload=False)
async def on_ready(self):
    # Bot ready event
```

## Troubleshooting

### Common Issues

#### Bot won't start
- Verify Discord token is correct
- Check database connection string
- Ensure all required environment variables are set

#### Database connection errors
- Confirm PostgreSQL is running
- Verify database name and credentials
- Check if migrations are up to date: `alembic current`

#### Commands not appearing
- Ensure bot has `applications.commands` scope
- Try `>jsk sync` to force command sync
- Check bot permissions in test server

#### Module import errors
- Verify you're in the correct virtual environment
- Reinstall requirements: `pip install -r requirements.txt --upgrade`

<CardGroup cols={2}>
<Card title="Architecture" href="/development/architecture" icon="building">
Understand the codebase structure.
</Card>
<Card title="Introduction" href="/development/introduction" icon="book">
Learn about the project philosophy.
</Card>
</CardGroup>
