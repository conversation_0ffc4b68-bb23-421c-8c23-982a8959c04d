---
title: "Your First Connection"
description: "Step-by-step walkthrough for making your first hub connection and understanding cross-server communication"
icon: "link"
---

## Introduction

Making your first connection to an InterChat hub is an exciting moment - you're about to join a global community of Discord servers! This guide will walk you through the entire process, from choosing the right hub to sending your first cross-server message.

<Info>
This tutorial assumes you've already [added InterChat to your server](/getting-started/quickstart) and have the necessary permissions.
</Info>

## Step 1: Understanding Hubs

Before connecting, let's understand what hubs are and how they work:

### What is a Hub?

<Frame>
<img src="/images/hub-diagram.png" alt="Diagram showing multiple Discord servers connected through an InterChat hub" />
</Frame>

A **hub** is like a virtual group that spans multiple Discord servers. When you connect your channel to a hub:

- **Messages sent** in your channel appear in all other connected channels
- **Messages from other servers** appear in your channel
- **Your community joins** a larger network of servers with similar interests

### Types of Hubs

<Tabs>
<Tab title="Public Hubs">
**Open to everyone**
- Easy to join with `/connect`
- Large, active communities
- Broad topics like "general chat" or "gaming"
- Great for getting started
</Tab>

<Tab title="Invite-Only Hubs">
**Require permission to join**
- Smaller, curated communities
- Specific requirements or applications
- Often more focused topics
- Higher quality discussions
</Tab>
</Tabs>

## Step 2: Exploring Available Hubs

Let's find the perfect hub for your first connection:

### Browse & Discover Hubs

<CodeGroup>
```bash View Public Hubs (in Discord)
/hubs
```

```bash Visit Web Directory (filters & details)
https://interchat.tech/hubs
```
</CodeGroup>

### Recommended Starter Hubs

For your first connection, consider these beginner-friendly hubs:

<CardGroup cols={2}>
<Card title="🌍 Global Chat" icon="globe">
  **Name:** `global-chat`
  **Members:** 500+ servers
  **Activity:** High
  **Language:** English

  Perfect for general conversations and meeting people worldwide.
</Card>

<Card title="🆘 Help & Support" icon="question">
  **Name:** `help-support`
  **Members:** 200+ servers
  **Activity:** Medium
  **Language:** English

  Ask questions and help others with Discord, bots, and server management.
</Card>

<Card title="🎮 Gaming General" icon="gamepad">
  **Name:** `gaming-general`
  **Members:** 300+ servers
  **Activity:** High
  **Language:** English

  Discuss games, find teammates, and share gaming experiences.
</Card>

<Card title="🎨 Creative Showcase" icon="palette">
  **Name:** `creative-showcase`
  **Members:** 150+ servers
  **Activity:** Medium
  **Language:** English

  Share artwork, get feedback, and discover amazing creations.
</Card>
</CardGroup>

## Step 3: Getting Hub Information

Before connecting, review the hub’s page on the website for description, rules, and expectations. If you have the hub name or an invite code, you can proceed to connect.

<Warning>
Always read the hub rules before connecting. Each hub may have specific guidelines for content, language, and behavior.
</Warning>

## Step 4: Preparing Your Channel

### Choose the Right Channel

Select an appropriate channel for your hub connection:

<Tabs>
<Tab title="Dedicated InterChat Channel">
**Recommended approach:**
- Create a new channel specifically for InterChat
- Name it clearly (e.g., #interchat, #global-chat)
- Set appropriate permissions
- Pin an explanation message

```markdown
📋 This channel is connected to InterChat hub: "global-chat"

Messages sent here will appear in other connected Discord servers.
Be respectful and follow both our server rules and hub guidelines.

Use /hub info to learn more about our connected community!
```
</Tab>

<Tab title="Existing Community Channel">
**Alternative approach:**
- Use an existing active channel
- Ensure members understand the change
- Update channel description
- Consider channel topic

<Warning>
Existing members might be surprised by cross-server messages. Announce the change clearly!
</Warning>
</Tab>
</Tabs>

### Set Channel Permissions

Configure permissions for optimal experience:

<Steps>
<Step title="InterChat Bot Permissions">
  Ensure InterChat has these permissions in your chosen channel:
  - ✅ Send Messages
  - ✅ Read Message History
  - ✅ Manage Messages (for moderation)
  - ✅ Create Webhooks (for message delivery)
  - ✅ Embed Links
  - ✅ Attach Files
</Step>

<Step title="Member Permissions">
  Set appropriate permissions for your server members:
  - ✅ Send Messages (unless moderated)
  - ✅ Read Message History
  - ✅ Use External Emojis (for cross-server reactions)
</Step>

<Step title="Role-Based Access (Optional)">
  For moderated communities, consider:
  - Create an "InterChat Verified" role
  - Require approval before posting
  - Set up automatic role assignment
</Step>
</Steps>

## Step 5: Making the Connection

Now for the exciting part - let's connect to your chosen hub!

### Basic Connection

<CodeGroup>
```bash Connect to Hub
/connect hub:global-chat channel:#interchat
```
</CodeGroup>

<Steps>
<Step title="Execute the Command">
  Run the command with your chosen hub and channel.

  <Check>
  You should receive a confirmation message if successful.
  </Check>
</Step>

<Step title="Verify Connection">
  Check that the connection was established:

  ```bash
  /connections list
  ```

  This shows all your server's active connections.
</Step>

<Step title="Test the Connection">
  Send a test message to ensure everything works:

  ```
  Hello from [Your Server Name]! 👋
  Excited to be part of this community!
  ```
</Step>
</Steps>

### Connection with Options

Some hubs support additional connection options:

<CodeGroup>
```bash Connect with Display Name
/connect hub:global-chat channel:#interchat display_name:"My Amazing Server"
```

```bash Connect with Webhook Customization
/connect hub:global-chat channel:#interchat use_webhooks:true
```
</CodeGroup>

## Step 6: Your First Cross-Server Message

<Frame>
<img src="/images/first-cross-server-message.png" alt="Screenshot of a cross-server message appearing" />
</Frame>

Once connected, every message in your channel will:

1. **Appear in your channel** as normal
2. **Get sent to the hub** processing system
3. **Appear in all other connected channels** across different servers
4. **Show the original server name** and user information

### Message Format

Cross-server messages typically appear like this:

```
🌐 ServerName | Username#1234
Your message content here!
```

### Understanding Message Flow

<Steps>
<Step title="You Send a Message">
  Type normally in your connected channel.
</Step>

<Step title="InterChat Processes">
  The bot receives your message and prepares it for distribution.
</Step>

<Step title="Hub Distribution">
  Your message is sent to all other servers connected to the same hub.
</Step>

<Step title="Other Servers Receive">
  Members in other servers see your message appear in their channels.
</Step>
</Steps>

## Step 7: Observing Hub Activity

### Incoming Messages

You'll start seeing messages from other servers appear in your channel:

```
🌐 TechCommunity | Developer_Alex#5678
Hey everyone! Working on a new Discord bot, any Python tips?

🌐 GamersUnited | Player_Sam#1234
Just finished the new Zelda game - absolutely amazing! 🎮

🌐 ArtistsHub | Creative_Maya#9876
Sharing my latest digital painting: [image attachment]
```

### Understanding Server Context

Each message shows:
- **🌐 Server icon** indicating it's from InterChat
- **Server name** where the message originated
- **Username and discriminator** of the original sender
- **Original message content** including images, links, and emojis

### Engagement Tips

<Tip>
Make a great first impression with these engagement strategies:
</Tip>

- **Introduce your server** and what makes it unique
- **Ask questions** to start conversations
- **Share relevant content** that matches the hub's theme
- **Be respectful** and follow hub guidelines
- **Help newcomers** who join after you

## Step 8: Managing Your Connection

### Monitoring Connection Health

Send a short message to verify broadcast, and use `/disconnect` if you need to remove the channel from a hub.

### Adjusting Settings

Customize your connection experience:

<ParamField path="/connection mute" type="command">
Temporarily stop receiving messages (still send)
</ParamField>

<ParamField path="/connection unmute" type="command">
Resume receiving messages from the hub
</ParamField>

<ParamField path="/connection nickname" type="command">
Change how your server appears to others
</ParamField>

### Disconnecting (If Needed)

If the hub isn't a good fit:

<CodeGroup>
```bash Disconnect from Hub
/disconnect channel:#interchat
```
</CodeGroup>

<Warning>
Disconnecting removes your channel from the hub immediately. Your server members will no longer see messages from that hub.
</Warning>

## Troubleshooting First Connection Issues

<AccordionGroup>
<Accordion title="Connection command fails">
**Common causes:**
- Hub name is incorrect or hub doesn't exist
- Missing permissions in the target channel
- Bot is experiencing temporary issues

**Solutions:**
- Double-check hub name with `/hubs` command
- Verify InterChat has all required permissions
- Try again in a few minutes
</Accordion>

<Accordion title="Messages not appearing from other servers">
**Common causes:**
- Hub is currently inactive
- Connection established but not fully synced
- Bot permission issues

**Solutions:**
- Wait 5-10 minutes for full synchronization
- Verify bot can send messages and create webhooks
</Accordion>

<Accordion title="Your messages not reaching other servers">
**Common causes:**
- Rate limiting due to high activity
- Message content violated hub rules
- Temporary hub moderation action

**Solutions:**
- Wait a few minutes between messages
- Review hub rules for content guidelines
- Contact hub moderators if issues persist
</Accordion>
</AccordionGroup>

## Next Steps After Your First Connection

<CardGroup cols={2}>
<Card title="👥 Explore User Features" icon="user" href="/user-guide/messaging">
  Learn about advanced messaging features, reactions, and user preferences
</Card>

<Card title="🔧 User Preferences" icon="gear" href="/user-guide/user-preferences">
  Customize your InterChat experience with personal settings
</Card>

<Card title="🔗 Connect More Hubs" icon="link" href="/user-guide/joining-hubs">
  Discover and connect to additional communities
</Card>

<Card title="🛡️ Moderation Basics" icon="shield" href="/hub-management/moderation/overview">
  Learn about moderation tools and keeping your community safe
</Card>
</CardGroup>

## Celebrating Your Connection

<Check>
Congratulations! You've successfully made your first InterChat connection and joined the global community.
</Check>

Your Discord server is now part of something bigger - a network of communities sharing conversations, ideas, and experiences across server boundaries. Welcome to the world of cross-server communication!

<Info>
Remember: With great connectivity comes great responsibility. Always represent your community well and follow both your server's rules and the hub's guidelines.
</Info>

---

**Need help or have questions about your first connection?** Join our [Discord support server](https://www.interchat.tech/support) where experienced users and staff can assist you!
