---
title: "Basic Setup & Config"
description: "Complete guide to configuring InterChat with advanced settings and best practices"
icon: "gear"
---
## Overview

This guide covers the complete setup process for InterChat, including advanced configuration options, permission management, and optimization tips for the best experience.

<Info>
Already completed the [Quick Start](/getting-started/quickstart)? This guide builds on those basics with advanced features and customization options.
</Info>

## Prerequisites

<Steps>
<Step title="Verify Permissions">
  Grant ONLY what InterChat actually needs to function:

  **Required (core features):**
  - ✅ View Channel / Read Messages
  - ✅ Read Message History
  - ✅ Send Messages
  - ✅ Create / Manage Webhooks (needed for cross-server broadcasting)
  - ✅ Use Application Commands (slash commands)
  - ✅ Embed Links (rich formatting)
  - ✅ Attach Files (allow media relays)
  - ✅ Use External Emojis (optional but improves appearance)

  **Optional (server-side moderation you may choose to delegate):**
  - 🟡 Manage Messages (only if you want the bot to auto-clean or moderate locally)
  - 🟡 Timeout / Kick / Ban Members (NOT required for hub bans – InterChat enforces hub bans internally without touching your local member permissions)

  <Tip>
  Hub bans and infractions are stored in InterChat's database and applied during message validation. The bot does NOT need Kick/Ban permissions to block a user from participating in a hub.
  </Tip>
</Step>

<Step title="Choose Your Channels">
  Decide which channels will be used for cross-server communication:
  
  - **Dedicated InterChat channels** (recommended)
  - **Existing community channels** 
  - **Multiple channels for different topics**
</Step>

<Step title="Plan Your Hub Strategy">
  Consider what types of communities you want to connect with:
  
  - **General discussion hubs**
  - **Topic-specific communities**
  - **Language or region-based groups**
  - **Private/invite-only hubs**
</Step>
</Steps>

## Channel Configuration

### Setting Up Dedicated Channels

For the best experience, create dedicated channels for InterChat:

<CodeGroup>
```bash Create Channel
# In Discord, create a new text channel
# Name suggestions:
- #interchat
- #global-chat  
- #community-bridge
- #cross-server
```
</CodeGroup>

<Tip>
Use clear, descriptive names so your members understand the channel's purpose.
</Tip>

### Channel Permission Setup

Keep it simple. In most cases you only need to ensure the bot can read & send plus manage its webhook:

**Bot in the channel:**
- View Channel / Read Messages
- Read Message History
- Send Messages
- Manage Webhooks
- Embed Links & Attach Files

**Everyone:**
- Send Messages (unless you intentionally lock the channel)
- Read Message History

Avoid granting broad moderation powers unless you explicitly need InterChat to perform local server actions.

## Hub Discovery & Connection

### Finding the Right Hubs

Discover communities in two ways:

<CodeGroup>
```bash List Public Hubs (in Discord)
/hubs
```

```bash Hub Directory (website)
https://interchat.tech/hubs
```
</CodeGroup>

### Hub Information

Before connecting, review the hub page on the website for rules and expectations. If you already know the name or have an invite code, you can connect directly with `/connect`.

### Making Connections

<Steps>
<Step title="Connect to a Public Hub by Name">
  ```bash
  /connect hub:hub-name channel:#your-channel
  ```
  <Check>You'll receive a confirmation when connected.</Check>
</Step>

<Step title="Connect with an Invite Code">
  ```bash
  /connect invite:abc123 channel:#your-channel
  ```
</Step>

<Step title="Test the Connection">
  Send a short message to verify broadcast.
</Step>
</Steps>

## Advanced Configuration

Most configuration today is hub-focused (rules, locks, moderators) rather than global server toggles. After connecting, adjust hub-level settings with the hub management commands (see Hub Management guides). A future global config command may be added, but there is no `/server config` command at this time.

### User Preferences

Help your members customize their experience:

<CodeGroup>
```bash User Preferences
/my preferences
```
</CodeGroup>

Members can configure:
- **Notification settings** for mentions and replies
- **Display preferences** for usernames and servers
- **Language preferences** for interface
- **Privacy settings** for profile visibility

## Moderation Setup

InterChat provides hub-level moderation through infractions and locks. There are currently no `/filter`, `/automod`, `/ratelimit`, or `/reputation` commands. To moderate:

- Use hub moderator/manager permissions (see Hub Management docs)
- Issue infractions or hub bans with the existing moderation commands (see Moderation Guide)
- Lock or unlock a hub if needed

For technical details of the validation and filtering pipeline, refer to the developer documentation.

## Multiple Hub Management

### Connecting Multiple Channels

For larger servers, you might want multiple InterChat connections:

<Tabs>
<Tab title="Topic-Based Channels">
```bash
# Gaming discussions
/connect hub:gaming-central channel:#gaming-chat

# Art and creativity  
/connect hub:creative-corner channel:#art-showcase

# General discussions
/connect hub:global-chat channel:#general-interchat
```
</Tab>

<Tab title="Language-Based Channels">
```bash
# English speakers
/connect hub:english-global channel:#english-chat

# Spanish speakers
/connect hub:español-global channel:#español-chat

# Mixed/International
/connect hub:international channel:#international-chat
```
</Tab>
</Tabs>

### Managing Multiple Connections

Use `/connect`, `/disconnect`, and `/hubs` to manage. Use `/connections list` command to track connected channels per your server’s structure.

## Optimization & Best Practices

### Performance Optimization

<Tip>
For servers with high message volume, consider these optimizations:
</Tip>

- **Limit connections** to 3-5 active hubs maximum
- **Use topic-specific channels** rather than connecting general chat
- **Enable rate limiting** to prevent spam
- **Regular cleanup** of inactive connections

### Community Guidelines

Establish clear rules for InterChat usage:

<AccordionGroup>
<Accordion title="Channel Rules">
Create specific rules for your InterChat channels:

```markdown
📋 InterChat Channel Rules

1. Be respectful to members from all servers
2. Use appropriate language and content
3. No spam, advertising, or self-promotion
4. Follow Discord's Terms of Service
5. Report issues to moderators immediately

Remember: Your messages reach multiple servers!
```
</Accordion>

<Accordion title="Member Education">
Educate your members about cross-server communication:

- Explain that messages reach multiple servers
- Highlight the importance of representing your community well
- Provide guidance on hub-specific rules and cultures
</Accordion>
</AccordionGroup>

## Troubleshooting Common Setup Issues

<Warning>
If you encounter issues during setup, try these solutions:
</Warning>

<AccordionGroup>
<Accordion title="Commands not working">
**Possible causes:**
- Missing permissions for InterChat
- Slash commands not synced
- Bot offline or experiencing issues

**Solutions:**
- Re-invite bot with full permissions
- Wait 5-10 minutes for command sync
- Check [status page](https://status.interchat.tech)
</Accordion>

<Accordion title="Messages not appearing">
**Possible causes:**
- Channel not properly connected
- Hub experiencing issues
- Permission problems

**Solutions:**
- Verify connection with `/hub info`
- Check hub status with `/hubs`
- Review channel permissions
</Accordion>

<Accordion title="Moderation not working">
**Possible causes:**
- Insufficient bot permissions (missing Send / Read / Webhooks)
- User is hub-banned (message silently blocked)
- Hub locked temporarily

**Solutions:**
- Re-check channel & webhook permissions
- Use moderation commands to view infractions / hub status
- Unlock the hub if intentionally locked
</Accordion>
</AccordionGroup>

## Next Steps

<CardGroup cols={2}>
<Card title="🔗 Make Your First Connection" icon="link" href="/getting-started/first-connection">
  Complete tutorial for connecting to your first hub
</Card>

<Card title="👥 User Guide" icon="users" href="/user-guide/joining-hubs">
  Help your members understand how to use InterChat
</Card>

<Card title="🛡️ Moderation Guide" icon="shield" href="/hub-management/moderation/overview">
  Learn advanced moderation techniques
</Card>

<Card title="🏗️ Create a Hub" icon="hammer" href="/hub-management/creating-hubs">
  Start your own community hub
</Card>
</CardGroup>

---

<Note>
Need personalized setup assistance? Join our [Discord support server](https://www.interchat.tech/support) where our team and community can help with specific configuration challenges.
</Note>
