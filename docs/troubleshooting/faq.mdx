---
title: "FAQ"
description: "Frequently asked questions about InterChat features and policies"
icon: "circle-question"
---

## General Questions

### What is InterChat?
InterChat is a Discord bot that connects channels across different servers, allowing users to have conversations that span multiple Discord communities. Messages sent in connected channels are broadcast to all other channels in the same "hub."

### Is InterChat free to use?
Yes, InterChat is free to use. Premium features may be available for supporters, but core functionality is available to everyone.

### How many servers can connect to one hub?
There's no hard limit, but performance is optimal with 10-50 connected channels. Very large hubs may experience slower message delivery.

### Can I use InterChat in my private server?
Yes, InterChat works in any Discord server where you have permission to invite bots and manage webhooks.

## Safety & Moderation

### How do you prevent spam and abuse?
InterChat has multiple protection layers:
- 7-day minimum account age requirement
- Message length limits (2000 characters)
- Hub-specific moderation with warnings and bans
- Global blacklists for serious violations
- Rate limiting to prevent flooding

### Who can moderate hubs?
Hub owners can promote users to Manager or Moderator roles. Each level has different permissions for configuration and enforcement.

### Can I report inappropriate content?
Yes, use the report system or contact InterChat staff directly via the support server. All reports are reviewed promptly.

### What happens if someone breaks the rules?
Depending on severity, actions range from warnings to permanent bans from specific hubs or InterChat entirely.

## Technical Questions

### Why do messages sometimes appear with different usernames?
InterChat uses webhooks to display messages with the original sender's name and avatar. If webhooks fail, messages may appear from the bot account instead.

### Can I edit or delete messages after sending?
Edits are propagated when possible. Deletions are attempted across connected channels via webhooks but may fail if a destination webhook was removed.

### Do attachments and embeds work?
Yes, most Discord content types are supported including images, files, and embeds. Very large files may be blocked due to size limits.

### Can I use InterChat with threads?
Thread support exists but may require specific setup. Check with your server admin about thread channel connections.

## Hub Management

### How do I create a hub?
Use `/hub create` in a server where you have Manage Guild permission. You'll need to provide a name, description, and connect your first channel.

### Can I transfer hub ownership?
Yes. Use `/hub configure` → Ownership Transfer (Owner only).

### How do I remove my server from a hub?
Run `/disconnect` in the connected channel. The webhook will be cleaned up and broadcasting stops immediately.

### Can one channel be in multiple hubs?
No. Each channel can only be connected to one hub at a time to prevent duplication and confusion.

## Privacy & Data

### What data does InterChat store?
InterChat stores:
- User profiles (message counts, badges)
- Hub configurations and connections
- Moderation records (infractions, appeals)
- Webhook URLs for broadcasting

### Can I delete my data?
Yes, contact InterChat support to request data deletion. Note that this may affect your ability to use certain features.

### Do you share data with third parties?
No, InterChat data is not shared with third parties except as required by Discord's Terms of Service or legal obligations.

### Is my data secure?
Yes, all data is encrypted in transit and at rest. Access is limited to essential bot operations and authorized staff for support purposes.

## Getting Help

### Commands aren't working, what should I do?
1. Check bot permissions in your server
2. Verify the bot is online
3. Try commands in a different channel
4. Join the support server for assistance

### How do I join the support server?
Use `/invite` command or check the InterChat website for the permanent invite link.

### Can I contribute to InterChat?
Yes! InterChat welcomes:
- Translators for new languages
- Developers for code contributions
- Community moderators
- Feature suggestions and bug reports

### Is there a roadmap for new features?
Check the GitHub repository and support server announcements for development updates and planned features.

<CardGroup cols={2}>
<Card title="Common Issues" href="/troubleshooting/common-issues" icon="wrench">
Troubleshoot specific problems.
</Card>
<Card title="Commands Reference" href="/user-guide/commands" icon="terminal">
Learn all available commands.
</Card>
</CardGroup>
