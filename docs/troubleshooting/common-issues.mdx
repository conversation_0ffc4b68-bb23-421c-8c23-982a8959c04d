---
title: "Common Issues"
description: "Solutions to frequently encountered InterChat problems"
icon: "wrench"
---

## Messages not broadcasting

### Missing permissions
**Symptom**: Messages send in your channel but don't appear in other connected channels.

**Solution**:
<Steps>
<Step title="Check bot permissions">
Ensure InterChat has "Manage Webhooks" and "Send Messages" in the channel.
</Step>
<Step title="Verify connection">
Re-run `/connect` and ensure the bot replies with a successful connection message.
</Step>
<Step title="Test with simple message">
Send a short test message (under 50 characters) to rule out content issues.
</Step>
</Steps>

### Hub locked
**Symptom**: Error message about hub being locked when you try to send.

**Solution**: Hub moderators need to unlock the hub via `/hub configure`. Contact hub staff or wait for maintenance to complete.

### Account age restriction
**Symptom**: New Discord accounts can't send messages in hubs.

**Solution**: InterChat requires accounts to be at least 7 days old. Wait for your account to mature or contact support if this seems incorrect.

## Connection problems

### Webhook creation fails
**Symptom**: Error when trying to connect a channel to a hub.

<Warning>
This usually indicates missing "Manage Webhooks" permission for the InterChat bot.
</Warning>

**Solution**:
1. Check bot role has "Manage Webhooks" permission
2. Ensure the bot role is above other roles that might conflict
3. Try reconnecting after fixing permissions

### Messages still not relaying after connect
**Symptom**: You recently connected, but messages don't broadcast.

**Solution**:
- Webhook may have been deleted—run `/disconnect` then connect again
- Test in a different channel to isolate the issue
- Check for Discord API outages affecting webhooks

## Hub access issues

### Can't join a hub
**Symptom**: Hub appears in `/hubs` but connection attempts fail.

**Possible causes**:
- Server is banned from the hub
- Hub has reached connection limits
- Hub is temporarily locked

**Solution**: Contact the hub moderators directly or try again later.

### Permission denied for hub commands
**Symptom**: `/hub configure` or moderation commands return permission errors.

**Solution**: Verify you have the correct hub role (Owner/Manager/Moderator). Use `/my hubs` to check your role in specific hubs.

## Performance issues

### Slow message delivery
**Symptom**: Messages take several seconds to appear in other channels.

**Causes**:
- High Discord API latency
- Many connected channels (broadcasting overhead)
- Rate limiting due to high activity

**Solutions**:
- Wait for Discord infrastructure to stabilize
- Consider reducing the number of connected channels
- Spread message sending across time to avoid rate limits

### Bot appears offline
**Symptom**: InterChat shows as offline but commands still work.

**Solution**: This is usually a Discord display issue. Try refreshing Discord or checking bot status on the support server.

## Command issues

### Commands not responding
**Symptom**: Slash commands don't appear or don't respond.

<Steps>
<Step title="Check bot online status">
Ensure InterChat appears online in the member list.
</Step>
<Step title="Verify command permissions">
Bot needs "Use Slash Commands" permission in the channel.
</Step>
<Step title="Try in different channel">
Test if the issue is channel-specific.
</Step>
<Step title="Restart Discord">
Sometimes Discord needs to refresh its command cache.
</Step>
</Steps>

### Slash commands show as "Application did not respond"
**Symptom**: Commands appear but timeout with an error message.

**Solution**: Usually indicates temporary bot overload or Discord API issues. Wait a few minutes and retry.

## Data sync issues

### Profile stats incorrect
**Symptom**: `/profile` shows wrong message counts or missing badges.

**Solution**: 
- Stats sync periodically; recent activity may take time to reflect
- Badge awards can take up to 24 hours to appear
- Contact support if data seems permanently incorrect

### Hub list doesn't update
**Symptom**: New hubs don't appear in `/hubs` or removed hubs still show.

**Solution**: Hub lists cache for performance. Updates may take 5-10 minutes to propagate.

<CardGroup cols={2}>
<Card title="FAQ" href="/troubleshooting/faq" icon="question">
Check frequently asked questions.
</Card>
<Card title="Commands Reference" href="/user-guide/commands" icon="terminal">
Verify correct command usage.
</Card>
</CardGroup>
