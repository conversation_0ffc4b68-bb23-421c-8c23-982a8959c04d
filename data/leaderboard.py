leaderboard_template = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Discord Leaderboard</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
            background: linear-gradient(135deg, #1a1f2e 0%, #2d3548 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .leaderboard-container {
            max-width: 900px;
            margin: 0 auto;
        }
        
        .leaderboard {
            background: linear-gradient(145deg, #242b3d 0%, #1e2332 100%);
            border-radius: 20px;
            padding: 32px;
            box-shadow: 
                0 20px 60px rgba(0, 0, 0, 0.4),
                0 0 0 1px rgba(255, 255, 255, 0.05);
            position: relative;
            overflow: hidden;
        }
        
        .leaderboard::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, #6366f1, #8b5cf6, #ec4899);
        }
        
        .leaderboard-header-row {
            display: grid;
            grid-template-columns: 60px 1fr 120px 120px;
            gap: 16px;
            padding: 16px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.08);
            margin-bottom: 16px;
        }
        
        .header-cell {
            font-size: 12px;
            font-weight: 600;
            color: #8b93a6;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .leaderboard-row {
            display: grid;
            grid-template-columns: 60px 1fr 120px 120px;
            gap: 16px;
            align-items: center;
            padding: 16px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.04);
            border-radius: 12px;
            margin: 0 -8px;
            padding-left: 8px;
            padding-right: 8px;
        }
        
        .leaderboard-row:last-child {
            border-bottom: none;
        }
        
        .rank {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            font-weight: 700;
            font-size: 16px;
        }
        
        .rank.top-1 {
            background: linear-gradient(135deg, #b58516, #945907);  /* Darker gold */
            color: #ffffff;
            box-shadow: 0 4px 16px rgba(181, 133, 22, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .rank.top-2 {
            background: linear-gradient(135deg, #64748b, #475569);  /* Darker silver */
            color: #ffffff;
            box-shadow: 0 4px 16px rgba(100, 116, 139, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .rank.top-3 {
            background: linear-gradient(135deg, #9c5d23, #854108);  /* Darker bronze */
            color: #ffffff;
            box-shadow: 0 4px 16px rgba(156, 93, 35, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .rank.regular {
            background: rgba(255, 255, 255, 0.05);
            color: #8b93a6;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            font-weight: 700;
            color: white;
            position: relative;
            flex-shrink: 0;
            background-size: cover;
            background-position: center;
        }
        
        .avatar::after {
            content: '';
            position: absolute;
            inset: -2px;
            border-radius: 50%;
            background: linear-gradient(135deg, #6366f1, #8b5cf6, #ec4899);
            z-index: -1;
            opacity: 0.3;
        }
        
        .avatar-fallback {
            background-image: none !important;
        }
        
        .user-details {
            min-width: 0;
        }
        
        .username {
            font-size: 16px;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 2px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .guild-tag {
            font-size: 12px;
            color: #8b93a6;
            font-weight: 500;
        }
        
        .stat-cell {
            text-align: center;
        }
        
        .stat-value {
            font-size: 18px;
            font-weight: 700;
            color: #ffffff;
            margin-bottom: 2px;
        }
        
        .stat-change {
            font-size: 11px;
            font-weight: 500;
        }
        
        .stat-change.positive {
            color: #10b981;
        }
        
        .stat-change.negative {
            color: #ef4444;
        }
        
        .stat-change.neutral {
            color: #8b93a6;
        }
        
        .medal-icon {
            font-size: 20px;
            margin: 0;  /* Remove margin to help with centering */
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
        }
    </style>
</head>
<body>
    <div class="leaderboard-container">
        <div class="leaderboard">
            <div class="leaderboard-header-row">
                <div class="header-cell">Rank</div>
                <div class="header-cell">Member</div>
                <div class="header-cell">{{ stat1_label }}</div>
                <div class="header-cell">{{ stat2_label }}</div>
            </div>
            
            {% for user in users %}
            <div class="leaderboard-row">
                <div class="rank {% if user.rank == 1 %}top-1{% elif user.rank == 2 %}top-2{% elif user.rank == 3 %}top-3{% else %}regular{% endif %}">
                    {% if user.rank == 1 %}
        <span class="medal-icon">🥇</span>
    {% elif user.rank == 2 %}
        <span class="medal-icon">🥈</span>
    {% elif user.rank == 3 %}
        <span class="medal-icon">🥉</span>
    {% else %}
        {{ user.rank }}
    {% endif %}
                </div>
                <div class="user-info">
                    <div class="avatar {{ 'avatar-fallback' if not user.avatar_url else '' }}" {% if user.avatar_url %}style="background-image: url('{{ user.avatar_url }}');"{% endif %}>
                        {% if not user.avatar_url %}{{ user.avatar_initials }}{% endif %}
                    </div>
                    <div class="user-details">
                        <div class="username">{{ user.username }}</div>
                        <div class="guild-tag">{{ user.guild_tag }}</div>
                    </div>
                </div>
                <div class="stat-cell">
                    <div class="stat-value">{{ user.stat1_value }}</div>
                    {% if user.stat1_change %}
                    <div class="stat-change {{ user.stat1_change_class }}">{{ user.stat1_change }}</div>
                    {% endif %}
                </div>
                <div class="stat-cell">
                    <div class="stat-value">{{ user.stat2_value }}</div>
                    {% if user.stat2_change %}
                    <div class="stat-change {{ user.stat2_change_class }}">{{ user.stat2_change }}</div>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</body>
</html>
"""
