from __future__ import annotations

from dataclasses import dataclass
from enum import Enum
from typing import Optional
from utils.modules.core.i18n import t

import discord


class ActionType(Enum):
    WARN = 'warn'
    MUTE = 'mute'
    BAN = 'ban'
    UNMUTE = 'unmute'
    UNBAN = 'unban'
    DELETE = 'delete'
    BLACKLIST = 'blacklist'


@dataclass
class ModerationTarget:
    """Represents a target for moderation action (user or server)."""

    user: Optional[discord.User | discord.Member] = None
    server: Optional[discord.Guild] = None

    @property
    def is_user(self) -> bool:
        return self.user is not None

    @property
    def is_server(self) -> bool:
        return self.server is not None

    @property
    def target_id(self) -> str:
        if self.user:
            return str(self.user.id)
        elif self.server:
            return str(self.server.id)
        return ''

    @property
    def target_name(self) -> str:
        if self.user:
            return str(self.user)
        elif self.server:
            return str(self.server)
        return ''

    def validate(self) -> tuple[bool, Optional[str]]:
        """Validate the target. Returns (is_valid, error_message)."""
        if self.user and self.server:
            return False, t('responses.moderation.target.both', locale='en')
        if not self.user and not self.server:
            return False, t('responses.moderation.target.missing', locale='en')
        return True, None
