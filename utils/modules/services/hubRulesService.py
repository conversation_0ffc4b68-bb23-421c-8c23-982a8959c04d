from datetime import datetime
from typing import List, Optional, Tuple

from utils.constants import logger
from utils.modules.common.validation_utils import TextValidators
from utils.modules.core.i18n import t
from utils.modules.services.BaseService import BaseService
from utils.utils import get_hub


class HubRulesService(BaseService):
    """Service for hub rules management operations."""

    MAX_RULES = 25
    MAX_RULE_LENGTH = 400

    async def get_hub_rules(self, hub_id: str) -> Optional[List[str]]:
        """Get rules for a hub."""
        hub = await get_hub(hub_id, self.session)
        return hub.rules if hub else None

    async def add_rule(self, hub_id: str, rule_text: str) -> Tuple[bool, str]:
        """Add a new rule to a hub."""
        try:
            # Validate rule
            is_valid, error_msg = self.validate_rule_text(rule_text)
            if not is_valid:
                return False, error_msg

            hub = await get_hub(hub_id, self.session)
            if not hub:
                return False, t('ui.common.messages.hubNotFound', locale='en')

            # Check rules count
            current_count = len(hub.rules) if hub.rules else 0
            is_valid, error_msg = self.validate_rules_count(current_count)
            if not is_valid:
                return False, error_msg

            # Add rule
            hub.rules = hub.rules + [rule_text]  # Create new list to trigger SQLAlchemy update
            hub.updatedAt = datetime.now()
            await self.session.commit()

            return True, ''

        except Exception as e:
            logger.error(f'Failed to add rule to hub {hub_id}: {e}')
            await self.session.rollback()
            return False, t('ui.common.messages.hubUpdateFailed', locale='en')

    async def update_rule(self, hub_id: str, rule_index: int, rule_text: str) -> Tuple[bool, str]:
        """Update an existing rule."""
        try:
            # Validate rule
            is_valid, error_msg = self.validate_rule_text(rule_text)
            if not is_valid:
                return False, error_msg

            hub = await get_hub(hub_id, self.session)
            if not hub:
                return False, t('ui.common.messages.hubNotFound', locale='en')

            # Validate rule index
            if not hub.rules or rule_index < 0 or rule_index >= len(hub.rules):
                return False, t('ui.hub.rules.errors.invalidRuleSelection', locale='en')

            # Update rule
            updated_rules = hub.rules.copy()
            updated_rules[rule_index] = rule_text
            hub.rules = updated_rules
            hub.updatedAt = datetime.now()
            await self.session.commit()

            return True, ''

        except Exception as e:
            logger.error(f'Failed to update rule in hub {hub_id}: {e}')
            await self.session.rollback()
            return False, t('ui.common.messages.hubUpdateFailed', locale='en')

    async def delete_rule(self, hub_id: str, rule_index: int) -> Tuple[bool, str]:
        """Delete a rule by index."""
        try:
            hub = await get_hub(hub_id, self.session)
            if not hub:
                return False, t('ui.common.messages.hubNotFound', locale='en')

            # Validate rule index
            if not hub.rules or rule_index < 0 or rule_index >= len(hub.rules):
                return False, t('ui.hub.rules.errors.ruleNotFound', locale='en')

            # Delete rule
            updated_rules = hub.rules.copy()
            updated_rules.pop(rule_index)
            hub.rules = updated_rules
            hub.updatedAt = datetime.now()
            await self.session.commit()

            return True, ''

        except Exception as e:
            logger.error(f'Failed to delete rule from hub {hub_id}: {e}')
            await self.session.rollback()
            return False, t('ui.common.messages.hubUpdateFailed', locale='en')

    async def get_rule_at_index(self, hub_id: str, rule_index: int) -> Optional[str]:
        """Get a specific rule by index."""
        hub = await get_hub(hub_id, self.session)
        if not hub or not hub.rules or rule_index < 0 or rule_index >= len(hub.rules):
            return None
        return hub.rules[rule_index]

    async def get_rules_count(self, hub_id: str) -> int:
        """Get the current number of rules for a hub."""
        hub = await get_hub(hub_id, self.session)
        return len(hub.rules) if hub and hub.rules else 0

    @classmethod
    def validate_rule_text(cls, text: str, locale: str = 'en') -> Tuple[bool, str]:
        empty_validation = TextValidators.validate_not_empty(text, 'rule text')
        if not empty_validation.is_valid:
            return False, t('ui.hub.rules.validation.empty', locale=locale)

        length_validation = TextValidators.validate_length(
            text, 1, cls.MAX_RULE_LENGTH, 'rule text'
        )
        if not length_validation.is_valid:
            return False, t(
                'ui.hub.rules.validation.tooLong', locale=locale, maxLength=cls.MAX_RULE_LENGTH
            )

        return True, ''

    @classmethod
    def validate_rules_count(cls, current_count: int, locale: str = 'en') -> Tuple[bool, str]:
        """Validate total rules count."""
        if current_count >= cls.MAX_RULES:
            return False, t(
                'ui.hub.rules.validation.maxRules', locale=locale, maxRules=cls.MAX_RULES
            )

        return True, ''
