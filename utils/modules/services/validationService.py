from typing import TYPE_CHECKING

import discord

from utils.modules.broadcast.checks import Message<PERSON>alida<PERSON>, ValidationResult
from utils.modules.core.db.models import Hub
from utils.modules.services.BaseService import BaseService

from utils.constants import logger

if TYPE_CHECKING:
    from sqlalchemy.ext.asyncio import AsyncSession


class ValidationService(BaseService):
    """Service for message validation operations."""

    def __init__(self, session: 'AsyncSession'):
        super().__init__(session)
        self._validator = MessageValidator(self.session)

    async def validate_message_for_broadcast(
        self,
        message: discord.Message,
        hub: Hub,
    ) -> 'ValidationResult':
        """
        Validate a message for broadcasting with caching support.
        """
        logger.debug(f'Message validation requested for {message.id}')
        if not message.guild:
            return ValidationResult(is_valid=False, reason='Message not from a guild')

        user_id = str(message.author.id)
        guild_id = str(message.guild.id)

        logger.debug(f'Validation started for {message.id}...')
        res = await self._validator.validate_message(message, hub, user_id, guild_id)
        logger.debug(f'Validation complete for {message.id}: {res}')
        return res
