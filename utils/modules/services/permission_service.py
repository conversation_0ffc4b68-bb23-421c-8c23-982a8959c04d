from typing import TYPE_CHECKING, Optional
from sqlalchemy import select

from utils.modules.core.db.models import Hub, HubModerator, HubModeratorRole
from utils.modules.hub.constants import HubPermissionLevel
from utils.constants import logger

if TYPE_CHECKING:
    from sqlalchemy.ext.asyncio import AsyncSession


class PermissionService:
    """Service for checking hub permissions."""

    def __init__(self, session: 'AsyncSession'):
        self.session = session

    async def get_user_permission(self, hub_id: str, user_id: str) -> HubPermissionLevel:
        """
        Get user's permission level for a specific hub.
        """
        # Fetch hub with moderators preloaded
        stmt = (
            select(HubModerator, Hub.ownerId)
            .join(Hub, Hub.id == HubModerator.hubId)
            .where(HubModerator.hubId == hub_id, HubModerator.userId == user_id)
        )

        result = (await self.session.execute(stmt)).tuples().first()

        if not result:
            logger.warning(f'Hub {hub_id} not found when checking permissions for user {user_id}')
            return HubPermissionLevel.NONE

        hub_moderator, hub_owner_id = result

        return self._calculate_permission_level(hub_moderator, hub_owner_id, user_id)

    async def get_user_permission_from_hub(self, hub: Hub, user_id: str) -> HubPermissionLevel:
        """
        Get user's permission level from an existing hub object.
        If moderators are not loaded, will load them from database.
        """
        stmt = select(HubModerator).where(
            HubModerator.hubId == hub.id, HubModerator.userId == user_id
        )
        moderator = await self.session.scalar(stmt)

        return self._calculate_permission_level(moderator, hub.ownerId, user_id)

    def _calculate_permission_level(
        self, mod: Optional[HubModerator], hub_owner_id: str, user_id: str
    ) -> HubPermissionLevel:
        """Calculate permission level based on hub data."""
        # Check if user is the hub owner
        if hub_owner_id == user_id:
            return HubPermissionLevel.OWNER

        if mod:
            if mod.userId == user_id:
                if mod.role == HubModeratorRole.MANAGER:
                    return HubPermissionLevel.MANAGER
                elif mod.role == HubModeratorRole.MODERATOR:
                    return HubPermissionLevel.MODERATOR

        return HubPermissionLevel.NONE

    async def check_permission(
        self,
        hub_id: str,
        user_id: str,
        required_level: HubPermissionLevel = HubPermissionLevel.MODERATOR,
    ) -> tuple[bool, HubPermissionLevel]:
        """
        Check if user has the required permission level.
        """
        actual_level = await self.get_user_permission(hub_id, user_id)
        has_permission = actual_level.value >= required_level.value

        logger.debug(
            f'Permission check: user {user_id} in hub {hub_id} - '
            f'actual: {actual_level.name}, required: {required_level.name}, '
            f'has_permission: {has_permission}'
        )

        return has_permission, actual_level

    async def check_permission_from_hub(
        self,
        hub: Hub,
        user_id: str,
        required_level: HubPermissionLevel = HubPermissionLevel.MODERATOR,
    ) -> tuple[bool, HubPermissionLevel]:
        """
        Check if user has the required permission level using existing hub object.

        Returns:
            tuple: (has_permission, actual_level)
        """
        actual_level = await self.get_user_permission_from_hub(hub, user_id)
        has_permission = actual_level.value >= required_level.value

        return has_permission, actual_level
