from typing import List, Optional
from sqlalchemy import select

from utils.modules.core.db.models import Hub, Connection, HubModerator
from utils.modules.hub.constants import HubPermissionLevel
from utils.modules.services.BaseService import BaseService
from utils.modules.services.permission_service import PermissionService
from utils.modules.common.database import DatabaseQueries


class HubService(BaseService):
    async def get_hub_by_id(self, hub_id: str) -> Optional[Hub]:
        """Get a hub by its ID."""
        return await DatabaseQueries.fetch_hub_by_id(self.session, hub_id)

    async def get_hub_by_name(self, hub_name: str, include_private: bool = False) -> Optional[Hub]:
        """Get a hub by its name."""
        return await DatabaseQueries.fetch_hub_by_name(
            self.session, hub_name, include_private=include_private
        )

    async def get_public_hubs(self) -> List[Hub]:
        """Get all public hubs."""
        result = await DatabaseQueries.fetch_public_hubs(self.session)
        return list(result)

    async def get_user_hubs(self, user_id: str) -> List[Hub]:
        """Get all hubs where the user has permissions."""
        stmt = (
            select(Hub)
            .join(HubModerator, Hub.id == HubModerator.hubId)
            .where(HubModerator.userId == user_id)
        )
        result = await self.session.execute(stmt)
        return list(result.scalars().all())

    async def get_hub_connections(
        self, hub_id: str, connected_only: bool = True
    ) -> List[Connection]:
        """Get all connections for a hub."""
        result = await DatabaseQueries.fetch_hub_connections(
            self.session, hub_id, connected_only=connected_only
        )
        return list(result)

    async def check_user_permission(self, hub_id: str, user_id: str) -> HubPermissionLevel:
        """Check user's permission level in a hub."""
        hub = await self.get_hub_by_id(hub_id)
        if not hub:
            return HubPermissionLevel.NONE
        perm_service = PermissionService(self.session)
        return await perm_service.get_user_permission_from_hub(hub, user_id)

    async def is_hub_locked(self, hub_id: str) -> bool:
        """Check if a hub is locked."""
        hub = await self.get_hub_by_id(hub_id)
        return hub.locked if hub else True

    async def lock_hub(self, hub_id: str, user_id: str) -> bool:
        """Lock a hub (requires appropriate permissions)."""
        permission_level = await self.check_user_permission(hub_id, user_id)
        if permission_level < HubPermissionLevel.MANAGER:
            return False

        hub = await self.get_hub_by_id(hub_id)
        if not hub:
            return False

        hub.locked = True
        await self.session.commit()
        return True

    async def unlock_hub(self, hub_id: str, user_id: str) -> bool:
        """Unlock a hub (requires appropriate permissions)."""
        permission_level = await self.check_user_permission(hub_id, user_id)
        if permission_level < HubPermissionLevel.MANAGER:
            return False

        hub = await self.get_hub_by_id(hub_id)
        if not hub:
            return False

        hub.locked = False
        await self.session.commit()
        return True

    async def update_hub_activity(self, hub_id: str) -> bool:
        """Update hub's last activity timestamp."""
        from datetime import datetime

        hub = await self.get_hub_by_id(hub_id)
        if not hub:
            return False

        hub.lastActive = datetime.now()
        await self.session.commit()
        return True
