import asyncio
from datetime import datetime
from typing import List, Literal, Optional, Tuple

from sqlalchemy import and_, or_, select
from sqlalchemy.orm import aliased

from utils.modules.core.db.models import (
    Blacklist,
    Hub,
    Infraction,
    InfractionStatus,
    InfractionType,
    ServerBlacklist,
    ServerData,
    User,
)
from utils.modules.events.eventDispatcher import (
    HubEventType,
    dispatch_server_action,
    dispatch_user_action,
)
from utils.modules.services.BaseService import BaseService
from utils.utils import ms_to_datetime, ms_to_human

NO_REASON = 'No reason provided.'


class ModerationService(BaseService):
    async def create_infraction(
        self,
        hub_id: str,
        mod_id: str,
        user_id: Optional[str] = None,
        server_id: Optional[str] = None,
        server_name: Optional[str] = None,
        infraction_type: InfractionType = InfractionType.BAN,
        reason: str = NO_REASON,
        duration_ms: Optional[int] = None,
        skip_log: bool = False,
    ) -> Infraction:
        if infraction_type == InfractionType.BAN:
            active = await self.get_active_infractions(hub_id, user_id=user_id, server_id=server_id)
            if any(inf.type == InfractionType.BAN for inf in active):
                raise ValueError('DUPLICATE_BAN_OR_MUTE')

        expires_at = ms_to_datetime(duration_ms) if duration_ms else None

        infraction = Infraction(
            hubId=hub_id,
            userId=user_id,
            serverId=server_id,
            type=infraction_type,
            reason=reason,
            moderatorId=mod_id,
            status=InfractionStatus.ACTIVE,
            expiresAt=expires_at,
            serverName=server_name,
        )

        self.session.add(infraction)
        await self.session.commit()

        if not skip_log:
            asyncio.create_task(self._log_infraction_event(infraction, 'CREATE'))

        return infraction

    async def get_active_infractions(
        self, hub_id: str, user_id: Optional[str] = None, server_id: Optional[str] = None
    ) -> List[Infraction]:
        conditions = [
            Infraction.hubId == hub_id,
            Infraction.status == InfractionStatus.ACTIVE,
            or_(Infraction.expiresAt.is_(None), Infraction.expiresAt > datetime.now()),
        ]

        if user_id:
            conditions.append(Infraction.userId == user_id)
        if server_id:
            conditions.append(Infraction.serverId == server_id)

        result = await self.session.execute(select(Infraction).where(and_(*conditions)))
        return list(result.scalars().all())

    async def revoke_infraction(
        self, infraction_id: str, mod_id: str, reason: str = NO_REASON
    ) -> bool:
        result = await self.session.execute(
            select(Infraction).where(Infraction.id == infraction_id)
        )
        infraction = result.scalar_one_or_none()

        if not infraction:
            return False

        infraction.status = InfractionStatus.REVOKED
        infraction.reason += f' | Revoked by {mod_id}: {reason}'
        await self.session.commit()

        asyncio.create_task(self._log_infraction_event(infraction, 'REVOKE'))
        return True

    async def delete_infraction(self, infraction_id: str) -> bool:
        result = await self.session.execute(
            select(Infraction).where(Infraction.id == infraction_id)
        )
        infraction = result.scalar_one_or_none()

        if not infraction:
            return False

        await self.session.delete(infraction)
        await self.session.commit()

        asyncio.create_task(self._log_infraction_event(infraction, 'DELETE'))
        return True

    async def is_user_banned(self, hub_id: str, user_id: str) -> bool:
        infractions = await self.get_active_infractions(hub_id, user_id=user_id)
        return any(inf.type == InfractionType.BAN for inf in infractions)

    async def is_server_banned(self, hub_id: str, server_id: str) -> bool:
        infractions = await self.get_active_infractions(hub_id, server_id=server_id)
        return any(inf.type == InfractionType.BAN for inf in infractions)

    async def create_blacklist_entry(
        self,
        user_id: str,
        mod_id: str,
        reason: str = NO_REASON,
        duration_ms: Optional[int] = None,
    ) -> Blacklist:
        if await self.is_user_blacklisted(user_id):
            raise ValueError('DUPLICATE_GLOBAL_BLACKLIST_USER')

        expires_at = ms_to_datetime(duration_ms) if duration_ms else None

        blacklist = Blacklist(
            userId=user_id,
            reason=reason,
            moderatorId=mod_id,
            expiresAt=expires_at,
            createdAt=datetime.now(),
        )
        self.session.add(blacklist)
        await self.session.commit()

        return blacklist

    async def create_server_blacklist_entry(
        self,
        server_id: str,
        mod_id: str,
        reason: str = NO_REASON,
        duration_ms: Optional[int] = None,
    ) -> ServerBlacklist:
        if await self.is_server_blacklisted(server_id):
            raise ValueError('DUPLICATE_GLOBAL_BLACKLIST_SERVER')

        expires_at = ms_to_datetime(duration_ms) if duration_ms else None

        blacklist = ServerBlacklist(
            serverId=server_id,
            reason=reason,
            moderatorId=mod_id,
            expiresAt=expires_at,
            createdAt=datetime.now(),
        )

        self.session.add(blacklist)
        await self.session.commit()
        return blacklist

    async def is_user_blacklisted(self, user_id: str) -> bool:
        result = await self.session.execute(
            select(Blacklist).where(
                and_(
                    Blacklist.userId == user_id,
                    or_(Blacklist.expiresAt.is_(None), Blacklist.expiresAt > datetime.now()),
                )
            )
        )
        return result.scalar_one_or_none() is not None

    async def is_server_blacklisted(self, server_id: str) -> bool:
        result = await self.session.execute(
            select(ServerBlacklist).where(
                and_(
                    ServerBlacklist.serverId == server_id,
                    or_(
                        ServerBlacklist.expiresAt.is_(None),
                        ServerBlacklist.expiresAt > datetime.now(),
                    ),
                )
            )
        )
        return result.scalar_one_or_none() is not None

    async def delete_blacklist_entry(self, user_id: str) -> bool:
        result = await self.session.execute(select(Blacklist).where(Blacklist.userId == user_id))
        entries = list(result.scalars().all())

        if not entries:
            return False

        for entry in entries:
            await self.session.delete(entry)
        await self.session.commit()
        return True

    async def delete_server_blacklist_entry(self, server_id: str) -> bool:
        result = await self.session.execute(
            select(ServerBlacklist).where(ServerBlacklist.serverId == server_id)
        )
        entry = result.scalar_one_or_none()

        if not entry:
            return False

        await self.session.delete(entry)
        await self.session.commit()
        return True

    async def _log_infraction_event(
        self,
        infraction: Infraction,
        action: Literal['CREATE', 'REVOKE', 'DELETE'],
        mod_id: Optional[str] = None,
    ) -> None:
        try:
            data = await self._get_infraction_names(infraction.id)
            if not data:
                return

            hub_name, user_name, moderator_name, server_name = data
            action_type = self._get_action_type(infraction, action)
            duration = (
                ms_to_human(int(infraction.expiresAt.timestamp() * 1000))
                if infraction.expiresAt
                else None
            )

            if infraction.userId:
                await dispatch_user_action(
                    action_type=action_type,
                    hub_id=infraction.hubId,
                    hub_name=hub_name,
                    moderator_id=mod_id or infraction.moderatorId,
                    moderator_name=moderator_name,
                    target_user_id=infraction.userId,
                    target_user_name=user_name,
                    reason=infraction.reason,
                    duration=duration,
                    expires_at=infraction.expiresAt,
                )
            elif infraction.serverId:
                await dispatch_server_action(
                    action_type=action_type,
                    hub_id=infraction.hubId,
                    hub_name=hub_name,
                    moderator_id=mod_id or infraction.moderatorId,
                    moderator_name=moderator_name,
                    target_server_id=infraction.serverId,
                    target_server_name=server_name,
                    reason=infraction.reason,
                    duration=duration,
                    expires_at=infraction.expiresAt,
                )
        except Exception:
            pass

    async def _get_infraction_names(
        self, infraction_id: str
    ) -> Optional[Tuple[str, str, str, str]]:
        user_alias = aliased(User)
        mod_alias = aliased(User)

        result = await self.session.execute(
            select(
                Hub.name,
                user_alias.name,
                mod_alias.name,
                ServerData.name,
            )
            .select_from(Infraction)
            .where(Infraction.id == infraction_id)
            .join(Hub, Hub.id == Infraction.hubId)
            .outerjoin(user_alias, user_alias.id == Infraction.userId)
            .outerjoin(mod_alias, mod_alias.id == Infraction.moderatorId)
            .outerjoin(ServerData, ServerData.id == Infraction.serverId)
        )

        row = result.first()
        if not row:
            return None

        return (
            row[0],
            row[1] or 'Unknown User',
            row[2] or 'Unknown Moderator',
            row[3] or 'Unknown Server',
        )

    def _get_action_type(self, infraction: Infraction, action: str = 'CREATE') -> HubEventType:
        type_map = {
            InfractionType.BAN: (HubEventType.USER_BAN, HubEventType.SERVER_BAN)
            if action == 'CREATE'
            else (HubEventType.USER_UNBAN, HubEventType.SERVER_UNBAN),
            InfractionType.MUTE: (HubEventType.USER_MUTE, HubEventType.SERVER_MUTE)
            if action == 'CREATE'
            else (HubEventType.USER_UNMUTE, HubEventType.SERVER_UNMUTE),
            InfractionType.WARNING: (HubEventType.USER_WARN, HubEventType.SERVER_WARN),
        }
        user_type, server_type = type_map[infraction.type]
        return user_type if infraction.userId else server_type
