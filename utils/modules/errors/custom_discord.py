import discord 
from discord.ext import commands

class InteractionCheck(commands.CommandError):
    def __init__(self, message='You may not use this interaction, as you did not invoke it.'):
        self.message = message
        super().__init__(message)

class RateLimited(commands.CommandError):
    def __init__(self, message='You are being rate limited. Take a chill pill.'):
        self.message = message
        super().__init__(message)