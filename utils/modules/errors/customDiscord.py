from discord.ext import commands
from utils.modules.core.i18n import t


class InteractionCheck(commands.CommandError):
    def __init__(self, message=None):
        self.message = message or t('responses.errors.interactionCheck', locale='en')
        super().__init__(self.message)


class RateLimited(commands.CommandError):
    def __init__(self, message=None):
        self.message = message or t('responses.errors.rateLimited', locale='en')
        super().__init__(self.message)


class WebhookRateLimit(commands.CommandError):
    def __init__(self, message=None):
        self.message = message or t('responses.errors.webhookRateLimit', locale='en')
        super().__init__(self.message)


class InvalidInput(commands.CommandError):
    def __init__(self, message=None):
        self.message = message or t('responses.errors.invalidInput', locale='en')
        super().__init__(self.message)


class InvalidInvite(commands.CommandError):
    def __init__(self, message=None):
        self.message = message or t('responses.errors.invalidInvite', locale='en')
        super().__init__(self.message)


class WebhookError(commands.CommandError):
    def __init__(self, message=None):
        self.message = message or t('responses.errors.webhookError', locale='en')
        super().__init__(self.message)


class NotConnected(commands.CommandError):
    def __init__(self, message=None):
        self.message = message or t('responses.errors.notConnected', locale='en')
        super().__init__(self.message)


class NoInteraction(commands.CommandError):
    def __init__(self, message=None):
        self.message = message or t('responses.errors.noInteraction', locale='en')
        super().__init__(self.message)
