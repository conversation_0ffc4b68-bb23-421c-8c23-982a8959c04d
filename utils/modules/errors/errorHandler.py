from typing import TYPE_CHECKING

import discord
from discord.ext import commands
from discord.ui import View, Button

import sentry_sdk as sdk
from utils.constants import logger

from utils.modules.errors import customDiscord
from utils.modules.core.i18n import t
from utils.constants import constants
from utils.utils import load_user_locale

if TYPE_CHECKING:
    from main import Bot


class ErrorButtons(View):
    def __init__(self, bot: 'Bot'):
        super().__init__()
        self.bot = bot

        self.add_item(
            Button(
                emoji=bot.emotes.code_icon,
                label='Support',
                url='https://discord.gg/8DhUA4HNpD',
                style=discord.ButtonStyle.link,
            )
        )
        self.add_item(
            Button(
                emoji=bot.emotes.wand_icon,
                label='Dashboard',
                url='https://interchat.tech/dashboard',
                style=discord.ButtonStyle.link,
            )
        )


def _send_to_sentry(error: Exception, source: commands.Context | discord.Interaction):
    """Send error details to Sen<PERSON> with comprehensive context"""
    if not constants.production:
        return

    is_interaction = isinstance(source, discord.Interaction)
    user = source.user if is_interaction else source.author
    guild = source.guild
    bot = source.client if is_interaction else source.bot

    with sdk.push_scope() as scope:
        scope.set_tag('error_type', type(error).__name__)
        scope.set_level('error')

        # User context
        scope.set_user({'id': str(user.id), 'username': str(user)})

        # Guild context
        if guild:
            scope.set_tag('guild_id', str(guild.id))
            scope.set_context(
                'guild',
                {
                    'name': guild.name,
                    'id': str(guild.id),
                    'member_count': guild.member_count,
                    'owner_id': str(guild.owner_id) if guild.owner_id else None,
                },
            )

        # Command context
        if not is_interaction and source.command:
            scope.set_tag('command_name', source.command.qualified_name)
            scope.set_context(
                'command',
                {
                    'name': source.command.qualified_name,
                    'cog': source.command.cog_name,
                    'invoked_with': source.invoked_with,
                },
            )
        elif is_interaction:
            if hasattr(source, 'command') and source.command:
                scope.set_tag('command_name', source.command.qualified_name)
                scope.set_context(
                    'interaction',
                    {
                        'command_name': source.command.qualified_name,
                        'command_type': getattr(source.command, 'type', None),
                        'data': str(source.data)[:500] if source.data else None,
                    },
                )

        # Channel context
        if hasattr(source, 'channel') and source.channel:
            channel = source.channel
            scope.set_context(
                'channel',
                {
                    'id': str(channel.id),
                    'name': getattr(channel, 'name', 'DM'),
                    'type': str(channel.type),
                    'nsfw': getattr(channel, 'nsfw', False),
                },
            )

        # Bot context
        scope.set_context(
            'bot',
            {
                'id': str(bot.user.id if bot.user else 0),
                'shard_id': getattr(bot, 'shard_id', None),
                'shard_count': getattr(bot, 'shard_count', None),
            },
        )

        # Additional error context based on error type
        if isinstance(error, discord.HTTPException):
            scope.set_context(
                'http_error',
                {
                    'status': error.status,
                    'code': error.code,
                    'text': str(error.text)[:200] if error.text else None,
                },
            )
        elif isinstance(error, commands.CommandError):
            scope.set_context(
                'command_error',
                {
                    'original_error': str(getattr(error, 'original', None)),
                },
            )

        return sdk.capture_exception(error)


async def error_handler(
    source: discord.Interaction['Bot'] | commands.Context['Bot'],
    error: Exception,
):
    original_error = error
    if isinstance(error, (commands.CommandInvokeError, commands.HybridCommandError)):
        error = error.original

    is_interaction = isinstance(source, discord.Interaction)
    user = source.user if is_interaction else source.author
    bot = source.client if is_interaction else source.bot
    emotes = bot.emotes

    locale = 'en'
    try:
        locale = await load_user_locale(source)
    except Exception as e:
        logger.warning(f'Could not load user locale during error handling: {e}')

    embed = discord.Embed(
        title=t('responses.errors.errorTitle', locale=locale), color=discord.Color.red()
    )

    if isinstance(error, commands.MissingRequiredArgument):
        embed.description = t(
            'responses.errors.missingArgument',
            locale=locale,
            x_icon=emotes.x_icon,
            param=error.param.name,
        )
    elif isinstance(error, commands.BadArgument):
        embed.description = f'{emotes.x_icon} Invalid input. Please try again.'
    elif isinstance(error, discord.NotFound):
        embed.description = f'{emotes.x_icon} Asset not found. Please check my permissions.'
    elif isinstance(error, discord.Forbidden):
        embed.description = f"{emotes.x_icon} I don't have permission to do that."
    elif isinstance(error, discord.HTTPException) and error.code == 10062:
        embed.description = f"{emotes.x_icon} Discord couldn't process that. Try again."
    elif isinstance(error, commands.MissingPermissions):
        embed.title = 'Insufficient Permissions'
        embed.description = f"{emotes.x_icon} You don't have permission to do this."
    elif isinstance(error, customDiscord.InteractionCheck):
        embed.description = f'{emotes.x_icon} You may not use this interaction.'
    elif isinstance(error, customDiscord.RateLimited):
        embed.description = f"{emotes.x_icon} You're being rate limited. Slow down."
    elif isinstance(error, customDiscord.WebhookRateLimit):
        embed.description = f'{emotes.x_icon} You have reached the webhook creation ratelimit. Please wait and try again soon.'
    elif isinstance(error, ValueError):
        embed.description = (
            f'{emotes.x_icon} You have not provided valid input, was it the correct type?'
        )
    elif isinstance(error, customDiscord.InvalidInput):
        embed.description = f'{emotes.x_icon} You have not provided valid input.'
    elif isinstance(error, customDiscord.InvalidInvite):
        embed.description = f'{emotes.x_icon} This invite is expired, or invalid. Please try again.'
    elif isinstance(error, customDiscord.WebhookError):
        embed.description = f'{emotes.x_icon} Uh oh! Something went wrong with our webhook service. Do I have the correct permissions?'
    elif isinstance(error, customDiscord.NotConnected):
        embed.description = f'{emotes.x_icon} I could not find a hub connected in this channel.'
    elif isinstance(error, customDiscord.NoInteraction):
        embed.description = f'{emotes.x_icon} {error.message}'
    elif isinstance(error, (commands.CheckFailure, commands.CommandNotFound)):
        return
    else:
        logger.error(
            'An unexpected error occurred and was caught by the global error handler:',
            exc_info=original_error,
        )
        error_id = _send_to_sentry(original_error, source) or 'unknown'

        embed.description = t('responses.errors.whoops', locale, x_icon=emotes.x_icon)
        embed.add_field(name='Error ID', value=f'`{error_id}`', inline=True)
        embed.title = t('responses.errors.errorTitle', locale)

    view = ErrorButtons(bot)
    try:
        if is_interaction:
            if not source.response.is_done():
                await source.response.send_message(embed=embed, view=view, ephemeral=True)
            else:
                await source.followup.send(embed=embed, view=view, ephemeral=True)
        else:
            await source.send(embed=embed, view=view)
    except discord.Forbidden:
        try:
            await user.send(embed=embed, view=view)
        except Exception:
            pass
