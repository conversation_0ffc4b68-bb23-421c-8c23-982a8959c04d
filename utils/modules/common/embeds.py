"""Centralized embed creation utilities to reduce code duplication."""

from typing import TYPE_CHECKING, Any, Optional, Dict, List
import discord
from utils.modules.core.i18n import t

if TYPE_CHECKING:
    from main import Bot


class EmbedBuilder:
    def __init__(self, bot: 'Bot', locale: str = 'en'):
        self.bot = bot
        self.locale = locale
        self.constants = bot.constants

    def create_error_embed(
        self,
        title: Optional[str] = None,
        description: str = '',
        *,
        fields: Optional[List[Dict[str, Any]]] = None,
        footer: Optional[str] = None,
    ) -> discord.Embed:
        """Create a standardized error embed."""
        if title is None:
            title = t('ui.common.titles.error', locale=self.locale)

        embed = discord.Embed(
            title=title,
            description=description,
            color=discord.Color.red(),
        )

        if fields:
            for field in fields:
                embed.add_field(**field)

        if footer:
            embed.set_footer(text=footer)

        return embed

    def create_success_embed(
        self,
        title: Optional[str] = None,
        description: str = '',
        *,
        fields: Optional[List[Dict[str, Any]]] = None,
        footer: Optional[str] = None,
    ) -> discord.Embed:
        """Create a standardized success embed."""
        if title is None:
            title = t('ui.common.titles.success', locale=self.locale)

        embed = discord.Embed(
            title=title,
            description=description,
            color=discord.Color.green(),
        )

        if fields:
            for field in fields:
                embed.add_field(**field)

        if footer:
            embed.set_footer(text=footer)

        return embed

    def create_info_embed(
        self,
        title: str,
        description: str = '',
        *,
        fields: Optional[List[Dict[str, Any]]] = None,
        footer: Optional[str] = None,
    ) -> discord.Embed:
        """Create a standardized info embed."""
        embed = discord.Embed(
            title=title,
            description=description,
            color=self.constants.color,
        )

        if fields:
            for field in fields:
                embed.add_field(**field)

        if footer:
            embed.set_footer(text=footer)

        return embed

    def create_warning_embed(
        self,
        title: str,
        description: str = '',
        *,
        fields: Optional[List[Dict[str, Any]]] = None,
        footer: Optional[str] = None,
    ) -> discord.Embed:
        """Create a standardized warning embed."""
        embed = discord.Embed(
            title=f'{self.bot.emotes.alert_icon} {title}',
            description=description,
            color=discord.Color.orange(),
        )

        if fields:
            for field in fields:
                embed.add_field(**field)

        if footer:
            embed.set_footer(text=footer)

        return embed


# Convenience functions for quick embed creation
def create_error_embed(
    bot: 'Bot', description: str, title: Optional[str] = None, locale: str = 'en'
) -> discord.Embed:
    """Quick error embed creation."""
    builder = EmbedBuilder(bot, locale)
    return builder.create_error_embed(title, description)


def create_success_embed(
    bot: 'Bot', description: str, title: Optional[str] = None, locale: str = 'en'
) -> discord.Embed:
    """Quick success embed creation."""
    builder = EmbedBuilder(bot, locale)
    return builder.create_success_embed(title, description)


def create_info_embed(
    bot: 'Bot', title: str, description: str = '', locale: str = 'en'
) -> discord.Embed:
    """Quick info embed creation."""
    builder = EmbedBuilder(bot, locale)
    return builder.create_info_embed(title, description)


# Common error messages
class CommonErrors:
    """Common error messages used across the bot."""

    @staticmethod
    def no_moderated_hubs(bot: 'Bot', locale: str = 'en') -> discord.Embed:
        """No moderated hubs error."""
        return create_error_embed(
            bot,
            t(
                'responses.moderation.errors.noModeratedHubs',
                locale=locale,
                x_icon=bot.emotes.x_icon,
            ),
            locale=locale,
        )

    @staticmethod
    def original_message_not_found(bot: 'Bot', locale: str = 'en') -> discord.Embed:
        """Original message not found error."""
        return create_error_embed(
            bot,
            t(
                'responses.moderation.errors.originalMessageNotFound',
                locale=locale,
                x_icon=bot.emotes.x_icon,
            ),
            locale=locale,
        )

    @staticmethod
    def fetch_author_or_server_failed(bot: 'Bot', locale: str = 'en') -> discord.Embed:
        """Fetch author or server failed error."""
        return create_error_embed(
            bot,
            t(
                'responses.moderation.errors.fetchAuthorOrServerFailed',
                locale=locale,
                x_icon=bot.emotes.x_icon,
            ),
            locale=locale,
        )

    @staticmethod
    def hub_not_found_for_message(bot: 'Bot', locale: str = 'en') -> discord.Embed:
        """Hub not found for message error."""
        return create_error_embed(
            bot,
            t(
                'responses.moderation.errors.hubNotFoundForMessage',
                locale=locale,
                x_icon=bot.emotes.x_icon,
            ),
            locale=locale,
        )

    @staticmethod
    def not_moderator_for_hub(bot: 'Bot', locale: str = 'en') -> discord.Embed:
        """Not moderator for hub error."""
        return create_error_embed(
            bot,
            t(
                'responses.moderation.errors.notModeratorForHub',
                locale=locale,
                x_icon=bot.emotes.x_icon,
            ),
            locale=locale,
        )

    @staticmethod
    def hub_message_only(bot: 'Bot', locale: str = 'en') -> discord.Embed:
        """Hub message only error."""
        return create_error_embed(
            bot,
            t('commands.report.errors.hubMessageOnly', locale=locale, x_icon=bot.emotes.x_icon),
            locale=locale,
        )

    @staticmethod
    def cannot_report_self(bot: 'Bot', locale: str = 'en') -> discord.Embed:
        """Cannot report self error."""
        return create_error_embed(
            bot,
            t('commands.report.errors.cannotReportSelf', locale=locale, x_icon=bot.emotes.x_icon),
            locale=locale,
        )

    @staticmethod
    def already_connected(bot: 'Bot', locale: str = 'en') -> discord.Embed:
        """Already connected error."""
        return create_error_embed(
            bot,
            f'{bot.emotes.x_icon} {t("ui.hub.connect.errors.alreadyConnected", locale=locale)}',
            locale=locale,
        )

    @staticmethod
    def connection_failed(bot: 'Bot', locale: str = 'en') -> discord.Embed:
        """Connection failed error."""
        return create_error_embed(
            bot,
            f'{bot.emotes.x_icon} Connection failed. Please try again in a moment.',
            locale=locale,
        )

    @staticmethod
    def hub_not_found(bot: 'Bot', locale: str = 'en') -> discord.Embed:
        """Hub not found error."""
        return create_error_embed(
            bot,
            t('ui.common.messages.hubNotFound', locale=locale),
            locale=locale,
        )

    @staticmethod
    def no_hubs_found(bot: 'Bot', locale: str = 'en', support_invite: str = '') -> discord.Embed:
        """No hubs found error."""
        return create_error_embed(
            bot,
            t('ui.hub.all.errors.notFound', locale=locale, supportServer=support_invite),
            locale=locale,
        )

    @staticmethod
    def rules_not_found(bot: 'Bot', locale: str = 'en') -> discord.Embed:
        """Hub rules not found."""
        return create_error_embed(
            bot,
            t('commands.rules.errors.noHub', locale=locale),
            locale=locale,
        )

    @staticmethod
    def not_connected_to_hub(bot: 'Bot', locale: str = 'en') -> discord.Embed:
        """Not connected to hub error."""
        return create_error_embed(
            bot,
            t('commands.rules.errors.notConnected', locale=locale),
            locale=locale,
        )

    @staticmethod
    def already_connected_to_hub(bot: 'Bot', locale: str = 'en') -> discord.Embed:
        """Already connected to hub error."""
        return create_error_embed(
            bot,
            f'{bot.emotes.x_icon} {t("ui.hub.connect.errors.alreadyConnected", locale=locale)}',
            locale=locale,
        )

    @staticmethod
    def no_hub_specified(bot: 'Bot', locale: str = 'en') -> discord.Embed:
        """No hub specified error."""
        return create_error_embed(
            bot,
            t('commands.rules.errors.noHub', locale=locale),
            locale=locale,
        )

    @staticmethod
    def manager_permission_required(bot: 'Bot', locale: str = 'en') -> discord.Embed:
        """Manager permission required error."""
        return create_error_embed(
            bot,
            f'{bot.emotes.x_icon} {t("responses.moderation.permissions.managerRequired", locale=locale)}',
            locale=locale,
        )

    @staticmethod
    def infraction_not_found(bot: 'Bot', locale: str = 'en') -> discord.Embed:
        """Infraction not found error."""
        return create_error_embed(
            bot,
            f'{bot.emotes.x_icon} {t("responses.infractions.delete.notFound", locale=locale)}',
            locale=locale,
        )

    @staticmethod
    def hub_not_found_by_name(bot: 'Bot', hub_name: str, locale: str = 'en') -> discord.Embed:
        """Hub not found by name error."""
        return create_error_embed(
            bot,
            t('commands.rules.errors.hubNotFound', locale=locale, hubName=hub_name),
            locale=locale,
        )


# Common success messages
class CommonSuccess:
    """Common success messages used across the bot."""

    @staticmethod
    def hub_connection_success(
        bot: 'Bot',
        hub_name: str,
        locale: str = 'en',
        author: Optional['discord.User | discord.Member'] = None,
        channel: Optional['discord.abc.GuildChannel'] = None,
    ) -> discord.Embed:
        embed = create_success_embed(
            bot,
            f'{bot.emotes.tick} {t("ui.hub.connect.success.description", locale=locale, hubName=hub_name)}',
            t('ui.hub.connect.success.title', locale=locale),
            locale=locale,
        )
        if author:
            embed.set_author(name=f'@{author.name}', icon_url=author.display_avatar.url)
        if channel:
            embed.set_footer(
                text=t('ui.hub.connect.success.fieldValue', locale=locale, channel=channel)
            )
        return embed

    @staticmethod
    def hub_disconnect_success(
        bot: 'Bot',
        locale: str = 'en',
        author: Optional['discord.User | discord.Member'] = None,
        channel: Optional['discord.abc.GuildChannel'] = None,
    ) -> discord.Embed:
        embed = create_success_embed(
            bot,
            f'{bot.emotes.tick} {t("ui.hub.disconnect.description", locale=locale)}',
            t('ui.hub.disconnect.title', locale=locale),
            locale=locale,
        )
        if author:
            embed.set_author(name=f'@{author.name}', icon_url=author.display_avatar.url)
        if channel:
            embed.set_footer(
                text=t('ui.hub.disconnect.fieldValue', locale=locale, channel=channel.name)
            )
        return embed

    @staticmethod
    def hub_deleted_success(bot: 'Bot', locale: str = 'en') -> discord.Embed:
        return create_success_embed(
            bot,
            f'{bot.emotes.tick} {t("ui.hub.delete.description", locale=locale)}',
            locale=locale,
        )
