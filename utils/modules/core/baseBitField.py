from enum import IntFlag
from typing import Self, TypeVar, Generic, Dict, Type

T = TypeVar('T', bound=IntFlag)


class BaseBitField(Generic[T]):
    FLAGS: Dict[str, T]  # override this in subclass

    def __init__(self, value: int = 0):
        self.value: T = self._enum()(value)

    @classmethod
    def _enum(cls) -> Type[T]:
        # Grab any value from FLAGS to infer the enum class
        return next(iter(cls.FLAGS.values())).__class__

    def has(self, *flags: str) -> bool:
        return all((self.value & self.FLAGS[f]) for f in flags)

    def add(self, *flags: str) -> Self:
        for f in flags:
            self.value |= self.FLAGS[f]
        return self

    def remove(self, *flags: str) -> Self:
        for f in flags:
            self.value &= ~self.FLAGS[f]
        return self

    def toggle(self, *flags: str) -> Self:
        for f in flags:
            self.value ^= self.FLAGS[f]
        return self

    def serialize(self) -> Dict[str, bool]:
        return {k: bool(self.value & v) for k, v in self.FLAGS.items()}

    def get(self) -> int:
        return self.value.value

    def __repr__(self):
        return f'<{self.__class__.__name__} value={self.get()} settings={self.serialize()}>'
