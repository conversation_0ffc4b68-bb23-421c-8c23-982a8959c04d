from typing import Optional
import discord
from discord.abc import GuildChannel
from sqlalchemy import select

from utils.constants import logger
from utils.modules.core.i18n import t
from utils.modules.core.db.models import Connection
from utils.modules.core.rateLimit import webhook_rate_limit

from typing import TYPE_CHECKING, Tuple, List

if TYPE_CHECKING:
    from main import Bot


async def get_webhook(bot: 'Bot', channel: GuildChannel) -> Optional[discord.Webhook]:
    """Get the webhook for a channel, or None if not found."""
    logger.debug(
        f'Looking for existing webhook in channel {channel.id} (type: {type(channel).__name__})'
    )

    if not isinstance(channel, (discord.TextChannel, discord.ForumChannel)):
        logger.debug(f'Channel {channel.id} is not a text/forum channel, skipping webhook lookup')
        return None

    try:
        webhooks = await channel.webhooks()
        logger.debug(f'Found {len(webhooks)} total webhooks in channel {channel.id}')

        for webhook in webhooks:
            if bot.user and webhook.user and (webhook.user.id == bot.user.id):
                logger.debug(f'Found InterChat webhook {webhook.id} in channel {channel.id}')
                return webhook

        logger.debug(f'No InterChat webhook found in channel {channel.id}')
    except Exception as e:
        logger.error(f'Failed to fetch webhooks for channel {channel.id}: {e}')

    return None


async def fetch_or_create_webhook(bot: 'Bot', channel: GuildChannel) -> Optional[discord.Webhook]:
    """Get or create a webhook for the specified channel."""
    logger.debug(
        f'Fetching or creating webhook for channel {channel.id} (type: {type(channel).__name__})'
    )

    if isinstance(channel, discord.Thread):
        parent = channel.parent
        if parent:
            logger.debug(f'Channel {channel.id} is a thread, using parent channel {parent.id}')
            webhook = await get_webhook(bot, parent)
            if not webhook:
                logger.info(f'Creating new webhook for parent channel {parent.id}')
                try:
                    webhook = await parent.create_webhook(name='InterChat Core 1')
                    logger.info(
                        f'Successfully created webhook {webhook.id} for parent channel {parent.id}'
                    )
                except Exception as e:
                    logger.error(f'Failed to create webhook for parent channel {parent.id}: {e}')
                    return None
            return webhook
        else:
            logger.warning(f'Thread {channel.id} has no parent channel')
            return None

    elif isinstance(channel, discord.TextChannel):
        webhook = await get_webhook(bot, channel)
        if not webhook:
            logger.info(f'Creating new webhook for text channel {channel.id}')
            try:
                webhook = await channel.create_webhook(name='InterChat Core 1')
                logger.info(f'Successfully created webhook {webhook.id} for channel {channel.id}')
            except Exception as e:
                logger.error(f'Failed to create webhook for channel {channel.id}: {e}')
                return None
        return webhook
    else:
        logger.warning(
            f'Cannot create webhook for channel type {type(channel).__name__} (ID: {channel.id})'
        )
        return None


async def get_and_cleanup_webhooks(bot: 'Bot', channel: GuildChannel):
    """
    Get all webhooks for a channel and delete the bot's webhook if it exists.
    """
    logger.debug(f'Cleaning up webhooks for channel {channel.id}')

    parent = channel.parent if isinstance(channel, discord.Thread) else channel

    if not isinstance(parent, (discord.TextChannel, discord.ForumChannel)):
        logger.debug(
            f'Cannot cleanup webhooks for channel type {type(parent).__name__ if parent else "None"}'
        )
        return

    try:
        webhooks = await parent.webhooks()
        logger.debug(f'Found {len(webhooks)} webhooks to check for cleanup in channel {parent.id}')
    except Exception as e:
        logger.error(f'Failed to fetch webhooks for cleanup in channel {parent.id}: {e}')
        return

    deleted_count = 0
    for webhook in webhooks:
        if bot.user and webhook.user and webhook.user.id == bot.user.id:
            try:
                await webhook.delete()
                deleted_count += 1
                logger.debug(f'Deleted InterChat webhook {webhook.id} from channel {parent.id}')
            except Exception as e:
                logger.warning(
                    f'Failed to delete webhook {webhook.id} from channel {parent.id}: {e}'
                )

    if deleted_count > 0:
        logger.info(f'Cleaned up {deleted_count} InterChat webhooks from channel {parent.id}')
    else:
        logger.debug(f'No InterChat webhooks found to clean up in channel {parent.id}')


async def validate_webhook(bot: 'Bot', channel: GuildChannel) -> Optional[discord.Webhook]:
    """
    Validate the webhook for a channel, re-creating it if necessary.
    Returns:
        The webhook if it exists or is created, None if the channel is not suitable.
    """
    logger.debug(f'Validating webhook for channel {channel.id} (type: {type(channel).__name__})')

    webhook = await get_webhook(bot, channel)

    if not webhook:
        logger.info(f'No existing webhook found for channel {channel.id}, creating new one')
        await webhook_rate_limit(channel)
        target = channel.parent if isinstance(channel, discord.Thread) else channel

        if isinstance(target, (discord.TextChannel, discord.ForumChannel)):
            try:
                webhook = await target.create_webhook(name='InterChat Core 1')
                logger.info(
                    f'Successfully created new webhook {webhook.id} for channel {channel.id}'
                )
            except Exception as e:
                logger.error(f'Failed to create webhook for channel {channel.id}: {e}')
                return None
        else:
            logger.warning(
                f'Cannot create webhook for channel type {type(target).__name__ if target else "None"} '
                f'(ID: {channel.id})'
            )
            return None
    else:
        logger.debug(f'Found existing webhook {webhook.id} for channel {channel.id}')

    if not webhook:
        logger.error(f'Webhook validation failed for channel {channel.id}: no webhook available')
        return None

    # Update the connection with the webhook URL
    logger.debug(f'Updating connection webhook URL for channel {channel.id}')
    async with bot.db.get_session() as session:
        stmt = select(Connection).where(Connection.channelId == str(channel.id)).limit(1)
        result = await session.scalar(stmt)

        if not result:
            return None

        old_webhook_url = result.webhookURL
        result.webhookURL = webhook.url
        await session.commit()

        if old_webhook_url != webhook.url:
            logger.info(
                f'Updated webhook URL for channel {channel.id}: {old_webhook_url[:50]}... -> {webhook.url[:50]}...'
            )
        else:
            logger.debug(f'Webhook URL unchanged for channel {channel.id}')

    return webhook


async def fix_connections(
    bot: 'Bot', guild: discord.Guild, locale
) -> Tuple[List[Tuple[Connection, str]], List[Tuple[Connection, str]]]:
    user_error: List[Tuple[Connection, str]] = []
    fixed: List[Tuple[Connection, str]] = []

    logger.info(f'Fixing connections for guild {guild.id} ({guild.name})')

    async with bot.db.get_session() as session:
        stmt = select(Connection).where(Connection.serverId == str(guild.id))
        connections = (await session.execute(stmt)).scalars().all()

        logger.debug(f'Found {len(connections)} connections to check in guild {guild.id}')

        for conn in connections:
            conn = await session.merge(conn)
            logger.debug(f'Checking connection {conn.id} for channel {conn.channelId}')

            channel = guild.get_channel(int(conn.channelId))
            if not channel:
                logger.warning(f'Channel {conn.channelId} not found, deleting connection {conn.id}')
                await session.delete(conn)
                user_error.append(
                    (
                        conn,
                        f'{bot.emotes.trash_icon} {t("commands.connections.fix.responses.errors.channelDeleted", locale)}',
                    )
                )
                continue

            permissions = channel.permissions_for(guild.me)
            if not permissions.view_channel:
                logger.warning(f'Missing view_channel permission for channel {conn.channelId}')
                user_error.append(
                    (
                        conn,
                        f'{bot.emotes.globe_icon} {t("commands.connections.fix.responses.errors.permissionsView", locale)}',
                    )
                )
                continue
            if not permissions.manage_webhooks:
                logger.warning(f'Missing manage_webhooks permission for channel {conn.channelId}')
                user_error.append(
                    (
                        conn,
                        f'{bot.emotes.link_icon} {t("commands.connections.fix.responses.errors.permissionsWebhook", locale)}',
                    )
                )
                continue
            if not permissions.send_messages:
                logger.warning(f'Missing send_messages permission for channel {conn.channelId}')
                user_error.append(
                    (
                        conn,
                        f'{bot.emotes.chat_icon} {t("commands.connections.fix.responses.errors.permissionsSend", locale)}',
                    )
                )
                continue

            webhook = await get_webhook(bot, channel)

            if not webhook:
                logger.info(f'No webhook found for channel {conn.channelId}, creating new one')
                await webhook_rate_limit(channel)
                target = channel.parent if isinstance(channel, discord.Thread) else channel

                if isinstance(target, (discord.TextChannel, discord.ForumChannel)):
                    try:
                        webhook = await target.create_webhook(name='InterChat Core 1')
                        conn.webhookURL = webhook.url
                        logger.info(
                            f'Created new webhook {webhook.id} for channel {conn.channelId}'
                        )
                        fixed.append(
                            (
                                conn,
                                f'{bot.emotes.tick} {t("commands.connections.fix.responses.success.fixed", locale)}',
                            )
                        )
                    except Exception as e:
                        logger.error(f'Failed to create webhook for channel {conn.channelId}: {e}')
                        user_error.append(
                            (
                                conn,
                                f'{bot.emotes.link_icon} Failed to create webhook: {str(e)}',
                            )
                        )
                else:
                    logger.warning(
                        f'Cannot create webhook for channel type {type(target).__name__} (ID: {conn.channelId})'
                    )
                    user_error.append(
                        (
                            conn,
                            f'{bot.emotes.link_icon} Unsupported channel type for webhook creation',
                        )
                    )
            else:
                logger.debug(
                    f'Webhook {webhook.id} already exists and is valid for channel {conn.channelId}'
                )
                fixed.append(
                    (
                        conn,
                        f'{bot.emotes.tick} {t("commands.connections.fix.responses.success.valid", locale)}',
                    )
                )

        await session.commit()
        logger.info(
            f'Connection fix completed for guild {guild.id}: {len(fixed)} fixed, {len(user_error)} errors'
        )

    return fixed, user_error
