from typing import TYPE_CHECKING, Optional, Set, List
from dataclasses import dataclass, field

from sqlalchemy import and_, select

from utils.modules.core.db.models import HubModerator, HubModeratorRole
from utils.modules.common.database import DatabaseQueries

if TYPE_CHECKING:
    from sqlalchemy.ext.asyncio import AsyncSession


@dataclass
class BypassConfig:
    """Configuration for bypass checking."""

    exempt_roles: Set[str] = field(default_factory=set)  # Discord role IDs that are exempt
    exempt_moderators: bool = True
    exempt_managers: bool = True
    exempt_owners: bool = True


class BypassChecker:
    def __init__(self, db_session: 'AsyncSession'):
        self.session = db_session

    async def check_bypass(
        self,
        hub_id: str,
        user_id: str,
        message: str,
        user_roles: Optional[List[str]] = None,
        bypass_config: Optional[BypassConfig] = None,
    ) -> Optional[str]:
        """
        Check if a user should bypass profanity filtering.

        Returns:
            Bypass reason if user should bypass, None otherwise
        """
        if bypass_config is None:
            bypass_config = BypassConfig()

        if bypass_config.exempt_owners:
            if await self._is_hub_owner(hub_id, user_id):
                return 'hub_owner'

        # Check hub moderation roles
        if bypass_config.exempt_moderators or bypass_config.exempt_managers:
            mod_role = await self._get_moderator_role(hub_id, user_id)
            if mod_role:
                if mod_role == HubModeratorRole.MANAGER and bypass_config.exempt_managers:
                    return 'hub_manager'
                elif mod_role == HubModeratorRole.MODERATOR and bypass_config.exempt_moderators:
                    return 'hub_moderator'

        # Check Discord role exemptions
        if bypass_config.exempt_roles and user_roles:
            for role_id in user_roles:
                if role_id in bypass_config.exempt_roles:
                    return f'exempt_role_{role_id}'

        return None

    async def _is_hub_owner(self, hub_id: str, user_id: str) -> bool:
        """Check if user is the owner of the hub."""
        hub = await DatabaseQueries.fetch_hub_by_id(self.session, hub_id)
        return bool(hub and hub.ownerId == user_id)

    async def _get_moderator_role(self, hub_id: str, user_id: str) -> Optional[HubModeratorRole]:
        """Get the moderator role for a user in a hub."""
        result = await self.session.execute(
            select(HubModerator).where(
                and_(HubModerator.hubId == hub_id, HubModerator.userId == user_id)
            )
        )
        moderator = result.scalar_one_or_none()
        return moderator.role if moderator else None
