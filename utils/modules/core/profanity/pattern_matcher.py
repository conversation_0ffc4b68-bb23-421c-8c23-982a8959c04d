import re
import unicodedata
from typing import Dict, List, Optional, Set, Tuple
from dataclasses import dataclass

from utils.modules.core.db.models import PatternMatchType


@dataclass
class MatchResult:
    """Result of a pattern match operation."""

    matched: bool
    pattern: str
    match_type: PatternMatchType
    matched_word: str
    start_pos: int
    end_pos: int
    pattern_id: Optional[str] = None


class PatternMatcher:
    """Pattern matching engine for profanity filtering."""

    def __init__(self):
        self._compiled_patterns: Dict[PatternMatchType, List[Tuple[str, re.Pattern, str]]] = {
            PatternMatchType.EXACT: [],
            PatternMatchType.PREFIX: [],
            PatternMatchType.SUFFIX: [],
            PatternMatchType.WILDCARD: [],
        }
        self._whitelist: Set[str] = set()

    def add_pattern(
        self, pattern: str, match_type: PatternMatchType, pattern_id: Optional[str] = None
    ) -> bool:
        """Add a pattern to the matcher."""
        if not pattern or not pattern.strip():
            return False

        # Normalize and clean the pattern
        normalized_pattern = self._normalize_text(pattern.strip())

        # Validate pattern based on type
        if not self._validate_pattern(normalized_pattern, match_type):
            return False

        # Compile the pattern
        compiled_pattern = self._compile_pattern(normalized_pattern, match_type)
        if compiled_pattern is None:
            return False

        # Add to appropriate list
        self._compiled_patterns[match_type].append(
            (normalized_pattern, compiled_pattern, pattern_id or '')
        )
        return True

    def add_whitelist_word(self, word: str) -> bool:
        """Add a word to the whitelist (words that should never be blocked)."""
        if not word or not word.strip():
            return False

        normalized_word = self._normalize_text(word.strip().lower())
        self._whitelist.add(normalized_word)
        return True

    def remove_whitelist_word(self, word: str) -> bool:
        """Remove a word from the whitelist."""
        normalized_word = self._normalize_text(word.strip().lower())
        if normalized_word in self._whitelist:
            self._whitelist.remove(normalized_word)
            return True
        return False

    def check_message(self, message: str) -> List[MatchResult]:
        """Check a message against all patterns."""
        if not message or not message.strip():
            return []

        # Normalize the message
        normalized_message = self._normalize_text(message)

        # Extract words for whitelist checking
        words = self._extract_words(normalized_message)

        matches = []

        # Check each pattern type
        for match_type, patterns in self._compiled_patterns.items():
            for original_pattern, compiled_pattern, pattern_id in patterns:
                # Find all matches for this pattern
                for match in compiled_pattern.finditer(normalized_message):
                    matched_word = match.group()

                    # Skip if the matched word is whitelisted
                    if self._is_whitelisted(matched_word, words):
                        continue

                    # Create match result
                    result = MatchResult(
                        matched=True,
                        pattern=original_pattern,
                        match_type=match_type,
                        matched_word=matched_word,
                        start_pos=match.start(),
                        end_pos=match.end(),
                        pattern_id=pattern_id,
                    )
                    matches.append(result)

        return matches

    def clear_patterns(self, match_type: Optional[PatternMatchType] = None) -> None:
        """
        Clear patterns of a specific type or all patterns.: Type of patterns to clear, or None to clear all
        """
        if match_type is None:
            for pattern_list in self._compiled_patterns.values():
                pattern_list.clear()
        else:
            self._compiled_patterns[match_type].clear()

    def get_pattern_count(self, match_type: Optional[PatternMatchType] = None) -> int:
        """Get the number of patterns loaded."""
        if match_type is None:
            return sum(len(patterns) for patterns in self._compiled_patterns.values())
        return len(self._compiled_patterns[match_type])

    def _normalize_text(self, text: str) -> str:
        """
        Normalize text for consistent matching.

        - Convert to lowercase
        - Normalize Unicode characters
        - Handle special characters that might be used to bypass filters
        """
        # Convert to lowercase
        text = text.lower()

        # Normalize Unicode (decompose accented characters)
        text = unicodedata.normalize('NFD', text)

        # Remove common bypass characters but preserve word structure
        # Replace common substitutions
        substitutions = {
            '@': 'a',
            '4': 'a',
            '3': 'e',
            '1': 'i',
            '!': 'i',
            '0': 'o',
            '5': 's',
            '7': 't',
            '+': 't',
            '$': 's',
            '|': 'l',
            '()': 'o',
            '[]': 'o',
            '{}': 'o',
            '*': '',
            '#': 'h',
        }

        for old, new in substitutions.items():
            text = text.replace(old, new)

        return text

    def _extract_words(self, text: str) -> Set[str]:
        """Extract individual words from text for whitelist checking."""
        # Use regex to find word boundaries
        words = re.findall(r'\b\w+\b', text)
        return set(word.lower() for word in words)

    def _is_whitelisted(self, matched_word: str, message_words: Set[str]) -> bool:
        """Check if a matched word should be whitelisted."""
        # Check exact match
        if matched_word.lower() in self._whitelist:
            return True

        # Check if any word in the message is whitelisted and contains the match
        for word in message_words:
            if word in self._whitelist and matched_word in word:
                return True

        return False

    def _validate_pattern(self, pattern: str, match_type: PatternMatchType) -> bool:
        """Validate a pattern based on its type."""
        if not pattern:
            return False

        # Check for invalid characters that could break regex
        invalid_chars = set('[]{}()*+?^$|\\')

        if match_type == PatternMatchType.EXACT:
            # Exact patterns should not contain regex special characters
            return not any(char in invalid_chars for char in pattern)
        elif match_type == PatternMatchType.PREFIX:
            # Prefix patterns should not start with * or contain other regex chars
            return not pattern.startswith('*') and not any(
                char in invalid_chars for char in pattern
            )
        elif match_type == PatternMatchType.SUFFIX:
            # Suffix patterns should not end with * or contain other regex chars
            return not pattern.endswith('*') and not any(char in invalid_chars for char in pattern)
        elif match_type == PatternMatchType.WILDCARD:
            # Wildcard patterns should not contain regex chars except for our wildcards
            return not any(char in invalid_chars for char in pattern)

        return False

    def _compile_pattern(self, pattern: str, match_type: PatternMatchType) -> Optional[re.Pattern]:
        """
        Compile a pattern into a regex based on its type.

        All patterns use word boundaries to prevent false positives.
        """
        try:
            if match_type == PatternMatchType.EXACT:
                # Exact match with word boundaries
                regex_pattern = r'\b' + re.escape(pattern) + r'\b'
            elif match_type == PatternMatchType.PREFIX:
                # Word starts with pattern
                regex_pattern = r'\b' + re.escape(pattern) + r'\w*\b'
            elif match_type == PatternMatchType.SUFFIX:
                # Word ends with pattern
                regex_pattern = r'\b\w*' + re.escape(pattern) + r'\b'
            elif match_type == PatternMatchType.WILDCARD:
                # Word contains pattern
                regex_pattern = r'\b\w*' + re.escape(pattern) + r'\w*\b'
            else:
                return None

            return re.compile(regex_pattern, re.IGNORECASE | re.UNICODE)
        except re.error:
            return None
