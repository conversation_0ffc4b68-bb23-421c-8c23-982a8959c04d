from datetime import datetime, timed<PERSON>ta
from typing import TYPE_CHECKING, Dict, List, Optional, Tuple
from dataclasses import dataclass
import asyncio

from sqlalchemy import and_, select
from sqlalchemy.orm import selectinload

from utils.modules.core.db.models import (
    AntiSwearRule,
    AntiSwearPattern,
    PatternMatchType,
    BlockWordAction,
    AlertSeverity,
)
from utils.modules.core.profanity.pattern_matcher import PatternMatcher, MatchResult
from utils.modules.core.profanity.actions import ActionExecutor
from utils.modules.core.profanity.bypass import BypassChecker
from utils.modules.events.eventDispatcher import HubEventType, create_hub_event, event_dispatcher

if TYPE_CHECKING:
    from sqlalchemy.ext.asyncio import AsyncSession


@dataclass
class FilterResult:
    """Result of profanity filter check."""

    blocked: bool
    matches: List[MatchResult]
    actions_executed: List[BlockWordAction]
    bypass_reason: Optional[str] = None
    severity: AlertSeverity = AlertSeverity.LOW
    rule_id: Optional[str] = None


@dataclass
class CachedRuleData:
    rule: Optional[AntiSwearRule]
    patterns: List[AntiSwearPattern]
    whitelists: List[str]
    loaded_at: datetime


@dataclass
class RuleInfo:
    """Plain rule info snapshot to avoid ORM attribute access after session closes."""

    id: str
    name: str
    enabled: bool
    actions: List[BlockWordAction]
    muteDurationMinutes: Optional[int]


class RuleShim:
    """Simple adapter to make RuleInfo compatible with ActionExecutor expectations."""

    def __init__(self, rule_info: RuleInfo):
        self.id = rule_info.id
        self.name = rule_info.name
        self.enabled = rule_info.enabled
        self.actions = rule_info.actions
        self.muteDurationMinutes = rule_info.muteDurationMinutes


class ProfanityFilterService:
    def __init__(self, db_session: 'AsyncSession', client_id: str):
        self.db = db_session
        self.pattern_matcher = PatternMatcher()
        self.action_executor = ActionExecutor(db_session, client_id)
        self.bypass_checker = BypassChecker(db_session)

        # caching with full rule data
        self._hub_cache: Dict[str, CachedRuleData] = {}
        self._matchers: Dict[str, PatternMatcher] = {}
        self._pattern_to_rule_cache: Dict[str, Dict[str, RuleInfo]] = {}
        self._cache_ttl = timedelta(minutes=15)  # Longer cache for better performance

    async def check_message(
        self,
        message: str,
        hub_id: str,
        user_id: str,
        channel_id: str,
        message_id: Optional[str] = None,
    ) -> FilterResult:
        """Check a message against profanity filters."""
        # Early exit for empty messages
        if not message or not message.strip():
            return FilterResult(blocked=False, matches=[], actions_executed=[])

        # Load rules and get matcher (cached)
        matcher = await self._ensure_rules_loaded(hub_id)
        if not matcher:
            return FilterResult(blocked=False, matches=[], actions_executed=[])

        # Check for bypass conditions (this should be fast if properly implemented)
        bypass_reason = await self.bypass_checker.check_bypass(hub_id, user_id, message)
        if bypass_reason:
            return FilterResult(
                blocked=False, matches=[], actions_executed=[], bypass_reason=bypass_reason
            )

        # Check message against patterns
        matches = matcher.check_message(message)
        if not matches:
            return FilterResult(blocked=False, matches=[], actions_executed=[])

        # Use cached pattern-to-rule mapping
        primary_match, rule = self._find_primary_match_cached(matches, hub_id)
        if not rule or not rule.enabled or not primary_match:
            return FilterResult(blocked=False, matches=matches, actions_executed=[])

        # Calculate severity (cached calculation)
        severity = self._calculate_severity(rule)

        # Execute actions and update stats in parallel
        actions_task = self.action_executor.execute_actions(
            rule.actions,
            hub_id=hub_id,
            user_id=user_id,
            channel_id=channel_id,
            message_id=message_id,
            triggered_word=primary_match.matched_word,
            rule=RuleShim(rule),  # type: ignore[arg-type]  # RuleShim has compatible interface
        )

        # Fire and forget for logging and stats (non-blocking)
        asyncio.create_task(
            self._log_violation(
                hub_id=hub_id,
                user_id=user_id,
                channel_id=channel_id,
                message_id=message_id,
                message_content=message,
                match=primary_match,
                rule_id=rule.id,
                rule_name=rule.name,
                severity=severity,
            )
        )

        actions_executed = await actions_task

        return FilterResult(
            blocked=BlockWordAction.BLOCK_MESSAGE in actions_executed,
            matches=matches,
            actions_executed=actions_executed,
            severity=severity,
            rule_id=rule.id,
        )

    async def reload_rules(self, hub_id: str) -> bool:
        """Force reload of rules for a specific hub."""
        try:
            # Clear cache for this hub
            self._hub_cache.pop(hub_id, None)
            self._matchers.pop(hub_id, None)
            self._pattern_to_rule_cache.pop(hub_id, None)

            await self._load_hub_rules(hub_id, force_reload=True)
            return True
        except Exception:
            return False

    async def add_pattern(
        self, hub_id: str, rule_id: str, pattern: str, match_type: PatternMatchType
    ) -> bool:
        """Add a new pattern to a rule."""
        # Validate the pattern first
        temp_matcher = PatternMatcher()
        if not temp_matcher.add_pattern(pattern, match_type, 'temp'):
            return False

        # Add to database
        try:
            db_pattern = AntiSwearPattern(ruleId=rule_id, pattern=pattern, matchType=match_type)
            self.db.add(db_pattern)
            await self.db.commit()

            # Reload rules for this hub
            await self.reload_rules(hub_id)
            return True
        except Exception:
            await self.db.rollback()
            return False

    async def _ensure_rules_loaded(self, hub_id: str) -> Optional[PatternMatcher]:
        """Ensure rules are loaded and up-to-date for a hub."""
        now = datetime.now()
        cached_data = self._hub_cache.get(hub_id)

        # Check if we need to reload
        if (
            cached_data is None
            or (now - cached_data.loaded_at) > self._cache_ttl
            or hub_id not in self._matchers
        ):
            await self._load_hub_rules(hub_id)

        return self._matchers.get(hub_id)

    async def _load_hub_rules(self, hub_id: str, force_reload: bool = False) -> None:
        """Load all enabled rules and patterns for a hub with aggressive caching."""
        # Single optimized query with joins and selectinload
        rules_with_data = (
            (
                await self.db.execute(
                    select(AntiSwearRule)
                    .options(
                        selectinload(AntiSwearRule.patterns), selectinload(AntiSwearRule.whitelists)
                    )
                    .where(and_(AntiSwearRule.hubId == hub_id, AntiSwearRule.enabled.is_(True)))
                )
            )
            .scalars()
            .all()
        )

        # Create new matcher for this hub
        matcher = PatternMatcher()
        pattern_to_rule_map: Dict[str, RuleInfo] = {}

        def _interpret_star_decorated(raw: str) -> Optional[tuple[str, PatternMatchType]]:
            """Interpret legacy star-decorated patterns.

            Accepted forms:
            - word       -> EXACT
            - word*      -> PREFIX
            - *word      -> SUFFIX
            - *word*     -> WILDCARD
            Any other use of '*' is considered invalid.
            """
            if '*' not in raw:
                return raw, PatternMatchType.EXACT

            if raw.startswith('*') and raw.endswith('*'):
                core = raw.strip('*')
                if raw.count('*') == 2 and core:
                    return core, PatternMatchType.WILDCARD
                return None
            if raw.startswith('*'):
                core = raw[1:]
                if '*' in core or not core:
                    return None
                return core, PatternMatchType.SUFFIX
            if raw.endswith('*'):
                core = raw[:-1]
                if '*' in core or not core:
                    return None
                return core, PatternMatchType.PREFIX
            return None

        # Process all rules and patterns
        for rule in rules_with_data:
            # Snapshot rule into plain dataclass to avoid ORM usage later
            rule_info = RuleInfo(
                id=rule.id,
                name=rule.name,
                enabled=bool(rule.enabled),
                actions=list(rule.actions or []),
                muteDurationMinutes=rule.muteDurationMinutes,
            )
            # Add patterns
            for pattern in rule.patterns:
                pat_text = pattern.pattern or ''
                pat_type = pattern.matchType

                # Normalize legacy star-decorated entries at load time
                if '*' in pat_text:
                    interpreted = _interpret_star_decorated(pat_text)
                    if interpreted is None:
                        # Skip invalid legacy pattern
                        continue
                    base, inferred_type = interpreted
                    pat_text = base
                    pat_type = inferred_type

                if matcher.add_pattern(pat_text, pat_type, pattern.id):
                    pattern_to_rule_map[pattern.id] = rule_info

            # Add whitelist words
            for whitelist_item in rule.whitelists:
                matcher.add_whitelist_word(whitelist_item.word)

        # Cache everything
        now = datetime.now()
        self._matchers[hub_id] = matcher
        self._pattern_to_rule_cache[hub_id] = pattern_to_rule_map

        # Cache the rule data (simplified version)
        self._hub_cache[hub_id] = CachedRuleData(
            rule=None,  # We don't need to store all rules, just the mapping
            patterns=[],
            whitelists=[],
            loaded_at=now,
        )

    def _find_primary_match_cached(
        self, matches: List[MatchResult], hub_id: str
    ) -> Tuple[Optional[MatchResult], Optional[RuleInfo]]:
        """Find the most severe match using cached rule mappings."""
        if not matches:
            return None, None

        pattern_to_rule_map = self._pattern_to_rule_cache.get(hub_id, {})
        if not pattern_to_rule_map:
            return matches[0], None

        # Find the match with the highest priority rule
        best_match = None
        best_rule = None
        best_priority = -1

        for match in matches:
            if match.pattern_id and match.pattern_id in pattern_to_rule_map:
                rule = pattern_to_rule_map[match.pattern_id]
                priority = self._calculate_rule_priority(rule)
                if priority > best_priority:
                    best_match = match
                    best_rule = rule
                    best_priority = priority

        return best_match or matches[0], best_rule

    def _calculate_rule_priority(self, rule: RuleInfo) -> int:
        """Calculate priority score for a rule based on its actions."""
        priority = 0
        action_weights = {
            BlockWordAction.BLOCK_MESSAGE: 1,
            BlockWordAction.WARN: 2,
            BlockWordAction.SEND_ALERT: 3,
            BlockWordAction.MUTE: 4,
            BlockWordAction.BAN: 5,
            BlockWordAction.BLACKLIST: 5,
        }

        for action in rule.actions:
            priority += action_weights.get(action, 0)

        return priority

    def _calculate_severity(self, rule: RuleInfo) -> AlertSeverity:
        """Calculate severity based on rule actions."""
        if BlockWordAction.BAN in rule.actions or BlockWordAction.BLACKLIST in rule.actions:
            return AlertSeverity.CRITICAL
        elif BlockWordAction.MUTE in rule.actions:
            return AlertSeverity.HIGH
        elif BlockWordAction.WARN in rule.actions:
            return AlertSeverity.MEDIUM
        else:
            return AlertSeverity.LOW

    async def _log_violation(
        self,
        hub_id: str,
        user_id: str,
        channel_id: str,
        message_id: Optional[str],
        message_content: str,
        match: MatchResult,
        rule_id: str,
        rule_name: str,
        severity: AlertSeverity,
    ) -> None:
        """Log violation asynchronously without blocking main flow."""
        try:
            event = create_hub_event(
                event_type=HubEventType.PROFANITY_VIOLATION,
                hub_id=hub_id,
                hub_name='Unknown Hub',
                target_user_id=user_id,
                target_user_name='Unknown User',
                message_id=message_id,
                channel_id=channel_id,
                original_content=message_content,
                extra_data={
                    'triggered_word': match.matched_word,
                    'pattern_id': match.pattern_id,
                    'rule_id': rule_id,
                    'rule_name': rule_name,
                    'severity': severity.value,
                },
            )
            await event_dispatcher.dispatch_hub_event(event)
        except Exception:
            # Silently fail - logging should not affect message processing
            pass
