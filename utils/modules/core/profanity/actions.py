from dataclasses import dataclass
from typing import TYPE_CHECKING, List, Optional


from utils.modules.core.db.models import (
    AntiSwearRule,
    BlockWordAction,
    InfractionType,
)
from utils.modules.events.eventDispatcher import HubEventType, create_hub_event, event_dispatcher
from utils.modules.services.moderationService import ModerationService
from utils.constants import logger

if TYPE_CHECKING:
    from sqlalchemy.ext.asyncio import AsyncSession


@dataclass
class ActionContext:
    hub_id: str
    user_id: str
    channel_id: str
    message_id: Optional[str]
    triggered_word: str
    rule: AntiSwearRule


class ActionExecutor:
    def __init__(self, db_session: 'AsyncSession', client_id: str):
        self.db = db_session
        self.modsvc = ModerationService(self.db)
        self.client_id = client_id

    async def execute_actions(
        self,
        actions: List[BlockWordAction],
        hub_id: str,
        user_id: str,
        channel_id: str,
        message_id: Optional[str],
        triggered_word: str,
        rule: AntiSwearRule,
    ) -> List[BlockWordAction]:
        """Execute a list of actions for a profanity violation."""
        context = ActionContext(
            hub_id=hub_id,
            user_id=user_id,
            channel_id=channel_id,
            message_id=message_id,
            triggered_word=triggered_word,
            rule=rule,
        )

        executed_actions = []

        for action in actions:
            try:
                success = await self._execute_single_action(action, context)
                if success:
                    executed_actions.append(action)
            except Exception as e:
                logger.error(f'Error executing action {action}: {e}', exc_info=e)
                continue

        return executed_actions

    async def _execute_single_action(self, action: BlockWordAction, context: ActionContext) -> bool:
        """Execute a single action."""
        if action == BlockWordAction.BLOCK_MESSAGE:
            return await self._block_message(context)
        elif action == BlockWordAction.WARN:
            return await self._warn_user(context)
        elif action == BlockWordAction.MUTE:
            return await self._mute_user(context)
        elif action == BlockWordAction.BAN or action == BlockWordAction.BLACKLIST:
            return await self._ban_user(context)
        elif action == BlockWordAction.SEND_ALERT:
            return await self._send_alert(context)
        else:
            return False

    async def _block_message(self, context: ActionContext) -> bool:
        """Block the message."""
        return True

    async def _warn_user(self, context: ActionContext) -> bool:
        """Issue a warning to the user."""

        # Create warning infraction
        await self.modsvc.create_infraction(
            hub_id=context.hub_id,
            user_id=context.user_id,
            mod_id=self.client_id,
            infraction_type=InfractionType.WARNING,
            reason=f"Profanity filter violation: '{context.triggered_word}' (Rule: {context.rule.name})",
            server_id=None,
            server_name=None,
        )
        return True

    async def _mute_user(self, context: ActionContext) -> bool:
        """Mute the user for a specified duration."""
        try:
            # Calculate expiry time
            duration_minutes = context.rule.muteDurationMinutes or 60  # Default 1 hour

            # Create new mute
            await self.modsvc.create_infraction(
                hub_id=context.hub_id,
                user_id=context.user_id,
                mod_id=self.client_id,
                infraction_type=InfractionType.MUTE,
                reason=f"Profanity filter violation: '{context.triggered_word}' (Rule: {context.rule.name})",
                duration_ms=duration_minutes * 60 * 1000,
            )
            return True
        except Exception:
            return False

    async def _ban_user(self, context: ActionContext) -> bool:
        """Ban the user from the hub. Always permanent."""
        try:
            # Create new ban
            await self.modsvc.create_infraction(
                hub_id=context.hub_id,
                user_id=context.user_id,
                mod_id=self.client_id,
                infraction_type=InfractionType.BAN,
                reason=f"Profanity filter violation: '{context.triggered_word}' (Rule: {context.rule.name})",
            )
            return True
        except Exception:
            return False

    async def _send_alert(self, context: ActionContext) -> bool:
        """Send an alert to moderators."""
        try:
            event = create_hub_event(
                event_type=HubEventType.PROFANITY_VIOLATION,
                hub_id=context.hub_id,
                hub_name='Unknown Hub',  # FIXME
                target_user_id=context.user_id,
                target_user_name='Unknown User',  # FIXME: this too
                message_id=context.message_id,
                channel_id=context.channel_id,
                original_content=None,
                extra_data={
                    'triggered_word': context.triggered_word,
                    'rule_name': context.rule.name,
                    'source': 'SEND_ALERT',
                },
            )
            await event_dispatcher.dispatch_hub_event(event)
            return True
        except Exception:
            return False
