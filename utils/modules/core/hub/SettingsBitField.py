from enum import IntFlag, auto

from utils.modules.core.baseBitField import BaseBitField


class HubSettings(IntFlag):
    REACTIONS = auto()
    HIDE_LINKS = auto()
    SPAM_FILTER = auto()
    BLOCK_INVITES = auto()
    USE_NICKNAMES = auto()
    BLOCK_NSFW = auto()
    ALLOW_VIDEOS = auto()


class HubSettingsBitField(BaseBitField[HubSettings]):
    FLAGS = {
        'Reactions': HubSettings.REACTIONS,
        'HideLinks': HubSettings.HIDE_LINKS,
        'SpamFilter': HubSettings.SPAM_FILTER,
        'BlockInvites': HubSettings.BLOCK_INVITES,
        'UseNicknames': HubSettings.USE_NICKNAMES,
        'BlockNSFW': HubSettings.BLOCK_NSFW,
        'AllowVideos': HubSettings.ALLOW_VIDEOS,
    }
