from typing import TYPE_CHECKING
import discord
from discord.ext import commands

from utils.modules.common.error_utils import send_error_message
from utils.modules.errors import customDiscord
from utils.modules.errors.errorHandler import error_handler
from utils.modules.hub.constants import HubPermissionLevel

if TYPE_CHECKING:
    from main import Bot


async def interaction_check(
    interaction: discord.Interaction['Bot'],
    invoked: discord.User | discord.Member,
    interacted: discord.User | discord.Member,
) -> bool:
    if invoked.id != interacted.id:
        try:
            raise customDiscord.InteractionCheck()
        except Exception as e:
            await error_handler(interaction, e)
        return False
    return True


def is_interchat_staff_direct(
    bot: 'Bot',
    user_id: int,
) -> bool:
    return user_id in bot.staff_ids


def is_interchat_staff(
    ctx: commands.Context['Bot'] | discord.Interaction['Bot'],
    user: discord.User | discord.Member | None = None,
) -> bool:
    bot = ctx.bot if isinstance(ctx, commands.Context) else ctx.client
    current_user = user

    if not user:
        current_user = ctx.author if isinstance(ctx, commands.Context) else ctx.user
    if not current_user:
        return False

    return current_user.id in bot.constants.auth_users or is_interchat_staff_direct(
        bot, current_user.id
    )


def is_interchat_staff_check():
    async def predicate(ctx: commands.Context['Bot'] | discord.Interaction['Bot']):
        if not is_interchat_staff(ctx):
            message = 'You must be an InterChat staff member to use this command. If you are looking for hub moderation, use `/ban`, `/mute`, `/warn` or `/modpanel`.'
            await send_error_message(ctx, message, ephemeral=True)
            return False
        return True

    return commands.check(predicate)


async def permission_check(level: HubPermissionLevel): ...
