import enum
from datetime import datetime
from typing import List, Optional

from cuid2 import Cuid
from sqlalchemy import (
    Text,
    <PERSON>olean,
    Column,
    DateTime,
    Float,
    ForeignKey,
    Index,
    Integer,
    String,
    Table,
    UniqueConstraint,
    func,
    sql,
    text,
)
from sqlalchemy.dialects.postgresql import ARRAY, ENUM
from sqlalchemy.orm import (
    DeclarativeBase,
    Mapped,
    MappedAsDataclass,
    mapped_column,
    relationship,
)


class Base(MappedAsDataclass, DeclarativeBase):
    pass


cuid = Cuid()


# Enums
class HubModeratorRole(enum.Enum):
    MODERATOR = 'MODERATOR'
    MANAGER = 'MANAGER'


HubModeratorRoleEnum = ENUM(
    HubModeratorRole,
    name='Role',
    create_type=True,
)


class HubActivityLevel(enum.Enum):
    LOW = 'LOW'  # <10 messages/day
    MEDIUM = 'MEDIUM'  # 10-100 messages/day
    HIGH = 'HIGH'  # >100 messages/day


HubActivityLevelEnum = ENUM(
    HubActivityLevel,
    name='HubActivityLevel',
    create_type=True,
)


class InfractionType(enum.Enum):
    BAN = 'BAN'
    BLACKLIST = 'BLACKLIST'  #: Deprecated: Use BAN instead
    MUTE = 'MUTE'
    WARNING = 'WARNING'


InfractionTypeEnum = ENUM(
    InfractionType,
    name='InfractionType',
    create_type=True,
)


class InfractionStatus(enum.Enum):
    ACTIVE = 'ACTIVE'
    REVOKED = 'REVOKED'
    APPEALED = 'APPEALED'


InfractionStatusEnum = ENUM(
    InfractionStatus,
    name='InfractionStatus',
    create_type=True,
)


class AppealStatus(enum.Enum):
    PENDING = 'PENDING'
    ACCEPTED = 'ACCEPTED'
    REJECTED = 'REJECTED'


AppealStatusEnum = ENUM(
    AppealStatus,
    name='AppealStatus',
    create_type=True,
)


class BlockWordAction(enum.Enum):
    BLOCK_MESSAGE = 'BLOCK_MESSAGE'
    SEND_ALERT = 'SEND_ALERT'
    WARN = 'WARN'
    MUTE = 'MUTE'  # Temporary mute (with expiry)
    BAN = 'BAN'  # Permanent ban (no expiry)
    BLACKLIST = 'BLACKLIST'  #: Deprecated: Use BAN instead


BlockWordActionEnum = ENUM(
    BlockWordAction,
    name='BlockWordAction',
    create_type=True,
    server_default=BlockWordAction.BLOCK_MESSAGE.value,
)


class PatternMatchType(enum.Enum):
    EXACT = 'EXACT'  # Exact word match with word boundaries
    PREFIX = 'PREFIX'  # Word starts with pattern (word*)
    SUFFIX = 'SUFFIX'  # Word ends with pattern (*word)
    WILDCARD = 'WILDCARD'  # Word contains pattern (*word*)


PatternMatchTypeEnum = ENUM(
    PatternMatchType,
    name='PatternMatchType',
    create_type=True,
)


class AlertSeverity(enum.Enum):
    LOW = 'LOW'
    MEDIUM = 'MEDIUM'
    HIGH = 'HIGH'
    CRITICAL = 'CRITICAL'


AlertSeverityEnum = ENUM(
    AlertSeverity,
    name='AlertSeverity',
    create_type=True,
)


class Badges(enum.Enum):
    VOTER = 'VOTER'  # For users who have voted on top.gg
    SUPPORTER = 'SUPPORTER'  # Premium supporters
    TRANSLATOR = 'TRANSLATOR'  # For users who help translate InterChat
    DEVELOPER = 'DEVELOPER'  # For users who contribute to the codebase
    STAFF = 'STAFF'  # For staff members who help manage the platform
    BETA_TESTER = 'BETA_TESTER'  # For users who participated in beta testing


BadgesEnum = ENUM(
    Badges,
    name='Badges',
    create_type=True,
)


class ReportStatus(enum.Enum):
    PENDING = 'PENDING'
    RESOLVED = 'RESOLVED'
    IGNORED = 'IGNORED'


ReportStatusEnum = ENUM(
    ReportStatus,
    name='ReportStatus',
    create_type=True,
)


class BlacklistType(enum.Enum):
    PERMANENT = 'PERMANENT'
    TEMPORARY = 'TEMPORARY'


BlacklistTypeEnum = ENUM(
    BlacklistType,
    name='BlacklistType',
    create_type=True,
)


class LeaderboardPeriod(enum.Enum):
    DAILY = 'DAILY'
    WEEKLY = 'WEEKLY'
    MONTHLY = 'MONTHLY'
    ALL_TIME = 'ALL_TIME'


LeaderboardPeriodEnum = ENUM(
    LeaderboardPeriod,
    name='LeaderboardPeriod',
    create_type=True,
)


class LeaderboardType(enum.Enum):
    USER = 'USER'
    SERVER = 'SERVER'
    HUB = 'HUB'


LeaderboardTypeEnum = ENUM(
    LeaderboardType,
    name='LeaderboardType',
    create_type=True,
)

# Association table for Hub-Tag many-to-many relationship
hub_tags = Table(
    '_HubToTag',
    Base.metadata,
    Column(
        'A', Text(), ForeignKey('Hub.id', ondelete='CASCADE', onupdate='CASCADE'), nullable=False
    ),
    Column(
        'B', Text(), ForeignKey('Tag.id', ondelete='CASCADE', onupdate='CASCADE'), nullable=False
    ),
)

# Prisma expects these indexes for implicit m-n:
Index('_HubToTag_AB_unique', hub_tags.c.A, hub_tags.c.B, unique=True)
Index('_HubToTag_B_index', hub_tags.c.B)


class Hub(Base):
    __tablename__ = 'Hub'

    # Primary fields
    id: Mapped[str] = mapped_column(
        Text(), primary_key=True, default=lambda: cuid.generate(), init=False
    )
    name: Mapped[str] = mapped_column(Text(), unique=True)
    description: Mapped[str] = mapped_column(Text)
    ownerId: Mapped[str] = mapped_column(Text(), ForeignKey('User.id'))
    iconUrl: Mapped[str] = mapped_column(Text())
    shortDescription: Mapped[Optional[str]] = mapped_column(String(100))

    # Relationships
    owner: Mapped['User'] = relationship(
        'User', back_populates='ownedHubs', lazy='noload', init=False
    )
    rulesAcceptances: Mapped[list['HubRulesAcceptance']] = relationship(
        'HubRulesAcceptance', back_populates='hub', lazy='noload', init=False
    )
    moderators: Mapped[list['HubModerator']] = relationship(
        'HubModerator', back_populates='hub', lazy='noload', init=False
    )
    connections: Mapped[list['Connection']] = relationship(
        'Connection', back_populates='hub', lazy='noload', init=False
    )
    tags: Mapped[list['Tag']] = relationship(
        'Tag', secondary=hub_tags, back_populates='hubs', lazy='noload', init=False
    )
    upvotes: Mapped[list['HubUpvote']] = relationship(
        'HubUpvote', back_populates='hub', lazy='noload', init=False
    )
    reviews: Mapped[list['HubReview']] = relationship(
        'HubReview', back_populates='hub', lazy='noload', init=False
    )
    logConfig: Mapped['HubLogConfig | None'] = relationship(
        'HubLogConfig', back_populates='hub', lazy='noload', init=False
    )
    blockWords: Mapped[list['BlockWord']] = relationship(
        'BlockWord', back_populates='hub', lazy='noload', init=False
    )
    antiSwearRules: Mapped[list['AntiSwearRule']] = relationship(
        'AntiSwearRule', back_populates='hub', lazy='noload', init=False
    )
    infractions: Mapped[list['Infraction']] = relationship(
        'Infraction', back_populates='hub', lazy='noload', init=False
    )
    invites: Mapped[list['HubInvite']] = relationship(
        'HubInvite', back_populates='hub', lazy='noload', init=False
    )
    messages: Mapped[list['Message']] = relationship(
        'Message', back_populates='hub', lazy='noload', init=False
    )
    reports: Mapped[list['HubReport']] = relationship(
        'HubReport', back_populates='hub', lazy='noload', init=False
    )
    activityMetrics: Mapped['HubActivityMetrics | None'] = relationship(
        'HubActivityMetrics', back_populates='hub', lazy='noload', init=False
    )
    leaderboardEntries: Mapped[list['LeaderboardEntry']] = relationship(
        'LeaderboardEntry', back_populates='hub', lazy='noload', init=False
    )
    announcements: Mapped[list['HubAnnouncement']] = relationship(
        'HubAnnouncement', back_populates='hub', lazy='noload', init=False
    )

    # Timestamps
    createdAt: Mapped[datetime] = mapped_column(
        DateTime(), server_default=func.now(), nullable=False
    )
    updatedAt: Mapped[datetime] = mapped_column(
        DateTime(), server_default=func.now(), onupdate=func.now(), nullable=False
    )
    lastActive: Mapped[datetime] = mapped_column(
        DateTime(), server_default=func.now(), nullable=False
    )

    # Optional fields
    lastNameChange: Mapped[Optional[datetime]] = mapped_column(
        DateTime(), default=None, server_default=func.now()
    )
    bannerUrl: Mapped[Optional[str]] = mapped_column(
        Text(), default=None, server_default=sql.null()
    )
    welcomeMessage: Mapped[Optional[str]] = mapped_column(
        Text(), default=None, server_default=sql.null()
    )
    language: Mapped[Optional[str]] = mapped_column(Text(), default=None, server_default=sql.null())
    region: Mapped[Optional[str]] = mapped_column(Text(), default=None, server_default=sql.null())

    # Numeric fields
    settings: Mapped[int] = mapped_column(
        Integer(), nullable=False, default=0, server_default=text('0')
    )
    appealCooldownHours: Mapped[int] = mapped_column(
        Integer(), default=168, server_default=text('168'), nullable=False
    )
    weeklyMessageCount: Mapped[int] = mapped_column(
        Integer(), default=0, server_default=text('0'), nullable=False
    )

    # Boolean flags
    private: Mapped[bool] = mapped_column(
        Boolean(), default=True, server_default=sql.true(), nullable=False
    )
    locked: Mapped[bool] = mapped_column(
        Boolean(), default=False, server_default=sql.false(), nullable=False
    )
    nsfw: Mapped[bool] = mapped_column(
        Boolean(), default=False, server_default=sql.false(), nullable=False
    )
    verified: Mapped[bool] = mapped_column(
        Boolean(), default=False, server_default=sql.false(), nullable=False
    )
    partnered: Mapped[bool] = mapped_column(
        Boolean(), default=False, server_default=sql.false(), nullable=False
    )
    featured: Mapped[bool] = mapped_column(
        Boolean(), default=False, server_default=sql.false(), nullable=False
    )

    # Array field (PostgreSQL specific)
    rules: Mapped[list[str]] = mapped_column(ARRAY(Text()), default=list, nullable=False)

    # Enum field
    activityLevel: Mapped[HubActivityLevel] = mapped_column(
        HubActivityLevelEnum,
        default=HubActivityLevel.LOW,
        server_default=HubActivityLevel.LOW.value,
        nullable=False,
    )

    # Indexes
    __table_args__ = (
        Index('Hub_ownerId_idx', 'ownerId'),
        Index('Hub_verified_featured_private_idx', 'verified', 'featured', 'private'),
        Index('Hub_activityLevel_idx', 'activityLevel'),
        Index('Hub_language_idx', 'language'),
        Index('Hub_nsfw_idx', 'nsfw'),
        Index('Hub_weeklyMessageCount_idx', 'weeklyMessageCount'),
    )


class Tag(Base):
    __tablename__ = 'Tag'

    id: Mapped[str] = mapped_column(
        Text(), primary_key=True, default=lambda: cuid.generate(), init=False
    )
    name: Mapped[str] = mapped_column(Text(), unique=True, nullable=False)
    category: Mapped[Optional[str]] = mapped_column(
        Text()
    )  # e.g., "Gaming", "Technology", "Art", "Music"
    description: Mapped[Optional[str]] = mapped_column(Text())
    color: Mapped[Optional[str]] = mapped_column(Text())  # Hex color for UI display

    # Many-to-many relationship with Hub
    hubs: Mapped[list['Hub']] = relationship(
        'Hub', secondary=hub_tags, back_populates='tags', lazy='noload'
    )

    createdAt: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now())
    isOfficial: Mapped[bool] = mapped_column(
        Boolean(), default=False, server_default=sql.false()
    )  # Official InterChat tags vs user-created
    usageCount: Mapped[int] = mapped_column(
        Integer(), default=0, server_default=text('0')
    )  # Track popularity for autocomplete

    __table_args__ = (
        Index('Tag_category_idx', 'category'),
        Index('Tag_usageCount_idx', 'usageCount'),
    )


class HubUpvote(Base):
    __tablename__ = 'HubUpvote'

    id: Mapped[str] = mapped_column(
        Text(), primary_key=True, default=lambda: cuid.generate(), init=False
    )
    userId: Mapped[str] = mapped_column(Text(), ForeignKey('User.id'), nullable=False)
    createdAt: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now())
    hubId: Mapped[str] = mapped_column(
        Text(),
        ForeignKey('Hub.id', ondelete='CASCADE'),
        nullable=False,
    )

    hub: Mapped['Hub'] = relationship('Hub', back_populates='upvotes', lazy='noload', init=False)
    user: Mapped['User'] = relationship(
        'User', back_populates='upvotedHubs', lazy='noload', init=False
    )

    __table_args__ = (
        UniqueConstraint('hubId', 'userId'),
        Index('HubUpvote_userId_idx', 'userId'),
    )


class HubReview(Base):
    __tablename__ = 'HubReview'

    id: Mapped[str] = mapped_column(
        Text(), primary_key=True, default=lambda: cuid.generate(), init=False
    )
    createdAt: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now())
    updatedAt: Mapped[datetime] = mapped_column(
        DateTime(), server_default=func.now(), onupdate=func.now()
    )
    rating: Mapped[int] = mapped_column(Integer())  # Rating from 1 to 5
    text: Mapped[str] = mapped_column(Text())  # Review text

    hubId: Mapped[str] = mapped_column(
        Text(),
        ForeignKey('Hub.id', ondelete='CASCADE'),
        nullable=False,
    )
    userId: Mapped[str] = mapped_column(
        Text(),
        ForeignKey('User.id'),
        nullable=False,
    )

    hub: Mapped['Hub'] = relationship('Hub', back_populates='reviews', lazy='noload', init=False)
    user: Mapped['User'] = relationship('User', back_populates='reviews', lazy='noload', init=False)

    __table_args__ = (
        UniqueConstraint('hubId', 'userId'),
        Index('HubReview_userId_idx', 'userId'),
    )


class HubModerator(Base):
    __tablename__ = 'HubModerator'

    id: Mapped[str] = mapped_column(
        Text(), primary_key=True, default=lambda: cuid.generate(), init=False
    )
    hubId: Mapped[str] = mapped_column(Text(), ForeignKey('Hub.id', ondelete='CASCADE'))
    userId: Mapped[str] = mapped_column(Text(), ForeignKey('User.id'))
    role: Mapped[HubModeratorRole] = mapped_column(
        HubModeratorRoleEnum,
        default=HubModeratorRole.MODERATOR,
        nullable=False,
    )

    hub: Mapped['Hub'] = relationship('Hub', back_populates='moderators', lazy='noload', init=False)
    user: Mapped['User'] = relationship(
        'User', back_populates='modPositions', lazy='noload', init=False
    )

    __table_args__ = (
        UniqueConstraint('hubId', 'userId'),
        Index('HubModerator_userId_idx', 'userId'),
    )


class Connection(Base):
    __tablename__ = 'Connection'

    id: Mapped[str] = mapped_column(
        Text(), primary_key=True, default=lambda: cuid.generate(), init=False
    )
    channelId: Mapped[str] = mapped_column(Text(), unique=True, nullable=False)
    invite: Mapped[Optional[str]] = mapped_column(Text())
    webhookURL: Mapped[str] = mapped_column(Text())
    serverId: Mapped[str] = mapped_column(Text(), ForeignKey('ServerData.id'))
    hubId: Mapped[str] = mapped_column(Text(), ForeignKey('Hub.id', ondelete='CASCADE'))
    createdAt: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now())
    lastActive: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now())
    parentId: Mapped[Optional[str]] = mapped_column(
        Text(), default=None, server_default=sql.null()
    )  # Parent channel ID for threads

    connected: Mapped[bool] = mapped_column(Boolean(), default=True, server_default=sql.true())
    server: Mapped['ServerData'] = relationship(
        'ServerData', back_populates='connections', lazy='noload', init=False
    )
    hub: Mapped['Hub'] = relationship(
        'Hub', back_populates='connections', lazy='noload', init=False
    )

    __table_args__ = (
        UniqueConstraint('channelId', 'serverId'),
        UniqueConstraint('hubId', 'serverId'),
        Index('Connection_serverId_idx', 'serverId'),
        Index('Connection_hubId_idx', 'hubId'),
        Index('Connection_hubId_channelId_idx', 'hubId', 'channelId'),
        Index('Connection_lastActive_idx', 'lastActive'),
        Index(
            'Connection_hub_lookup_partial_idx',
            'channelId',
            'hubId',
            'serverId',
            postgresql_where=text('connected = true'),
        ),
        Index(
            'Connection_channel_connected_partial_idx',
            'channelId',
            'connected',
            postgresql_where=text('connected = true'),
        ),
        Index(
            'Connection_hub_connected_partial_idx',
            'hubId',
            postgresql_where=text('connected = true'),
        ),
    )


class Infraction(Base):
    __tablename__ = 'Infraction'

    id: Mapped[str] = mapped_column(
        Text(), primary_key=True, default=lambda: cuid.generate(10), init=False
    )
    hubId: Mapped[str] = mapped_column(Text(), ForeignKey('Hub.id'))
    moderatorId: Mapped[str] = mapped_column(Text(), ForeignKey('User.id'))
    reason: Mapped[str] = mapped_column(Text())
    expiresAt: Mapped[Optional[datetime]] = mapped_column(DateTime())

    # For user infractions
    userId: Mapped[Optional[str]] = mapped_column(Text(), ForeignKey('User.id'))

    # For server infractions
    serverId: Mapped[Optional[str]] = mapped_column(Text(), ForeignKey('ServerData.id'))
    serverName: Mapped[Optional[str]] = mapped_column(Text())
    type: Mapped[InfractionType] = mapped_column(InfractionTypeEnum, nullable=False)
    hub: Mapped['Hub'] = relationship(
        'Hub', back_populates='infractions', lazy='noload', init=False
    )
    moderator: Mapped['User'] = relationship(
        'User',
        foreign_keys=[moderatorId],
        back_populates='issuedInfractions',
        lazy='noload',
        init=False,
    )
    user: Mapped['User'] = relationship(
        'User',
        foreign_keys=[userId],
        back_populates='infractions',
        lazy='noload',
        init=False,
    )
    server: Mapped['ServerData'] = relationship(
        'ServerData', back_populates='infractions', lazy='noload', init=False
    )
    appeals: Mapped[list['Appeal']] = relationship(
        'Appeal', back_populates='infraction', lazy='noload', init=False
    )
    createdAt: Mapped[datetime] = mapped_column(
        DateTime(),
        server_default=func.now(),
        default=None,
    )
    updatedAt: Mapped[datetime] = mapped_column(
        DateTime(),
        server_default=func.now(),
        onupdate=func.now(),
        default=None,
    )

    status: Mapped[InfractionStatus] = mapped_column(
        InfractionStatusEnum,
        default=InfractionStatus.ACTIVE,
        server_default=InfractionStatus.ACTIVE.value,
    )
    notified: Mapped[bool] = mapped_column(Boolean(), default=False, server_default=sql.false())

    __table_args__ = (
        Index('Infraction_status_hubId_idx', 'status', 'hubId'),
        Index('Infraction_userId_idx', 'userId'),
        Index('Infraction_serverId_idx', 'serverId'),
        Index('Infraction_type_idx', 'type'),
        Index('Infraction_expiresAt_idx', 'expiresAt'),
        # Composite index for user infractions lookup
        Index(
            'Infraction_userId_hubId_status_type_expiresAt_idx',
            'userId',
            'hubId',
            'status',
            'type',
            'expiresAt',
            postgresql_where=(userId.isnot(None)),  # Partial index for PostgreSQL
        ),
        # Composite index for server infractions lookup
        Index(
            'Infraction_serverId_hubId_status_type_expiresAt_idx',
            'serverId',
            'hubId',
            'status',
            'type',
            'expiresAt',
            postgresql_where=(serverId.isnot(None)),  # Partial index for PostgreSQL
        ),
    )


class Appeal(Base):
    __tablename__ = 'Appeal'

    infractionId: Mapped[str] = mapped_column(Text(), ForeignKey('Infraction.id'))
    userId: Mapped[str] = mapped_column(Text(), ForeignKey('User.id'))
    reason: Mapped[str] = mapped_column(Text())
    infraction: Mapped['Infraction'] = relationship(
        'Infraction', back_populates='appeals', lazy='noload'
    )
    createdAt: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now(), init=False)
    updatedAt: Mapped[datetime] = mapped_column(
        DateTime(), server_default=func.now(), onupdate=func.now(), init=False
    )
    id: Mapped[str] = mapped_column(
        Text(), primary_key=True, default=lambda: cuid.generate(), init=False
    )
    user: Mapped['User'] = relationship(
        'User', back_populates='appeals', lazy='noload', default=None
    )
    status: Mapped[AppealStatus] = mapped_column(
        AppealStatusEnum,
        default=AppealStatus.PENDING,
        server_default=AppealStatus.PENDING.value,
    )

    __table_args__ = (
        Index('Appeal_infractionId_idx', 'infractionId'),
        Index('Appeal_userId_idx', 'userId'),
        Index('Appeal_status_idx', 'status'),
        Index('Appeal_createdAt_idx', 'createdAt'),
    )


class BlockWord(Base):
    __tablename__ = 'BlockWord'

    id: Mapped[str] = mapped_column(
        Text(), primary_key=True, default=lambda: cuid.generate(), init=False
    )
    hubId: Mapped[str] = mapped_column(
        Text(), ForeignKey('Hub.id', ondelete='CASCADE'), nullable=False
    )
    name: Mapped[str] = mapped_column(Text(), nullable=False)
    createdBy: Mapped[str] = mapped_column(Text(), ForeignKey('User.id'), nullable=False)
    createdAt: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now(), init=False)
    updatedAt: Mapped[datetime] = mapped_column(
        DateTime(),
        server_default=func.now(),
        onupdate=func.now(),
        init=False,
    )

    # Relations
    words: Mapped[str] = mapped_column(Text(), nullable=False)  # separated by comma (,)
    actions: Mapped[list[BlockWordAction]] = mapped_column(
        ARRAY(BlockWordActionEnum),
    )

    hub: Mapped['Hub'] = relationship('Hub', back_populates='blockWords', lazy='noload')
    creator: Mapped['User'] = relationship(
        'User', back_populates='blockWordsCreated', lazy='noload'
    )

    __table_args__ = (
        UniqueConstraint('hubId', 'name'),
        Index('BlockWord_hubId_idx', 'hubId'),
    )


class AntiSwearRule(Base):
    __tablename__ = 'AntiSwearRule'

    id: Mapped[str] = mapped_column(
        Text(), primary_key=True, default=lambda: cuid.generate(), init=False
    )
    hubId: Mapped[str] = mapped_column(
        Text(), ForeignKey('Hub.id', ondelete='CASCADE'), nullable=False
    )
    name: Mapped[str] = mapped_column(Text(), nullable=False)
    createdBy: Mapped[str] = mapped_column(Text(), ForeignKey('User.id'), nullable=False)
    enabled: Mapped[bool] = mapped_column(
        Boolean(), default=True, server_default=sql.true(), nullable=False
    )  # Enable/disable the entire rule
    # Duration configuration for time-based actions (in minutes)
    muteDurationMinutes: Mapped[Optional[int]] = mapped_column(
        Integer(), default=None, server_default=sql.null()
    )
    createdAt: Mapped[datetime] = mapped_column(
        DateTime(), server_default=func.now(), default=None, init=False
    )
    updatedAt: Mapped[datetime] = mapped_column(
        DateTime(), server_default=func.now(), onupdate=func.now(), default=None, init=False
    )
    actions: Mapped[list[BlockWordAction]] = mapped_column(
        ARRAY(BlockWordActionEnum),
        default=list,
        server_default=text('\'{}\'::"BlockWordAction"[]'),
    )

    hub: Mapped['Hub'] = relationship(
        'Hub', back_populates='antiSwearRules', lazy='noload', init=False
    )
    creator: Mapped['User'] = relationship(
        'User', back_populates='antiSwearRulesCreated', lazy='noload', init=False
    )
    patterns: Mapped[list['AntiSwearPattern']] = relationship(
        'AntiSwearPattern',
        back_populates='rule',
        lazy='noload',
        cascade='all, delete-orphan',
        init=False,
    )
    whitelists: Mapped[list['AntiSwearWhitelist']] = relationship(
        'AntiSwearWhitelist',
        back_populates='rule',
        lazy='noload',
        cascade='all, delete-orphan',
        init=False,
    )

    __table_args__ = (
        UniqueConstraint('hubId', 'name'),
        Index('AntiSwearRule_hubId_idx', 'hubId'),
    )


class AntiSwearPattern(Base):
    __tablename__ = 'AntiSwearPattern'

    id: Mapped[str] = mapped_column(
        Text(), primary_key=True, default=lambda: cuid.generate(), init=False
    )
    ruleId: Mapped[str] = mapped_column(
        Text(), ForeignKey('AntiSwearRule.id', ondelete='CASCADE'), nullable=False
    )
    pattern: Mapped[str] = mapped_column(Text(), nullable=False)  # Individual word or pattern
    matchType: Mapped[PatternMatchType] = mapped_column(
        PatternMatchTypeEnum,
        default=PatternMatchType.EXACT,
        server_default=PatternMatchType.EXACT.value,
        nullable=False,
    )  # Type of pattern matching to use

    rule: Mapped['AntiSwearRule'] = relationship(
        'AntiSwearRule', back_populates='patterns', lazy='noload', init=False
    )

    __table_args__ = (
        Index('AntiSwearPattern_ruleId_idx', 'ruleId'),
        Index('AntiSwearPattern_matchType_idx', 'matchType'),
    )


class AntiSwearWhitelist(Base):
    __tablename__ = 'AntiSwearWhitelist'

    id: Mapped[str] = mapped_column(
        Text(), primary_key=True, default=lambda: cuid.generate(), init=False
    )
    ruleId: Mapped[str] = mapped_column(
        Text(), ForeignKey('AntiSwearRule.id', ondelete='CASCADE'), nullable=False
    )
    word: Mapped[str] = mapped_column(
        Text(), nullable=False
    )  # Safe word that should never be blocked
    createdBy: Mapped[str] = mapped_column(Text(), ForeignKey('User.id'), nullable=False)
    reason: Mapped[Optional[str]] = mapped_column(
        Text(), default=None, server_default=sql.null()
    )  # Optional reason for whitelisting
    createdAt: Mapped[datetime] = mapped_column(
        DateTime(), server_default=func.now(), default=None, init=False
    )

    rule: Mapped['AntiSwearRule'] = relationship(
        'AntiSwearRule', back_populates='whitelists', lazy='noload', init=False
    )
    creator: Mapped['User'] = relationship(
        'User', back_populates='antiSwearWhitelistsCreated', lazy='noload', init=False
    )

    __table_args__ = (
        UniqueConstraint('ruleId', 'word'),
        Index('AntiSwearWhitelist_ruleId_idx', 'ruleId'),
        Index('AntiSwearWhitelist_word_idx', 'word'),
    )


class HubLogConfig(Base):
    __tablename__ = 'HubLogConfig'

    id: Mapped[str] = mapped_column(
        Text(), primary_key=True, default=lambda: cuid.generate(10), init=False
    )
    hubId: Mapped[str] = mapped_column(
        Text(), ForeignKey('Hub.id', ondelete='CASCADE'), unique=True, nullable=False
    )
    modLogsChannelId: Mapped[str] = mapped_column(
        Text(), nullable=True, server_default=sql.null(), default=None
    )
    modLogsRoleId: Mapped[str] = mapped_column(
        Text(), nullable=True, server_default=sql.null(), default=None
    )
    joinLeavesChannelId: Mapped[str] = mapped_column(
        Text(), nullable=True, server_default=sql.null(), default=None
    )
    joinLeavesRoleId: Mapped[str] = mapped_column(
        Text(), nullable=True, server_default=sql.null(), default=None
    )
    appealsChannelId: Mapped[str] = mapped_column(
        Text(), nullable=True, server_default=sql.null(), default=None
    )
    appealsRoleId: Mapped[str] = mapped_column(
        Text(), nullable=True, server_default=sql.null(), default=None
    )
    reportsChannelId: Mapped[str] = mapped_column(
        Text(), nullable=True, server_default=sql.null(), default=None
    )
    reportsRoleId: Mapped[str] = mapped_column(
        Text(), nullable=True, server_default=sql.null(), default=None
    )
    networkAlertsChannelId: Mapped[str] = mapped_column(
        Text(), nullable=True, server_default=sql.null(), default=None
    )
    networkAlertsRoleId: Mapped[str] = mapped_column(
        Text(), nullable=True, server_default=sql.null(), default=None
    )
    messageModerationChannelId: Mapped[str] = mapped_column(
        Text(), nullable=True, server_default=sql.null(), default=None
    )
    messageModerationRoleId: Mapped[str] = mapped_column(
        Text(), nullable=True, server_default=sql.null(), default=None
    )

    hub: Mapped['Hub'] = relationship('Hub', back_populates='logConfig', lazy='noload', init=False)
    __table_args__ = (Index('HubLogConfig_hubId_idx', 'hubId'),)


class HubInvite(Base):
    __tablename__ = 'HubInvite'

    hubId: Mapped[str] = mapped_column(
        Text(), ForeignKey('Hub.id', ondelete='CASCADE'), nullable=False
    )
    expires: Mapped[Optional[datetime]] = mapped_column(
        DateTime(), default=None, server_default=sql.null()
    )
    code: Mapped[str] = mapped_column(
        Text(), primary_key=True, default=lambda: cuid.generate(7), unique=True
    )
    createdAt: Mapped[datetime] = mapped_column(DateTime(), default=None, server_default=func.now())

    maxUses: Mapped[int] = mapped_column(
        Integer(), default=0, server_default=text('0')
    )  # 0 = unlimited
    uses: Mapped[int] = mapped_column(Integer(), default=0, server_default=text('0'))

    hub: Mapped['Hub'] = relationship('Hub', back_populates='invites', lazy='noload', init=False)

    __table_args__ = (Index('HubInvite_hubId_idx', 'hubId'),)


class HubRulesAcceptance(Base):
    __tablename__ = 'HubRulesAcceptance'

    id: Mapped[str] = mapped_column(
        Text(), primary_key=True, default=lambda: cuid.generate(), init=False
    )
    userId: Mapped[str] = mapped_column(Text(), ForeignKey('User.id'), nullable=False)
    hubId: Mapped[str] = mapped_column(
        Text(), ForeignKey('Hub.id', ondelete='CASCADE'), nullable=False
    )
    acceptedAt: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now(), init=False)

    user: Mapped['User'] = relationship(
        'User', back_populates='rulesAcceptances', lazy='noload', init=False
    )
    hub: Mapped['Hub'] = relationship(
        'Hub', back_populates='rulesAcceptances', lazy='noload', init=False
    )

    __table_args__ = (
        UniqueConstraint('userId', 'hubId'),
        Index('HubRulesAcceptance_hubId_userId_idx', 'hubId', 'userId'),
    )


class HubActivityMetrics(Base):
    __tablename__ = 'HubActivityMetrics'

    id: Mapped[str] = mapped_column(
        Text(), primary_key=True, default=lambda: cuid.generate(), init=False
    )
    hubId: Mapped[str] = mapped_column(
        Text(), ForeignKey('Hub.id', ondelete='CASCADE'), unique=True, nullable=False
    )
    hub: Mapped['Hub'] = relationship('Hub', back_populates='activityMetrics', lazy='noload')
    lastUpdated: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now())
    createdAt: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now())

    # Daily metrics
    messagesLast24h: Mapped[int] = mapped_column(Integer(), default=0, server_default=text('0'))
    activeUsersLast24h: Mapped[int] = mapped_column(Integer(), default=0, server_default=text('0'))
    newConnectionsLast24h: Mapped[int] = mapped_column(
        Integer(), default=0, server_default=text('0')
    )

    # Weekly metrics
    messagesLast7d: Mapped[int] = mapped_column(Integer(), default=0, server_default=text('0'))
    activeUsersLast7d: Mapped[int] = mapped_column(Integer(), default=0, server_default=text('0'))
    newConnectionsLast7d: Mapped[int] = mapped_column(
        Integer(), default=0, server_default=text('0')
    )

    # Growth metrics
    memberGrowthRate: Mapped[float] = mapped_column(
        Float, default=0.0, server_default=text('0.0')
    )  # Percentage growth over 7 days
    engagementRate: Mapped[float] = mapped_column(
        Float, default=0.0, server_default=text('0.0')
    )  # Messages per active user

    # Timestamps

    __table_args__ = (
        Index('HubActivityMetrics_hubId_idx', 'hubId'),
        Index('HubActivityMetrics_lastUpdated_idx', 'lastUpdated'),
    )


class DevAlerts(Base):
    __tablename__ = 'DevAlerts'

    id: Mapped[str] = mapped_column(
        Text(), primary_key=True, default=lambda: cuid.generate(), init=False
    )
    title: Mapped[str] = mapped_column(Text(), nullable=False)
    content: Mapped[str] = mapped_column(Text(), nullable=False)
    imageUrl: Mapped[Optional[str]] = mapped_column(Text(), default=None, server_default=sql.null())
    createdAt: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now(), init=False)
    thumbnailUrl: Mapped[Optional[str]] = mapped_column(
        Text(), default=None, server_default=sql.null()
    )


class HubAnnouncement(Base):
    __tablename__ = 'HubAnnouncement'

    id: Mapped[str] = mapped_column(
        Text(), primary_key=True, default=lambda: cuid.generate(), init=False
    )
    hubId: Mapped[str] = mapped_column(Text(), ForeignKey('Hub.id', ondelete='CASCADE'))
    title: Mapped[str] = mapped_column(Text())
    content: Mapped[str] = mapped_column(Text())
    createdAt: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now(), init=False)
    frequencyMs: Mapped[int] = mapped_column(Integer())
    previousAnnouncement: Mapped[datetime] = mapped_column(
        DateTime(), nullable=True, default=None, server_default=sql.null()
    )
    nextAnnouncement: Mapped[datetime] = mapped_column(
        DateTime(), nullable=True, default=None, server_default=sql.null()
    )
    imageUrl: Mapped[Optional[str]] = mapped_column(Text(), default=None, server_default=sql.null())
    thumbnailUrl: Mapped[Optional[str]] = mapped_column(
        Text(), default=None, server_default=sql.null()
    )
    hub: Mapped['Hub'] = relationship(
        'Hub', back_populates='announcements', lazy='noload', init=False
    )


class ServerData(Base):
    __tablename__ = 'ServerData'

    id: Mapped[str] = mapped_column(Text(), primary_key=True)
    name: Mapped[str] = mapped_column(Text())
    lastMessageAt: Mapped[datetime] = mapped_column(
        DateTime(), server_default=func.now(), default=None
    )
    # When the server was added to Db
    createdAt: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now(), default=None)
    updatedAt: Mapped[datetime] = mapped_column(
        DateTime(), server_default=func.now(), onupdate=func.now(), default=None
    )

    # Relations
    connections: Mapped[List['Connection']] = relationship(
        'Connection', back_populates='server', lazy='noload', init=False
    )
    infractions: Mapped[List['Infraction']] = relationship(
        'Infraction', back_populates='server', lazy='noload', init=False
    )
    serverBlacklists: Mapped[List['ServerBlacklist']] = relationship(
        'ServerBlacklist', back_populates='server', lazy='noload', init=False
    )
    leaderboardEntries: Mapped[List['LeaderboardEntry']] = relationship(
        'LeaderboardEntry',
        back_populates='server',
        lazy='noload',
        cascade='all, delete-orphan',
        init=False,
    )

    inviteCode: Mapped[Optional[str]] = mapped_column(
        Text(), default=None, server_default=sql.null(), nullable=True
    )
    messageCount: Mapped[int] = mapped_column(Integer(), default=0, server_default=text('0'))
    iconUrl: Mapped[Optional[str]] = mapped_column(Text(), default=None, server_default=sql.null())

    __table_args__ = (
        Index('ServerData_lastMessageAt_idx', text('"lastMessageAt" DESC')),
        Index('ServerData_messageCount_idx', 'messageCount'),
        Index('ServerData_createdAt_idx', 'createdAt'),
    )


class ReputationLog(Base):
    __tablename__ = 'ReputationLog'

    id: Mapped[str] = mapped_column(
        Text(), primary_key=True, default=lambda: cuid.generate(), init=False
    )
    giverId: Mapped[str] = mapped_column(Text(), nullable=False)
    receiverId: Mapped[str] = mapped_column(Text(), ForeignKey('User.id'), nullable=False)
    reason: Mapped[str] = mapped_column(Text(), nullable=False)
    timestamp: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now())
    receiver: Mapped['User'] = relationship('User', back_populates='reputationLog', lazy='noload')
    automatic: Mapped[bool] = mapped_column(Boolean(), default=False, server_default=sql.false())

    __table_args__ = (
        Index('ReputationLog_receiverId_idx', 'receiverId'),
        Index('ReputationLog_giverId_idx', 'giverId'),
    )


class Account(Base):
    __tablename__ = 'Account'

    # Primary key
    id = Column(String, primary_key=True, nullable=False)  # cuid or uuid

    # Foreign key to user
    userId = Column(String, ForeignKey('User.id', ondelete='CASCADE'), nullable=False)

    # NextAuth fields
    type = Column(String, nullable=False)
    provider = Column(String, nullable=False)
    providerAccountId = Column(String, nullable=False)
    refresh_token = Column(Text)
    access_token = Column(Text)
    expires_at = Column(Integer)
    token_type = Column(String)
    scope = Column(String)
    id_token = Column(Text)
    session_state = Column(String)

    # Relationships (optional)
    user: Mapped['User'] = relationship('User', back_populates='accounts')

    # Table-level constraints
    __table_args__ = (
        UniqueConstraint('provider', 'providerAccountId', name='account_provider_account_unique'),
        Index('account_userId_idx', 'userId'),
    )


class Session(Base):
    __tablename__ = 'Session'

    sessionToken = Column(Text, primary_key=True, nullable=False)
    userId = Column(Text, ForeignKey('User.id', ondelete='CASCADE'), nullable=False)
    expires = Column(DateTime, nullable=False)

    # relationships (optional)
    user: Mapped['User'] = relationship('User', back_populates='sessions')

    __table_args__ = (Index('session_userId_idx', 'userId'),)


class Achievement(Base):
    __tablename__ = 'Achievement'

    id: Mapped[str] = mapped_column(Text(), primary_key=True)
    name: Mapped[str] = mapped_column(Text(), nullable=False)
    description: Mapped[str] = mapped_column(Text(), nullable=False)
    badgeEmoji: Mapped[str] = mapped_column(Text(), nullable=False)
    badgeUrl: Mapped[str] = mapped_column(Text())

    # Relations
    userAchievements: Mapped[List['UserAchievement']] = relationship(
        'UserAchievement',
        back_populates='achievement',
        lazy='noload',
        cascade='all, delete-orphan',
    )
    userProgress: Mapped[List['UserAchievementProgress']] = relationship(
        'UserAchievementProgress',
        back_populates='achievement',
        lazy='noload',
        cascade='all, delete-orphan',
    )

    createdAt: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now())
    updatedAt: Mapped[datetime] = mapped_column(
        DateTime(), server_default=func.now(), onupdate=func.now()
    )
    threshold: Mapped[int] = mapped_column(Integer(), default=1)
    secret: Mapped[bool] = mapped_column(Boolean(), default=False, server_default=sql.false())


class UserAchievement(Base):
    __tablename__ = 'UserAchievement'

    id: Mapped[str] = mapped_column(
        Text(), primary_key=True, default=lambda: cuid.generate(), init=False
    )
    userId: Mapped[str] = mapped_column(
        Text(), ForeignKey('User.id', ondelete='CASCADE'), nullable=False
    )
    achievementId: Mapped[str] = mapped_column(
        Text(), ForeignKey('Achievement.id', ondelete='CASCADE'), nullable=False
    )
    user: Mapped['User'] = relationship('User', back_populates='achievements', lazy='noload')
    achievement: Mapped['Achievement'] = relationship(
        'Achievement', back_populates='userAchievements', lazy='noload'
    )
    unlockedAt: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now())

    __table_args__ = (
        UniqueConstraint('userId', 'achievementId'),
        Index('UserAchievement_userId_idx', 'userId'),
        Index('UserAchievement_achievementId_idx', 'achievementId'),
    )


class UserAchievementProgress(Base):
    __tablename__ = 'UserAchievementProgress'

    userId: Mapped[str] = mapped_column(
        Text(), ForeignKey('User.id', ondelete='CASCADE'), primary_key=True
    )
    achievementId: Mapped[str] = mapped_column(
        Text(), ForeignKey('Achievement.id', ondelete='CASCADE'), primary_key=True
    )

    user: Mapped['User'] = relationship('User', back_populates='achievementProgress', lazy='noload')
    achievement: Mapped['Achievement'] = relationship(
        'Achievement', back_populates='userProgress', lazy='noload'
    )

    createdAt: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now())
    updatedAt: Mapped[datetime] = mapped_column(
        DateTime(), server_default=func.now(), onupdate=func.now()
    )
    currentValue: Mapped[int] = mapped_column(Integer(), default=0, server_default=text('0'))


class Message(Base):
    __tablename__ = 'Message'

    id: Mapped[str] = mapped_column(Text(), primary_key=True)  # Discord Snowflake
    hubId: Mapped[str] = mapped_column(ForeignKey('Hub.id', ondelete='CASCADE'))
    content: Mapped[str]
    imageUrl: Mapped[Optional[str]]
    channelId: Mapped[str]
    guildId: Mapped[str]
    authorId: Mapped[str]
    referredMessageId: Mapped[Optional[str]] = mapped_column(ForeignKey('Message.id'))
    createdAt: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now(), default=None)

    # Relationships
    hub: Mapped['Hub'] = relationship('Hub', back_populates='messages', lazy='noload', init=False)

    hubReports: Mapped[list['HubReport']] = relationship(
        'HubReport',
        back_populates='message',
        lazy='noload',
        cascade='all, delete-orphan',
        init=False,
    )
    globalReports: Mapped[list['GlobalReport']] = relationship(
        'GlobalReport',
        back_populates='message',
        lazy='noload',
        cascade='all, delete-orphan',
        init=False,
    )

    broadcasts: Mapped[list['Broadcast']] = relationship(
        'Broadcast',
        back_populates='message',
        lazy='noload',
        cascade='all, delete-orphan',
        init=False,
    )

    referredTo: Mapped[Optional['Message']] = relationship(
        'Message',
        remote_side=[id],
        back_populates='referredBy',
        lazy='noload',
        foreign_keys=[referredMessageId],
        init=False,
    )
    referredBy: Mapped[list['Message']] = relationship(
        'Message',
        back_populates='referredTo',
        lazy='noload',
        foreign_keys=[referredMessageId],
        init=False,
    )
    reactions: Mapped[list['HubMessageReaction']] = relationship(
        'HubMessageReaction',
        back_populates='message',
        lazy='noload',
        cascade='all, delete-orphan',
        init=False,
    )

    __table_args__ = (
        Index('Message_hubId_idx', 'hubId'),
        Index('Message_authorId_idx', 'authorId'),
        Index('Message_guildId_idx', 'guildId'),
        Index('Message_referredMessageId_idx', 'referredMessageId'),
        Index('Message_createdAt_idx', text('"createdAt" DESC')),
        # Composite indexes
        Index('Message_guildId_authorId_idx', 'guildId', 'authorId'),
        Index('Message_hubId_createdAt_idx', 'hubId', text('"createdAt" DESC')),
        Index('Message_guildId_hubId_idx', 'guildId', 'hubId'),
        Index('Message_channel_timestamp_idx', 'channelId', text('"createdAt" DESC')),
        Index('Message_author_timestamp_idx', 'authorId', text('"createdAt" DESC')),
    )


class HubMessageReaction(Base):
    __tablename__ = 'HubMessageReaction'

    # Primary key
    id: Mapped[str] = mapped_column(primary_key=True, default=lambda: cuid.generate(), init=False)

    # Foreign keys
    messageId: Mapped[str] = mapped_column(
        Text(), ForeignKey('Message.id', ondelete='CASCADE'), nullable=False
    )

    # Reaction data
    emoji: Mapped[str] = mapped_column(String(64), nullable=False)
    users: Mapped[list[str]] = mapped_column(ARRAY(Text()), nullable=False, default=list)

    # Relationships
    message: Mapped['Message'] = relationship(back_populates='reactions', init=False)
    __table_args__ = (UniqueConstraint('messageId', 'emoji'),)


class Broadcast(Base):
    __tablename__ = 'Broadcast'

    id: Mapped[str] = mapped_column(
        Text(), primary_key=True
    )  # Discord message ID of the broadcast message
    messageId: Mapped[str] = mapped_column(
        Text(), ForeignKey('Message.id', ondelete='CASCADE'), nullable=False
    )
    guildId: Mapped[str] = mapped_column(Text(), nullable=False)
    channelId: Mapped[str] = mapped_column(Text(), nullable=False)
    message: Mapped['Message'] = relationship(
        'Message', back_populates='broadcasts', lazy='noload', init=False
    )

    createdAt: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now(), init=False)

    __table_args__ = (
        Index('Broadcast_messageId_idx', 'messageId'),
        Index('Broadcast_channelId_idx', 'channelId'),
        Index('Broadcast_createdAt_idx', text('"createdAt" DESC')),
        Index('Broadcast_messageId_channelId_idx', 'messageId', 'channelId'),
        Index(
            'Broadcast_id_messageId_channelId_createdAt_idx',
            'id',
            'messageId',
            'channelId',
            'createdAt',
        ),
    )


class GlobalReport(Base):
    __tablename__ = 'GlobalReport'

    id: Mapped[str] = mapped_column(
        Text(), primary_key=True, default=lambda: cuid.generate(10), init=False
    )  # Short, unique report ID
    reporterId: Mapped[str] = mapped_column(
        Text(), ForeignKey('User.id'), nullable=False
    )  # User who submitted the report

    reportedUserId: Mapped[str] = mapped_column(
        Text(), ForeignKey('User.id'), nullable=False
    )  # User who was reported (if applicable)
    reportedServerId: Mapped[str] = mapped_column(
        Text(), nullable=False
    )  # Server where the reported content originated
    messageId: Mapped[str] = mapped_column(
        Text(), ForeignKey('Message.id'), nullable=True
    )  # ID of the reported message (if applicable)
    reason: Mapped[str] = mapped_column(Text(), nullable=False)  # Reason for the report
    reporter: Mapped['User'] = relationship(
        'User',
        foreign_keys=[reporterId],
        back_populates='globalReportsSubmitted',
        lazy='noload',
        init=False,
    )
    reportedUser: Mapped['User'] = relationship(
        'User',
        foreign_keys=[reportedUserId],
        back_populates='globalReportsReceived',
        lazy='noload',
        init=False,
    )

    message: Mapped['Message'] = relationship(
        'Message', back_populates='globalReports', lazy='noload', init=False
    )

    createdAt: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now(), init=False)
    updatedAt: Mapped[datetime] = mapped_column(
        DateTime(), server_default=func.now(), onupdate=func.now(), init=False
    )

    handledBy: Mapped[str] = mapped_column(
        Text(), ForeignKey('User.id'), nullable=True, default=None, server_default=sql.null()
    )  # Moderator who handled the report
    handler: Mapped['User'] = relationship(
        'User',
        foreign_keys=[handledBy],
        back_populates='globalReportsHandled',
        lazy='noload',
        init=False,
    )
    status: Mapped[ReportStatus] = mapped_column(
        ReportStatusEnum,
        default=ReportStatus.PENDING,
        server_default=ReportStatus.PENDING.value,
    )
    handledAt: Mapped[datetime] = mapped_column(
        DateTime(), nullable=True, default=None, server_default=sql.null()
    )  # When the report was handled

    __table_args__ = (
        Index('GlobalReport_status_idx', 'status'),
        Index('GlobalReport_createdAt_idx', 'createdAt'),
        Index('GlobalReport_reporterId_idx', 'reporterId'),
        Index('GlobalReport_messageId_idx', 'messageId'),
        Index('GlobalReport_handledBy_idx', 'handledBy'),
        Index('GlobalReport_reportedUserId_idx', 'reportedUserId'),
    )


class HubReport(Base):
    __tablename__ = 'HubReport'

    id: Mapped[str] = mapped_column(
        Text(), primary_key=True, default=lambda: cuid.generate(10), init=False
    )  # Short, unique report ID
    hubId: Mapped[str] = mapped_column(
        Text(), ForeignKey('Hub.id', ondelete='CASCADE'), nullable=False
    )
    reporterId: Mapped[str] = mapped_column(
        Text(), ForeignKey('User.id'), nullable=False
    )  # User who submitted the report

    reportedUserId: Mapped[str] = mapped_column(
        Text(), ForeignKey('User.id'), nullable=False
    )  # User who was reported (if applicable)
    reportedServerId: Mapped[str] = mapped_column(
        Text(), nullable=False
    )  # Server where the reported content originated
    messageId: Mapped[str] = mapped_column(
        Text(), ForeignKey('Message.id'), nullable=True
    )  # ID of the reported message (if applicable)
    reason: Mapped[str] = mapped_column(Text(), nullable=False)  # Reason for the report

    hub: Mapped['Hub'] = relationship('Hub', back_populates='reports', lazy='noload', init=False)
    reporter: Mapped['User'] = relationship(
        'User',
        foreign_keys=[reporterId],
        back_populates='hubReportsSubmitted',
        lazy='noload',
        init=False,
    )

    reportedUser: Mapped['User'] = relationship(
        'User',
        foreign_keys=[reportedUserId],
        back_populates='hubReportsReceived',
        lazy='noload',
        init=False,
    )

    message: Mapped['Message'] = relationship(
        'Message', back_populates='hubReports', lazy='noload', init=False
    )

    createdAt: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now(), init=False)
    updatedAt: Mapped[datetime] = mapped_column(
        DateTime(), server_default=func.now(), onupdate=func.now(), init=False
    )

    handledBy: Mapped[str] = mapped_column(
        Text(), ForeignKey('User.id'), nullable=True, default=None, server_default=sql.null()
    )  # Moderator who handled the report
    handler: Mapped['User'] = relationship(
        'User',
        foreign_keys=[handledBy],
        back_populates='hubReportsHandled',
        lazy='noload',
        init=False,
    )
    status: Mapped[ReportStatus] = mapped_column(
        ReportStatusEnum,
        default=ReportStatus.PENDING,
        server_default=ReportStatus.PENDING.value,
    )
    handledAt: Mapped[datetime] = mapped_column(
        DateTime(), nullable=True, default=None, server_default=sql.null()
    )  # When the report was handled

    __table_args__ = (
        Index('HubReport_hubId_idx', 'hubId'),
        Index('HubReport_status_idx', 'status'),
        Index('HubReport_createdAt_idx', 'createdAt'),
        Index('HubReport_reporterId_idx', 'reporterId'),
        Index('HubReport_messageId_idx', 'messageId'),
        Index('HubReport_handledBy_idx', 'handledBy'),
        Index('HubReport_reportedUserId_idx', 'reportedUserId'),
    )


class Blacklist(Base):
    __tablename__ = 'Blacklist'

    id: Mapped[str] = mapped_column(
        Text(), primary_key=True, default=lambda: cuid.generate(10), init=False
    )
    userId: Mapped[str] = mapped_column(Text(), ForeignKey('User.id'), nullable=False)
    moderatorId: Mapped[str] = mapped_column(
        Text(), ForeignKey('User.id'), nullable=False
    )  # Staff member who issued the blacklist
    reason: Mapped[str] = mapped_column(Text(), nullable=False)  # Reason for the blacklist
    expiresAt: Mapped[datetime | None] = mapped_column(
        DateTime(),
        default=None,
        server_default=sql.null(),
    )  # When the blacklist expires (null for permanent blacklists)

    user: Mapped['User'] = relationship(
        'User',
        foreign_keys=[userId],
        back_populates='blacklists',
        lazy='noload',
        init=False,
    )
    moderator: Mapped['User'] = relationship(
        'User',
        foreign_keys=[moderatorId],
        back_populates='issuedBlacklists',
        lazy='noload',
        init=False,
    )

    createdAt: Mapped[datetime] = mapped_column(
        DateTime(),
        server_default=func.now(),
        default=None,
    )
    updatedAt: Mapped[Optional[datetime]] = mapped_column(
        DateTime(),
        server_default=func.now(),
        onupdate=func.now(),
        default=None,
    )
    type: Mapped[BlacklistType] = mapped_column(
        BlacklistTypeEnum,
        default=BlacklistType.PERMANENT,
        server_default=BlacklistType.PERMANENT.value,
    )

    __table_args__ = (
        Index('Blacklist_userId_idx', 'userId'),
        Index('Blacklist_expiresAt_idx', 'expiresAt'),
        Index('Blacklist_createdAt_idx', 'createdAt'),
        # Index for user blacklist validation during broadcasting
        Index('Blacklist_userId_expiresAt_idx', 'userId', 'expiresAt'),
        Index('Blacklist_userId_type_idx', 'userId', 'type'),
    )


class ServerBlacklist(Base):
    __tablename__ = 'ServerBlacklist'

    id: Mapped[str] = mapped_column(
        Text(), primary_key=True, default=lambda: cuid.generate(10), init=False
    )
    serverId: Mapped[str] = mapped_column(Text(), ForeignKey('ServerData.id'), nullable=False)
    moderatorId: Mapped[str] = mapped_column(Text(), ForeignKey('User.id'), nullable=False)
    reason: Mapped[str] = mapped_column(Text(), nullable=False)
    duration: Mapped[int | None] = mapped_column(
        Integer(), default=None, server_default=sql.null()
    )  # Duration in milliseconds for temporary blacklists (null for permanent)
    expiresAt: Mapped[datetime | None] = mapped_column(
        DateTime(), default=None, server_default=sql.null()
    )  # When the blacklist expires (null for permanent blacklists)

    server: Mapped['ServerData'] = relationship(
        'ServerData', back_populates='serverBlacklists', lazy='noload', init=False
    )
    moderator: Mapped['User'] = relationship(
        'User', back_populates='issuedServerBlacklists', lazy='noload', init=False
    )

    createdAt: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now(), default=None)
    updatedAt: Mapped[datetime] = mapped_column(
        DateTime(),
        server_default=func.now(),
        onupdate=func.now(),
        default=None,
    )
    type: Mapped[BlacklistType] = mapped_column(
        BlacklistTypeEnum,
        default=BlacklistType.PERMANENT,
        server_default=BlacklistType.PERMANENT.value,
    )

    __table_args__ = (
        Index('ServerBlacklist_serverId_idx', 'serverId'),
        Index('ServerBlacklist_expiresAt_idx', 'expiresAt'),
        Index('ServerBlacklist_createdAt_idx', 'createdAt'),
        # Index for server blacklist validation during broadcasting
        Index(
            'ServerBlacklist_serverId_expiresAt_idx',
            'serverId',
            'expiresAt',
        ),
        Index('ServerBlacklist_serverId_type_idx', 'serverId', 'type'),
    )


class LeaderboardEntry(Base):
    __tablename__ = 'LeaderboardEntry'

    id: Mapped[str] = mapped_column(
        Text(), primary_key=True, default=lambda: cuid.generate(), init=False
    )
    userId: Mapped[str] = mapped_column(
        Text(), ForeignKey('User.id', ondelete='CASCADE'), nullable=False
    )
    hubId: Mapped[str] = mapped_column(Text(), ForeignKey('Hub.id', ondelete='CASCADE'))
    serverId: Mapped[str] = mapped_column(Text(), ForeignKey('ServerData.id', ondelete='CASCADE'))
    period: Mapped[LeaderboardPeriod] = mapped_column(LeaderboardPeriodEnum, nullable=False)
    type: Mapped[LeaderboardType] = mapped_column(LeaderboardTypeEnum, nullable=False)

    user: Mapped['User'] = relationship('User', back_populates='leaderboardEntries', lazy='noload')
    hub: Mapped['Hub'] = relationship('Hub', back_populates='leaderboardEntries', lazy='noload')
    server: Mapped['ServerData'] = relationship(
        'ServerData', back_populates='leaderboardEntries', lazy='noload'
    )

    lastActivityAt: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now())
    createdAt: Mapped[datetime] = mapped_column(DateTime(), server_default=func.now())
    updatedAt: Mapped[datetime] = mapped_column(
        DateTime(), server_default=func.now(), onupdate=func.now()
    )
    messageCount: Mapped[int] = mapped_column(Integer(), default=0, server_default=text('0'))
    score: Mapped[int] = mapped_column(Integer(), default=0, server_default=text('0'))
    rank: Mapped[int] = mapped_column(Integer(), default=0, server_default=text('0'))

    __table_args__ = (
        Index('LeaderboardEntry_type_period_score_idx', 'type', 'period', 'score'),
        Index('LeaderboardEntry_userId_type_period_idx', 'userId', 'type', 'period'),
        Index('LeaderboardEntry_hubId_type_period_idx', 'hubId', 'type', 'period'),
        Index('LeaderboardEntry_serverId_type_period_idx', 'serverId', 'type', 'period'),
        Index('LeaderboardEntry_lastActivityAt_idx', 'lastActivityAt'),
    )


class User(Base):
    __tablename__ = 'User'

    # Primary fields
    id: Mapped[str] = mapped_column(Text(), primary_key=True)
    name: Mapped[Optional[str]] = mapped_column(Text())
    image: Mapped[Optional[str]] = mapped_column(Text())

    # Relations - ALL with init=False to exclude from constructor
    ownedHubs: Mapped[list['Hub']] = relationship(
        'Hub', back_populates='owner', lazy='noload', init=False
    )
    appeals: Mapped[list['Appeal']] = relationship(
        'Appeal', back_populates='user', lazy='noload', init=False
    )
    infractions: Mapped[list['Infraction']] = relationship(
        'Infraction',
        foreign_keys='Infraction.userId',
        back_populates='user',
        lazy='noload',
        init=False,
    )
    issuedInfractions: Mapped[list['Infraction']] = relationship(
        'Infraction',
        foreign_keys='Infraction.moderatorId',
        back_populates='moderator',
        lazy='noload',
        init=False,
    )
    upvotedHubs: Mapped[list['HubUpvote']] = relationship(
        'HubUpvote', back_populates='user', lazy='noload', init=False
    )
    reputationLog: Mapped[list['ReputationLog']] = relationship(
        'ReputationLog', back_populates='receiver', lazy='noload', init=False
    )
    modPositions: Mapped[list['HubModerator']] = relationship(
        'HubModerator', back_populates='user', lazy='noload', init=False
    )
    reviews: Mapped[list['HubReview']] = relationship(
        'HubReview',
        back_populates='user',
        lazy='noload',
        cascade='all, delete-orphan',
        init=False,
    )
    blockWordsCreated: Mapped[list['BlockWord']] = relationship(
        'BlockWord', back_populates='creator', lazy='noload', init=False
    )
    antiSwearRulesCreated: Mapped[list['AntiSwearRule']] = relationship(
        'AntiSwearRule', back_populates='creator', lazy='noload', init=False
    )
    antiSwearWhitelistsCreated: Mapped[list['AntiSwearWhitelist']] = relationship(
        'AntiSwearWhitelist', back_populates='creator', lazy='noload', init=False
    )
    rulesAcceptances: Mapped[list['HubRulesAcceptance']] = relationship(
        'HubRulesAcceptance', back_populates='user', lazy='noload', init=False
    )
    accounts: Mapped[list['Account']] = relationship(
        'Account',
        back_populates='user',
        lazy='noload',
        cascade='all, delete-orphan',
        init=False,
    )
    sessions: Mapped[list['Session']] = relationship(
        'Session',
        back_populates='user',
        lazy='noload',
        cascade='all, delete-orphan',
        init=False,
    )
    hubReportsSubmitted: Mapped[list['HubReport']] = relationship(
        'HubReport',
        foreign_keys='HubReport.reporterId',
        back_populates='reporter',
        lazy='noload',
        init=False,
    )
    hubReportsReceived: Mapped[list['HubReport']] = relationship(
        'HubReport',
        foreign_keys='HubReport.reportedUserId',
        back_populates='reportedUser',
        lazy='noload',
        init=False,
    )
    hubReportsHandled: Mapped[list['HubReport']] = relationship(
        'HubReport',
        foreign_keys='HubReport.handledBy',
        back_populates='handler',
        lazy='noload',
        init=False,
    )
    globalReportsSubmitted: Mapped[list['GlobalReport']] = relationship(
        'GlobalReport',
        foreign_keys='GlobalReport.reporterId',
        back_populates='reporter',
        lazy='noload',
        init=False,
    )
    globalReportsReceived: Mapped[list['GlobalReport']] = relationship(
        'GlobalReport',
        foreign_keys='GlobalReport.reportedUserId',
        back_populates='reportedUser',
        lazy='noload',
        init=False,
    )
    globalReportsHandled: Mapped[list['GlobalReport']] = relationship(
        'GlobalReport',
        foreign_keys='GlobalReport.handledBy',
        back_populates='handler',
        lazy='noload',
        init=False,
    )
    achievements: Mapped[list['UserAchievement']] = relationship(
        'UserAchievement',
        back_populates='user',
        lazy='noload',
        cascade='all, delete-orphan',
        init=False,
    )
    achievementProgress: Mapped[list['UserAchievementProgress']] = relationship(
        'UserAchievementProgress',
        back_populates='user',
        lazy='noload',
        cascade='all, delete-orphan',
        init=False,
    )
    blacklists: Mapped[list['Blacklist']] = relationship(
        'Blacklist',
        foreign_keys='Blacklist.userId',
        back_populates='user',
        lazy='noload',
        init=False,
    )
    issuedBlacklists: Mapped[list['Blacklist']] = relationship(
        'Blacklist',
        foreign_keys='Blacklist.moderatorId',
        back_populates='moderator',
        lazy='noload',
        init=False,
    )
    issuedServerBlacklists: Mapped[list['ServerBlacklist']] = relationship(
        'ServerBlacklist', back_populates='moderator', lazy='noload', init=False
    )
    leaderboardEntries: Mapped[list['LeaderboardEntry']] = relationship(
        'LeaderboardEntry',
        back_populates='user',
        lazy='noload',
        cascade='all, delete-orphan',
        init=False,
    )
    # Timestamps
    lastMessageAt: Mapped[Optional[datetime]] = mapped_column(
        DateTime(),
        server_default=func.now(),
        default=None,
    )
    inboxLastReadDate: Mapped[Optional[datetime]] = mapped_column(
        DateTime(),
        server_default=func.now(),
        default=None,
    )
    createdAt: Mapped[Optional[datetime]] = mapped_column(
        DateTime(),
        server_default=func.now(),
        default=None,
    )
    updatedAt: Mapped[Optional[datetime]] = mapped_column(
        DateTime(),
        server_default=func.now(),
        onupdate=func.now(),
        default=None,
    )

    # Optional enum field
    activityLevel: Mapped[Optional[HubActivityLevel]] = mapped_column(
        HubActivityLevelEnum,
        default=None,
        server_default=sql.null(),
    )  # user's preferred activity level

    lastHubJoinAt: Mapped[Optional[datetime]] = mapped_column(
        DateTime(), default=None, server_default=sql.null()
    )  # Last time the user joined a hub

    # nextauth
    email: Mapped[Optional[str]] = mapped_column(Text(), default=None, server_default=sql.null())
    emailVerified: Mapped[Optional[datetime]] = mapped_column(
        DateTime(), default=None, server_default=sql.null()
    )

    # Boolean flags
    showBadges: Mapped[bool] = mapped_column(Boolean(), default=True, server_default=sql.true())
    mentionOnReply: Mapped[bool] = mapped_column(Boolean(), default=True, server_default=sql.true())
    showNsfwHubs: Mapped[bool] = mapped_column(Boolean(), default=False, server_default=sql.false())

    # Numeric fields
    voteCount: Mapped[int] = mapped_column(Integer(), default=0, server_default=text('0'))
    reputation: Mapped[int] = mapped_column(Integer(), default=0, server_default=text('0'))
    messageCount: Mapped[int] = mapped_column(Integer(), default=0, server_default=text('0'))
    hubJoinCount: Mapped[int] = mapped_column(Integer(), default=0, server_default=text('0'))
    hubEngagementScore: Mapped[float] = mapped_column(
        Float, default=0.0, server_default=text('0.0')
    )  # Calculated engagement metric

    locale: Mapped[Optional[str]] = mapped_column(Text(), default='en')
    lastVoted: Mapped[Optional[datetime]] = mapped_column(
        DateTime(), default=None, server_default=sql.null()
    )

    badges: Mapped[list[Badges]] = mapped_column(
        ARRAY(BadgesEnum),
        default=list,
        server_default=text('\'{}\'::"Badges"[]'),
    )

    # Hub recommendation preferences
    preferredLanguages: Mapped[list[str]] = mapped_column(
        ARRAY(Text()), default=list, server_default=text("'{}'::text[]")
    )

    # Indexes
    __table_args__ = (
        Index('User_reputation_idx', 'reputation'),
        Index('User_locale_idx', 'locale'),
        Index('User_email_idx', 'email'),
        Index('User_voteCount_idx', 'voteCount'),
        Index('User_lastVoted_idx', 'lastVoted'),
        Index('User_createdAt_idx', 'createdAt'),
    )
