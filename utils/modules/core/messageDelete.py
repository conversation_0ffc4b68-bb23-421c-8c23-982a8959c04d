import asyncio
from enum import Enum
from typing import TYPE_CHECKING, Optional, Sequence, Tuple, Union

import discord
from discord.ext import commands
from sqlalchemy import select, delete
from sqlalchemy.ext.asyncio import AsyncSession

from utils.constants import logger
from utils.modules.common.error_utils import send_error_message as send_generic_error_message
from utils.modules.core.db.models import Broadcast, Connection, Hub, Message
from utils.modules.core.cache import webhook_cache
from utils.modules.core.i18n import t
from utils.modules.core.moderation import fetch_original_msg_with_extra
from utils.modules.errors.customErrors import MessageDeletionError
from utils.modules.events.hubLoggingHelpers import HubLogger
from utils.modules.hub.constants import HubPermissionLevel
from utils.modules.services.permission_service import PermissionService

if TYPE_CHECKING:
    from main import Bot

DiscordContext = Union[discord.Interaction['Bot'], commands.Context['Bot']]


class MessageDeleteResult(Enum):
    SUCCESS = 'success'
    PARTIAL = 'partial'
    NOT_FOUND = 'not_found'


class MessageDeletionService:
    def __init__(self, bot: 'Bot'):
        self.bot = bot

    # region High-Level Methods

    async def delete_message_from_context(
        self,
        ctx: DiscordContext,
        message: Union[discord.Message, str],
        reason: Optional[str] = None,
        locale: str = 'en',
    ) -> bool:
        """
        Handles the entire message deletion process initiated by a user command or interaction.
        This includes permission checks, deletion, and user feedback.

        Returns:
            True if the deletion was successful, otherwise raises MessageDeletionError.

        Raises:
            MessageDeletionError: If any part of the validation or deletion process fails.
        """
        moderator = ctx.user if isinstance(ctx, discord.Interaction) else ctx.author
        message_id = message if isinstance(message, str) else str(message.id)

        try:
            # 1. Validation and Permission Check
            async with self.bot.db.get_session() as session:
                message_result = await fetch_original_msg_with_extra(session, message_id)

                if not message_result:
                    raise MessageDeletionError(
                        f'{self.bot.emotes.x_icon} {t("responses.moderation.delete.notInterChatMessage", locale=locale)}'
                    )

                _, _, hub_id = message_result

                hub = await session.get(Hub, hub_id)
                if not hub:
                    raise MessageDeletionError(
                        f'{self.bot.emotes.x_icon} {t("responses.moderation.errors.hubNotFoundForMessage", locale=locale)}'
                    )

                await self._validate_moderator_permissions(
                    session, moderator, hub, message_result[0].authorId, locale
                )

            # 2. Perform the Core Deletion Logic
            await self.execute_deletion(
                message_id=message_id,
                reason=reason,
                moderator_id=str(moderator.id),
                moderator_name=str(moderator),
            )

            # 3. Send Success Feedback
            await self.send_success_message(ctx, message_id, locale)
            return True

        except MessageDeletionError as e:
            await self.send_error_message(ctx, str(e), locale)
            raise  # Re-raise the exception after notifying the user

    # endregion

    # region Core Deletion Logic Methods

    async def execute_deletion(
        self,
        message_id: str,
        reason: Optional[str] = None,
        moderator_id: Optional[str] = None,
        moderator_name: Optional[str] = None,
    ) -> MessageDeleteResult:
        """
        Performs the low-level deletion of an InterChat message and its broadcasts.
        """
        async with self.bot.db.get_session() as session:
            message_data = await session.execute(
                select(Message.content, Message.hubId, Message.channelId).where(
                    Message.id == message_id
                )
            )
            message_info = message_data.tuples().first()

            if not message_info:
                return MessageDeleteResult.NOT_FOUND

            has_references = (
                await session.execute(
                    select(Message.id).where(Message.referredMessageId == message_id).limit(1)
                )
            ).first() is not None

            broadcast_result = await session.execute(
                select(Broadcast.channelId, Broadcast.id, Connection.parentId)
                .join(Connection, Connection.channelId == Broadcast.channelId)
                .where(Broadcast.messageId == message_id)
            )
            broadcasts = broadcast_result.tuples().all()

            # Always delete from Discord first
            await self._delete_broadcasted_messages(broadcasts)

            # Database record cleanup
            await session.execute(delete(Broadcast).where(Broadcast.messageId == message_id))
            if not has_references:
                await session.execute(delete(Message).where(Message.id == message_id))
                result = MessageDeleteResult.SUCCESS
            else:
                result = MessageDeleteResult.PARTIAL

            await session.commit()

            content, hub_id, channel_id = message_info

            # Log the moderator action
            if moderator_id and moderator_name:
                hub = await session.get(Hub, hub_id)
                await HubLogger.log_message_delete(
                    hub_id=hub_id,
                    hub_name=hub.name if hub else 'Unknown Hub',
                    message_id=message_id,
                    channel_id=channel_id,
                    original_content=content,
                    moderator_id=moderator_id,
                    moderator_name=moderator_name,
                    reason=reason,
                )

            return result

    async def _delete_broadcasted_messages(
        self, broadcasts: Sequence[Tuple[str, str, str | None]]
    ) -> tuple[int, int]:
        """
        Deletes all Discord messages associated with a list of broadcasts.

        Returns:
            A tuple containing the number of successful deletions and the number of failures.
        """
        if not broadcasts:
            return 0, 0

        semaphore = asyncio.Semaphore(10)

        async def limited_delete(task):
            async with semaphore:
                return await task

        tasks = [
            self._delete_single_webhook_message(channel_id, broadcast_message_id, bool(parent_id))
            for channel_id, broadcast_message_id, parent_id in broadcasts
        ]

        results = await asyncio.gather(*[limited_delete(t) for t in tasks], return_exceptions=True)

        failed_deletions = [r for r in results if isinstance(r, Exception)]
        if failed_deletions:
            logger.warning(f'Failed to delete {len(failed_deletions)} webhook messages.')

        return len(broadcasts) - len(failed_deletions), len(failed_deletions)

    async def _delete_single_webhook_message(
        self, channel_id: str, message_id: str, is_thread: bool
    ) -> None:
        try:
            webhook = await self._get_webhook_for_channel(channel_id)
            if not webhook:
                logger.warning(
                    f'No webhook found for channel {channel_id}, cannot delete message {message_id}.'
                )
                return

            if is_thread:
                target = discord.Object(id=int(channel_id))
                await webhook.delete_message(int(message_id), thread=target)
            else:
                await webhook.delete_message(int(message_id))

        except discord.NotFound:
            # This is not an error, the message is already gone.
            logger.debug(f'Webhook message {message_id} already deleted in channel {channel_id}.')
        except discord.Forbidden:
            logger.error(
                f'Forbidden: Lacking permissions to delete message {message_id} in channel {channel_id}.'
            )
            # Consider clearing cache as webhook might have wrong perms
            await webhook_cache.clear_webhook_url(channel_id)
        except Exception as e:
            logger.error(f'An unexpected error occurred deleting webhook message {message_id}: {e}')
            raise

    # endregion

    # region Helper & Utility Methods

    async def _validate_moderator_permissions(
        self,
        session: AsyncSession,
        moderator: Union[discord.User, discord.Member],
        hub: Hub,
        message_author_id: str,
        locale: str,
    ) -> None:
        """Ensures the moderator has the required permissions in the hub."""
        # Users can always delete their own messages
        if moderator.id == int(message_author_id):
            return

        perm_service = PermissionService(session)
        has_perm, _ = await perm_service.check_permission_from_hub(
            hub, str(moderator.id), HubPermissionLevel.MODERATOR
        )
        if not has_perm:
            permission_name = HubPermissionLevel.MODERATOR.name.title()
            raise MessageDeletionError(
                f'{self.bot.emotes.x_icon} {t("responses.infractions.permissions.insufficient", locale=locale, permission=permission_name)}'
            )

    async def _get_webhook_for_channel(self, channel_id: str) -> Optional[discord.Webhook]:
        """Retrieves a webhook for a channel, utilizing a cache."""
        webhook_url = await webhook_cache.get_webhook_url(channel_id)
        if not webhook_url:
            async with self.bot.db.get_session() as session:
                webhook_url = await session.scalar(
                    select(Connection.webhookURL).where(Connection.channelId == channel_id)
                )
            if webhook_url:
                await webhook_cache.set_webhook_url(channel_id, webhook_url)

        return (
            discord.Webhook.from_url(webhook_url, session=self.bot.http_session)
            if webhook_url
            else None
        )

    async def send_success_message(
        self, ctx: DiscordContext, message_id: str, locale: str = 'en'
    ) -> None:
        embed = discord.Embed(
            title=t('ui.common.titles.success', locale=locale),
            description=f'{self.bot.emotes.tick} {t("responses.moderation.delete.success", locale=locale, messageId=message_id)}',
            color=discord.Color.green(),
        )
        await self._send_response(ctx, embed=embed, ephemeral=True)

    async def send_error_message(self, ctx: DiscordContext, error: str, locale: str = 'en') -> None:
        await send_generic_error_message(
            ctx, error, t('ui.common.titles.error', locale=locale), ephemeral=True
        )

    async def _send_response(
        self, ctx: DiscordContext, embed: discord.Embed, ephemeral: bool = True
    ) -> None:
        if isinstance(ctx, discord.Interaction):
            if not ctx.response.is_done():
                await ctx.response.send_message(embed=embed, ephemeral=ephemeral)
            else:
                await ctx.followup.send(embed=embed, ephemeral=ephemeral)
        else:
            await ctx.send(embed=embed)

    # endregion


async def delete_interchat_message(
    bot: 'Bot',
    message_id: str,
    reason: Optional[str] = None,
    moderator_id: Optional[str] = None,
    moderator_name: Optional[str] = None,
) -> MessageDeleteResult:
    """Deletes an InterChat message."""
    deletion_service = MessageDeletionService(bot)
    return await deletion_service.execute_deletion(
        message_id=message_id,
        reason=reason,
        moderator_id=moderator_id,
        moderator_name=moderator_name,
    )
