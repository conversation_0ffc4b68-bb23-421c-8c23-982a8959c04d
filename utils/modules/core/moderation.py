from typing import TYPE_CHECKING, <PERSON><PERSON>, <PERSON><PERSON>, Union

import discord
from sqlalchemy import and_, or_, select

from utils.constants import constants
from utils.modules.common.database import DatabaseQueries
from utils.modules.core.db.models import (
    Hub,
    HubModerator,
    HubModeratorRole,
    Message,
    User,
)

if TYPE_CHECKING:
    from sqlalchemy.ext.asyncio import AsyncSession

    from main import Bot


async def get_user_moderated_hubs(bot: 'Bot', user_id: str):
    """Get all hubs where the user has Moderator or Manager permissions."""
    async with bot.db.get_session() as session:
        stmt = (
            select(Hub)
            .join(HubModerator, Hub.id == HubModerator.hubId, isouter=True)
            .where(
                or_(
                    Hub.ownerId == user_id,
                    and_(
                        HubModerator.userId == user_id,
                        HubModerator.role.in_(
                            [HubModeratorRole.MODERATOR, HubModeratorRole.MANAGER]
                        ),
                    ),
                )
            )
            .distinct()
        )
        result = await session.execute(stmt)
        return result.scalars().all()


async def fetch_original_message(session: 'AsyncSession', message_id: str) -> Optional[Message]:
    """Fetch original message, first directly then through broadcast relationship."""
    return await DatabaseQueries.fetch_message_with_broadcast_fallback(session, message_id)


async def fetch_original_msg_with_extra(
    session: 'AsyncSession', message_id: str
) -> Optional[Tuple[Message, User, str]]:
    """
    Fetch original message with user and hub data.

    Returns:
        Tuple of (Message, User, Hub.id) if found, otherwise None
    """
    return await DatabaseQueries.fetch_message_with_user_and_hub(session, message_id)


def mod_panel_embed(
    bot: 'Bot',
    hub: Hub,
    target_user: Optional[Union[discord.User, discord.Member]],
    target_server: Optional[discord.Guild],
    target_message: Optional[discord.Message],
    msg_content: str,
    user_infractions: Optional[int],
    server_infractions: Optional[int],
    _locale: str,
) -> discord.Embed:
    """Create the embed for the mod panel."""
    emotes = bot.emotes
    message_field_str = (
        f'> **[Message Content]({target_message.jump_url})** {msg_content.replace("`", "")[:50]}\n'
        if target_message
        else ''
    )
    server_str = (
        f'> **Server:** {target_server.name} (`{target_server.id}`)\n' if target_server else ''
    )

    embed = discord.Embed(
        title='Moderation Panel',
        description=' ',
        color=constants.color,
    )

    embed.add_field(
        name=f'{emotes.chat_icon} Context',
        value=f'{message_field_str}{server_str}> **Hub:** {hub.name}',
        inline=False,
    )
    # TODO: Include infraction counts when we have that data

    if target_user:
        safe = discord.utils.escape_markdown(target_user.name)
        user_info_str = f'> **User:** {safe} (`{target_user.id}`)\n'
    else:
        user_info_str = ''

    server_info_str = (
        f'> **Server:** {target_server.name} (`{target_server.id}`)\n'
        f'> **Owner:** <@{target_server.owner_id}>\n'
        if target_server
        else ''
    )
    embed.add_field(
        name=f'{emotes.info_icon} Target Information',
        value=f'{user_info_str}{server_info_str}',
        inline=False,
    )

    if target_user:
        embed.set_author(name=target_user.name, icon_url=target_user.display_avatar.url)
    elif target_server:
        embed.set_author(
            name=target_server.name,
            icon_url=target_server.icon.url if target_server.icon else None,
        )

    return embed
