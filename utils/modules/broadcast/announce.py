import discord
from sqlalchemy import select
from typing import TYPE_CHECKING
from utils.modules.core.db.models import Connection, Hub

from utils.constants import logger

if TYPE_CHECKING:
    from main import Bot


async def broadcast_announcement(bot: 'Bot', hub: Hub, embed: discord.Embed):
    async with bot.db.get_session() as session:
        stmt = select(Connection.webhookURL).where(Connection.hubId == hub.id)
        webhook_urls = (await session.scalars(stmt)).all()

    for url in webhook_urls:
        webhook = discord.Webhook.from_url(url, client=bot)
        try:
            await webhook.send(embed=embed, username=f'{hub.name} | Official Hub Announcement')
        except discord.NotFound:
            logger.warning(f'Webhook {url} raised discord.NotFound - Skipping.')
            continue
        except Exception as e:
            raise Exception(f'Failed to send announcement to webhook {url}: {e}') from e
