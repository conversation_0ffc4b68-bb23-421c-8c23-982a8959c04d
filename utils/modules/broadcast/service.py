import asyncio
import random
from typing import TYPE_CHECKING, List, Optional, Tuple

import aiohttp
import discord
from sqlalchemy import and_, func, select
from sqlalchemy.ext.asyncio import AsyncSession

from utils.constants import logger
from utils.modules.common.database import DatabaseQueries
from utils.modules.common.message_utils import Message<PERSON><PERSON><PERSON>er, MessageValidator
from utils.modules.common.user_utils import UserManager
from utils.modules.services.hubService import HubService
from utils.modules.services.userService import UserService
from utils.modules.services.validationService import ValidationService
from utils.modules.ui.views.hubRulesAcceptanceView import HubRulesAcceptanceView

if TYPE_CHECKING:
    from utils.modules.broadcast.checks import ValidationResult
from utils.constants import constants
from utils.modules.broadcast.messageUtils import (
    create_allowed_mentions_for_broadcast,
    create_reply_embed,
    format_message_with_attachments,
    format_user_badges,
    get_broadcast_ids_for_channels,
    is_reply_mention_required,
    store_message_and_broadcasts,
)
from utils.modules.broadcast.notify import NotificationManager
from utils.modules.core.antiSpam import AntiSpamManager
from utils.modules.core.cache import previous_message_cache
from utils.modules.core.db.models import (
    Connection,
    Hub,
    HubRulesAcceptance,
    Message,
    ServerData,
    User,
)
from utils.modules.core.moderation import fetch_original_msg_with_extra

if TYPE_CHECKING:
    from main import Bot


class MessageBroadcastService:
    def __init__(self, bot: 'Bot', session: 'AsyncSession'):
        self.bot = bot
        self.session = session

        # Retry configuration
        self.max_retries = 3
        self.base_delay = 1.0
        self.max_delay = 30.0
        self.backoff_factor = 2.0
        self.jitter = True

        self.hubsvc = HubService(self.session)
        self.usersvc = UserService(self.session)
        self.validationsvc = ValidationService(self.session)

    def _calculate_retry_delay(self, attempt: int) -> float:
        """Calculate exponential backoff delay with jitter."""
        delay = min(self.base_delay * (self.backoff_factor**attempt), self.max_delay)

        if self.jitter:
            # Add random jitter ±20% to avoid thundering herd
            jitter_range = delay * 0.2
            delay += random.uniform(-jitter_range, jitter_range)

        return max(0.1, delay)  # Minimum 100ms delay

    async def _should_retry_error(self, error: Exception) -> bool:
        """Determine if an error should trigger a retry."""
        if isinstance(error, (BrokenPipeError, ConnectionError, aiohttp.ClientOSError)):
            return True

        if isinstance(error, discord.HTTPException):
            # Retry on rate limits and server errors
            if error.status == 429:  # Rate limited
                return True
            if 500 <= error.status < 600:  # Server errors
                return True
            # Don't retry on client errors (4xx) except rate limits
            return False

        if isinstance(error, (asyncio.TimeoutError, aiohttp.ClientTimeout)):
            return True

        # Don't retry unknown errors to avoid infinite loops
        return False

    async def _get_rate_limit_delay(self, error: discord.HTTPException) -> float:
        """Extract rate limit delay from Discord error."""
        if hasattr(error, 'retry_after') and error.retry_after:
            return float(error.retry_after)
        return 1.0

    async def process_message(self, message: discord.Message) -> bool:
        """
        Process a message for broadcasting.
        Returns True if message was processed, False if it should be ignored.
        """
        if not MessageValidator.is_processable_message(message):
            return False

        # Check if message is from a connected channel
        connection_data = await self._get_connection_and_hub(
            str(message.channel.id), str(message.author.id)
        )
        if not connection_data:
            return False

        connection, hub, rules_acceptance = connection_data
        logger.debug(
            f'Message {message.id} from connected channel {message.channel.id} '
            f'to hub {hub.id} ({hub.name})'
        )

        await UserManager.upsert_user(message.author, self.session)

        # Check if this is user's first message in hub and show welcome rules
        should_stop = await self._check_and_show_welcome_rules(message, hub, rules_acceptance)
        if should_stop:
            logger.debug(
                f'Stopping processing for message {message.id}: showing welcome rules to user {message.author.id}'
            )
            return False

        # Validate message
        logger.debug(f'Starting validation for message {message.id}')
        validation_result = await self.validationsvc.validate_message_for_broadcast(message, hub)
        if not validation_result.is_valid:
            logger.info(
                f'Message {message.id} failed validation: {validation_result.reason} '
                f'(notify: {validation_result.should_notify})'
            )
            await self._handle_validation_failure(message, validation_result, hub)
            return False

        logger.debug(f'Message {message.id} passed all validations')

        # Process and broadcast message
        logger.info(
            f'Broadcasting message {message.id} from user {message.author.id} '
            f'in hub {hub.id} ({hub.name})'
        )
        await self._broadcast_message_to_hub(message, connection, hub)
        logger.debug(f'Successfully completed broadcast for message {message.id}')
        return True

    async def _get_connection_and_hub(
        self, channel_id: str, user_id: str
    ) -> Optional[Tuple[Connection, Hub, HubRulesAcceptance]]:
        stmt = (
            select(Connection, Hub, HubRulesAcceptance)
            .select_from(Connection)
            .join(Hub, Connection.hubId == Hub.id)  # Hub must exist if Connection exists
            .join(
                HubRulesAcceptance,
                and_(
                    HubRulesAcceptance.hubId == Hub.id,
                    HubRulesAcceptance.userId == user_id,
                ),
                isouter=True,
            )
            .where(
                Connection.channelId == channel_id,
                Connection.connected.is_(True),
            )
            .limit(1)
        )

        result = await self.session.execute(stmt)
        connection_data = result.tuples().first()

        if connection_data:
            connection, hub, rules_acceptance = connection_data
            logger.debug(
                f'Found connection for channel {channel_id}: '
                f'hub={hub.id} ({hub.name}), connected={connection.connected}'
            )

        return connection_data

    async def _handle_validation_failure(
        self, message: discord.Message, validation_result: 'ValidationResult', hub: Hub
    ):
        """Handle validation failure with appropriate actions."""
        logger.debug(
            f'Handling validation failure for message {message.id}: {validation_result.reason}'
        )

        # Handle spam-specific actions
        if validation_result.spam_action:
            logger.info(
                f'Applying spam action "{validation_result.spam_action}" for user {message.author.id} '
                f'in hub {hub.id}'
            )
            spam_manager = AntiSpamManager(self.session)

            if validation_result.spam_action == 'mute':
                # Mute the user for spam
                infraction_id = await spam_manager.mute_user_for_spam(
                    self.bot, str(message.author.id), hub.id, message.author.name
                )
                if infraction_id:
                    logger.info(
                        f'Successfully muted user {message.author.id} for spam (infraction: {infraction_id})'
                    )
                    # Update notification message with infraction ID for tracking
                    validation_result = validation_result._replace(infraction_id=infraction_id)
                else:
                    logger.warning(f'Failed to mute user {message.author.id} for spam')

        # Send notification if needed
        if validation_result.should_notify and validation_result.notification_message:
            logger.debug(
                f'Sending validation notification to user {message.author.id}: '
                f'{validation_result.notification_message[:100]}...'
            )
            notifier = NotificationManager(self.session)
            await notifier.send_validation_notification(
                message.author,
                validation_result.notification_message,
                validation_result.infraction_id,
            )
        else:
            logger.debug(f'No notification needed for failed message {message.id}')

        logger.info(f'Message {message.id} blocked: {validation_result.reason}')

    async def _broadcast_message_to_hub(
        self, message: discord.Message, connection: Connection, hub: Hub
    ):
        """Broadcast message to all connections in the hub."""
        logger.debug(f'Starting broadcast for message {message.id} in hub {hub.id} ({hub.name})')

        # Process message content
        processed_content, attachment_urls = format_message_with_attachments(
            message.content, message.attachments, message.stickers
        )
        logger.debug(
            f'Processed message content: {len(processed_content)} chars, '
            f'{len(attachment_urls)} attachments'
        )

        # Get other connections in the hub
        other_connections = await self._get_other_connections(hub.id, connection.channelId)
        if not other_connections:
            logger.debug(
                f'No other connections found in hub {hub.id} to broadcast to '
                f'(excluding source channel {connection.channelId})'
            )
            return

        logger.debug(
            f'Found {len(other_connections)} target connections for broadcast in hub {hub.id}'
        )

        # Check if previous message was from a different user
        current_author_id = str(message.author.id)
        previous_author_id = await previous_message_cache.get_previous_author(hub.id)
        should_show_badges = previous_author_id != current_author_id

        logger.debug(
            f'Badge display check: previous_author={previous_author_id}, '
            f'current_author={current_author_id}, should_show_badges={should_show_badges}'
        )

        reply_data = await self._get_reply_data(message)
        if reply_data:
            logger.debug(
                f'Message {message.id} is a reply to message {reply_data[0].id} '
                f'from user {reply_data[1].id}'
            )

        badge_prefix = ''
        if should_show_badges:
            badge_prefix = await format_user_badges(self.bot, message.author.id, self.usersvc)
            logger.debug(f'Generated badge prefix for user {message.author.id}: {badge_prefix}')

        # Update the previous message cache with current author
        await previous_message_cache.set_previous_author(hub.id, current_author_id)

        # Precompute broadcast IDs for the replied message across all target channels (if this is a reply)
        channel_to_broadcast_id: dict[str, str] = {}
        if reply_data:
            original_message, _, _ = reply_data
            logger.debug(f'Precomputing broadcast IDs for replied message {original_message.id}')
            channel_to_broadcast_id = await get_broadcast_ids_for_channels(
                self.session, original_message.id, [c.channelId for c in other_connections]
            )
            logger.debug(
                f'Found {len(channel_to_broadcast_id)} precomputed broadcast IDs for reply context'
            )

        # Broadcast to other connections
        logger.info(
            f'Broadcasting message {message.id} to {len(other_connections)} connections '
            f'in hub {hub.id}'
        )
        broadcast_message_ids = await self._send_broadcasts(
            message,
            other_connections,
            processed_content,
            badge_prefix,
            reply_data,
            channel_to_broadcast_id,
            attachment_urls,
        )

        logger.info(
            f'Successfully sent {len(broadcast_message_ids)} broadcasts for message {message.id} '
            f'(attempted {len(other_connections)})'
        )

        # Store message and broadcast records
        logger.debug(f'Storing message and broadcast records for message {message.id}')
        await store_message_and_broadcasts(
            message,
            hub,
            processed_content,
            broadcast_message_ids,
            self.session,
            referred_message=reply_data[0] if reply_data else None,
        )

        # Increment message counts for user and server
        logger.debug(f'Incrementing message counts for message {message.id}')
        await self._increment_message_counts(message)

    async def _get_other_connections(
        self, hub_id: str, exclude_channel_id: str
    ) -> List[Connection]:
        """Get all other connections in the hub."""
        result = await DatabaseQueries.fetch_hub_connections(
            self.session, hub_id, exclude_channel_id
        )
        return list(result)

    async def _get_reply_data(self, message: discord.Message):
        """Get reply data if this message is a reply."""
        if message.reference and message.reference.message_id:
            logger.debug(
                f'Message {message.id} is a reply to message {message.reference.message_id}'
            )
            reply_data = await fetch_original_msg_with_extra(
                self.session, str(message.reference.message_id)
            )
            if reply_data:
                logger.debug(
                    f'Found reply data for original message {message.reference.message_id}'
                )
            else:
                logger.debug(f'No reply data found for message {message.reference.message_id}')
            return reply_data
        return None

    async def _send_broadcasts(
        self,
        message: discord.Message,
        connections: List[Connection],
        processed_content: str,
        badge_prefix: str,
        reply_data: Optional[Tuple[Message, User, str]],
        channel_to_broadcast_id: dict[str, str],
        attachment_urls: list[str],
    ) -> List[Tuple[str, str, str]]:
        """Send broadcasts to all connections with retry logic."""
        broadcast_message_ids: List[Tuple[str, str, str]] = []

        # Extract reply information for reply mentions
        replied_user_id = None
        original_server_id = None
        if reply_data:
            replied_message, replied_user, _ = reply_data
            replied_user_id = replied_user.id
            original_server_id = replied_message.guildId

        start_time = discord.utils.utcnow().timestamp() * 1000

        async def send_one_with_retry(conn: Connection) -> Optional[Tuple[str, str, str]]:
            """Send broadcast with exponential backoff retry logic."""
            last_error = None

            for attempt in range(self.max_retries + 1):
                try:
                    logger.debug(
                        f'Sending broadcast to channel {conn.channelId} '
                        f'(attempt {attempt + 1}/{self.max_retries + 1})'
                    )

                    sent_message = await self._send_single_broadcast(
                        message,
                        conn,
                        processed_content,
                        badge_prefix,
                        replied_user_id,
                        original_server_id,
                        reply_data,
                        channel_to_broadcast_id,
                        attachment_urls,
                    )

                    if sent_message:
                        if attempt > 0:
                            logger.info(
                                f'Successfully sent broadcast to channel {conn.channelId} '
                                f'after {attempt + 1} attempts'
                            )
                        else:
                            logger.debug(
                                f'Successfully sent broadcast to channel {conn.channelId}: '
                                f'message ID {sent_message.id}'
                            )
                        return (str(sent_message.id), conn.channelId, conn.serverId)
                    else:
                        logger.warning(
                            f'Failed to send broadcast to channel {conn.channelId}: '
                            f'no message returned (attempt {attempt + 1})'
                        )
                        return None

                except discord.NotFound as e:
                    if e.code == 10015:  # Unknown Webhook error code
                        logger.warning(
                            f'Webhook deleted for channel {conn.channelId}: {e} - disconnecting connection'
                        )
                        # Disconnect the connection due to webhook deletion - don't retry
                        await self._disconnect_connection_webhook_deleted(conn)
                        return None
                    else:
                        logger.error(
                            f'Not found error for channel {conn.channelId}: {e}', exc_info=True
                        )
                        return None

                except discord.HTTPException as e:
                    last_error = e

                    # Handle rate limits specially
                    if e.status == 429:
                        retry_delay = await self._get_rate_limit_delay(e)
                        logger.warning(
                            f'Rate limited for channel {conn.channelId}, '
                            f'waiting {retry_delay}s before retry {attempt + 1}'
                        )
                        await asyncio.sleep(retry_delay)
                        continue

                    # Check if we should retry this error
                    if await self._should_retry_error(e) and attempt < self.max_retries:
                        retry_delay = self._calculate_retry_delay(attempt)
                        logger.warning(
                            f'HTTP error for channel {conn.channelId} '
                            f'(status: {e.status}, code: {e.code}), '
                            f'retrying in {retry_delay:.2f}s (attempt {attempt + 1})'
                        )
                        await asyncio.sleep(retry_delay)
                        continue
                    else:
                        logger.error(
                            f'HTTP error broadcasting to channel {conn.channelId}: {e} '
                            f'(status: {e.status}, code: {e.code}) - not retrying',
                            exc_info=True,
                        )
                        return None

                except (BrokenPipeError, ConnectionError, aiohttp.ClientOSError) as e:
                    last_error = e

                    if attempt < self.max_retries:
                        retry_delay = self._calculate_retry_delay(attempt)
                        logger.warning(
                            f'Connection error for channel {conn.channelId}: {e} - '
                            f'retrying in {retry_delay:.2f}s (attempt {attempt + 1})'
                        )
                        await asyncio.sleep(retry_delay)
                        continue
                    else:
                        logger.error(
                            f'Connection error for channel {conn.channelId}: {e} - '
                            f'max retries exceeded',
                            exc_info=True,
                        )
                        return None

                except (asyncio.TimeoutError, aiohttp.ClientError) as e:
                    last_error = e

                    if attempt < self.max_retries:
                        retry_delay = self._calculate_retry_delay(attempt)
                        logger.warning(
                            f'Timeout error for channel {conn.channelId}: {e} - '
                            f'retrying in {retry_delay:.2f}s (attempt {attempt + 1})'
                        )
                        await asyncio.sleep(retry_delay)
                        continue
                    else:
                        logger.error(
                            f'Timeout error for channel {conn.channelId}: {e} - '
                            f'max retries exceeded',
                            exc_info=True,
                        )
                        return None

                except Exception as e:
                    last_error = e
                    logger.error(
                        f'Unexpected error broadcasting to channel {conn.channelId}: {e}',
                        exc_info=True,
                    )
                    return None

            # All retries exhausted
            logger.error(
                f'Failed to broadcast to channel {conn.channelId} after '
                f'{self.max_retries + 1} attempts. Last error: {last_error}'
            )
            return None

        logger.debug(
            f'Starting parallel broadcast to {len(connections)} channels in hub '
            f'{connections[0].hubId if connections else "unknown"}'
        )

        # Execute all broadcasts concurrently with retry logic
        results = await asyncio.gather(
            *(send_one_with_retry(c) for c in connections), return_exceptions=False
        )

        successful_broadcasts = [item for item in results if item is not None]
        failed_broadcasts = len(connections) - len(successful_broadcasts)

        logger.info(
            f'Broadcast results for message {message.id}: {len(successful_broadcasts)} successful, '
            f'{failed_broadcasts} failed out of {len(connections)} attempts'
        )

        for item in successful_broadcasts:
            broadcast_message_ids.append(item)

        logger.debug(
            f'Broadcasted message from {message.author} to {len(broadcast_message_ids)} channels '
            f'in {discord.utils.utcnow().timestamp() * 1000 - start_time}ms'
        )

        return broadcast_message_ids

    async def _send_single_broadcast(
        self,
        message: discord.Message,
        connection: Connection,
        processed_content: str,
        badge_prefix: str,
        replied_user_id: Optional[str],
        original_server_id: Optional[str],
        reply_data: Optional[Tuple[Message, User, str]],
        channel_to_broadcast_id: dict[str, str],
        attachment_urls: list[str],
    ) -> Optional[discord.WebhookMessage]:
        """Send a single broadcast message."""
        logger.debug(
            f'Preparing broadcast for channel {connection.channelId} '
            f'(webhook: {connection.webhookURL[:50]}...)'
        )

        webhook = discord.Webhook.from_url(url=connection.webhookURL, session=self.bot.http_session)

        should_mention = bool(
            replied_user_id and is_reply_mention_required(connection.serverId, original_server_id)
        )

        logger.debug(
            f'Broadcast to {connection.channelId}: mention_required={should_mention}, '
            f'has_reply_data={reply_data is not None}, attachments={len(attachment_urls)}'
        )

        # Create allowed mentions for this specific broadcast
        allowed_mentions = create_allowed_mentions_for_broadcast(
            int(replied_user_id) if should_mention and replied_user_id else None
        )

        # Prepare embeds and content
        reply_embed: Optional[discord.Embed] = None
        try:
            # Use precomputed broadcast id if available for the target channel
            precomputed_broadcast_id = channel_to_broadcast_id.get(connection.channelId)
            if not attachment_urls:
                reply_embed = create_reply_embed(
                    message.reference,
                    reply_data,
                    connection.channelId,
                    connection.serverId,
                    # use broadcast ID if available; otherwise, send it to the server that originated the replied-to message
                    precomputed_broadcast_id or reply_data[0].id if reply_data else None,
                )
                if reply_embed:
                    logger.debug(f'Created reply embed for channel {connection.channelId}')
        except Exception as e:
            logger.warning(f'Failed to create reply embed for channel {connection.channelId}: {e}')
            reply_embed = None

        embeds = [reply_embed] if reply_embed else []
        reply_mention = f'<@{replied_user_id}> ' if should_mention else ''
        badge_pre = f'-# {badge_prefix} ' if badge_prefix else ''
        final_content = f'{badge_pre}\n{reply_mention}{processed_content}'

        webhook_username = MessageFormatter.format_webhook_username(
            message.author.name, getattr(message.guild, 'name', 'Unknown Server')
        )

        logger.debug(
            f'Sending webhook message to channel {connection.channelId}: '
            f'username="{webhook_username}", content_length={len(final_content)}, '
            f'embeds={len(embeds)}, thread={connection.parentId is not None}'
        )

        try:
            # Send message (handle threads vs regular channels)
            if connection.parentId:
                sent_message = await webhook.send(
                    final_content,
                    username=webhook_username,
                    avatar_url=message.author.display_avatar.url,
                    thread=discord.Object(id=connection.channelId),
                    embeds=embeds,
                    allowed_mentions=allowed_mentions,
                    wait=True,
                )
            else:
                sent_message = await webhook.send(
                    final_content,
                    username=webhook_username,
                    avatar_url=message.author.display_avatar.url,
                    embeds=embeds,
                    allowed_mentions=allowed_mentions,
                    wait=True,
                )

            logger.debug(f'Successfully sent webhook message to channel {connection.channelId}')
            return sent_message

        except Exception as e:
            logger.error(
                f'Failed to send webhook message to channel {connection.channelId}: {e}',
                exc_info=True,
            )
            raise

    async def _check_and_show_welcome_rules(
        self, message: discord.Message, hub: Hub, rules_acceptance: HubRulesAcceptance
    ):
        """
        Check if user is new to hub and show welcome rules if configured.
        Returns:
            True if message processing should stop (rules shown) or False to continue.
        """
        # Only show rules if hub has rules configured
        if not hub.rules or len(hub.rules) == 0:
            return False

        # Check if user has already seen rules for this hub
        existing_acceptance = (
            rules_acceptance.userId == str(message.author.id) if rules_acceptance else None
        )

        if existing_acceptance:
            return False

        # Send welcome rules message with buttons (don't create record yet)
        # The record will be created only when user accepts via the button
        await self._send_welcome_rules_message(message, hub)
        return True  # Stop further processing this message

    async def _send_welcome_rules_message(self, message: discord.Message, hub: Hub):
        """Send a welcome message with hub rules to the user."""
        # Format rules as numbered list
        rules_text = ''
        for i, rule in enumerate(hub.rules, 1):
            rules_text += f'**{i}.** {rule}\n'

        # Truncate if too long for embed
        if len(rules_text) > 4000:
            rules_text = rules_text[:3950] + '...\n\n*Some rules truncated due to length.*'

        embed = discord.Embed(
            title=f'Welcome to {hub.name}! 📋',
            description=f'Hi {message.author.mention}! Welcome to the InterChat hub, **{hub.name}**. Please take a moment to read our community rules:\n\n{rules_text}',
            color=constants.color,
        )

        plural = 's' if len(hub.rules) != 1 else ''
        embed.set_footer(
            text=f'{len(hub.rules)} rule{plural} • Please accept these rules to participate in the hub'
        )

        # Create the rules acceptance view
        view = HubRulesAcceptanceView(self.bot, message.author, hub, str(message.channel.id))

        try:
            await message.channel.send(embed=embed, view=view)
        except discord.HTTPException:
            logger.error(
                f'Failed to send welcome rules message for hub {hub.id} to user {message.author.id}'
            )

    async def _increment_message_counts(self, message: discord.Message):
        """Increment message counts for both user and server after successful broadcast."""
        try:
            await UserManager.update_user_activity(str(message.author.id), self.session)
        except Exception as e:
            logger.warning(f'Failed to increment user message count: {e}')

        # Increment server message count
        if message.guild:
            try:
                existing_server = await self.session.get(ServerData, str(message.guild.id))
                if existing_server:
                    existing_server.messageCount += 1
                    existing_server.lastMessageAt = func.now()
                else:
                    # Create new server if doesn't exist
                    new_server = ServerData(
                        id=str(message.guild.id),
                        name=message.guild.name,
                        messageCount=1,
                        lastMessageAt=func.now(),
                        iconUrl=message.guild.icon.url if message.guild.icon else None,
                    )
                    self.session.add(new_server)
            except Exception as e:
                logger.warning(f'Failed to increment server message count: {e}')

        await self.session.commit()
        logger.debug(
            f'Incremented message counts for user {message.author.id}'
            + (f' and server {message.guild.id}' if message.guild else '')
        )

    async def _disconnect_connection_webhook_deleted(
        self,
        connection: Connection,
    ):
        """
        Disconnect a connection due to webhook deletion and notify the channel.
        """
        try:
            # Mark connection as disconnected
            connection.connected = False
            await self.session.commit()

            logger.info(
                f'Disconnected connection {connection.id} (channel {connection.channelId}) '
                f'due to webhook deletion'
            )

            # Try to notify the original channel about the disconnection
            try:
                channel = self.bot.get_channel(int(connection.channelId))
                if not channel:
                    channel = await self.bot.fetch_channel(int(connection.channelId))

                if channel and isinstance(channel, (discord.abc.Messageable)):
                    hub = await self.session.get(Hub, connection.hubId)
                    hub_name = hub.name if hub else 'Unknown Hub'

                    embed = discord.Embed(
                        title=f'{self.bot.emotes.alert_icon} Connection Paused',
                        description=(
                            f"This channel's connection to **{hub_name}** has been paused "
                            f'because the webhook was deleted.\n\n'
                            f'To restore the connection, please ues `/manage connections` or re-join the hub.'
                        ),
                        color=discord.Color.yellow(),
                    )
                    embed.set_footer(text='This notification will only be shown once.')

                    await channel.send(embed=embed)
                    logger.info(
                        f'Sent disconnection notification to channel {connection.channelId}'
                    )

            except Exception as notification_error:
                logger.warning(
                    f'Failed to notify channel {connection.channelId} about disconnection: '
                    f'{notification_error}'
                )

        except Exception as e:
            logger.error(
                f'Failed to disconnect connection {connection.channelId} due to webhook deletion: {e}'
            )
