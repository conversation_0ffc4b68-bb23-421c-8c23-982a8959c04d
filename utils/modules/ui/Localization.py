import discord
import yaml
import json
import os
from typing import Dict, Any, List


class LocaleManager:
    _instance = None
    _initialized = False

    def __new__(cls, locales_path: str = 'locales/'):
        if cls._instance is None:
            cls._instance = super(LocaleManager, cls).__new__(cls)
        return cls._instance

    def __init__(self, locales_path: str = 'locales/'):
        if not self._initialized:
            self.locales = self._load_locales(locales_path)
            self._initialized = True

    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance

    def _merge_dict(self, base: Dict[str, Any], other: Dict[str, Any]) -> Dict[str, Any]:
        for k, v in (other or {}).items():
            if isinstance(v, dict) and isinstance(base.get(k), dict):
                base[k] = self._merge_dict(base[k], v)
            else:
                base[k] = v
        return base

    def _load_single_file(self, file_path: str) -> Dict[str, Any]:
        with open(file_path, 'r', encoding='utf-8') as file:
            if file_path.endswith(('.yaml', '.yml')):
                return yaml.safe_load(file) or {}
            elif file_path.endswith('.json'):
                return json.load(file) or {}
            else:
                return {}

    def _load_locales(self, path: str) -> Dict[str, Any]:
        try:
            if os.path.isfile(path):
                return self._load_single_file(path)

            if os.path.isdir(path):
                locales: Dict[str, Dict[str, Any]] = {}

                for entry in os.listdir(path):
                    lang_dir = os.path.join(path, entry)
                    if os.path.isdir(lang_dir):
                        language = entry
                        aggregated: Dict[str, Any] = locales.get(language, {})
                        for inner in os.listdir(lang_dir):
                            inner_path = os.path.join(lang_dir, inner)
                            if os.path.isfile(inner_path) and inner.endswith(
                                ('.yaml', '.yml', '.json')
                            ):
                                # Merge file content; for YAML, we expect top-level sections (e.g., commands, responses, ui)
                                aggregated = self._merge_dict(
                                    aggregated, self._load_single_file(inner_path)
                                )
                        if aggregated:
                            locales[language] = aggregated

                if not locales:
                    raise ValueError(f'No valid locale files found in directory: {path}')

                return locales

        except FileNotFoundError:
            raise FileNotFoundError(f'Locales path not found: {path}')
        except Exception as e:
            raise Exception(f'Error loading locales: {e}')

    def get_text(self, language: str, key_path: str, **kwargs) -> str:
        keys = key_path.split('.')
        current = self.locales.get(language, {})

        # Traverse for desired language
        for key in keys:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                current = None
                break

        # Fallback to English if not found
        if current is None:
            fallback_current = self.locales.get('en', {})
            for key in keys:
                if isinstance(fallback_current, dict) and key in fallback_current:
                    fallback_current = fallback_current[key]
                else:
                    fallback_current = None
                    break
            current = fallback_current

        # As a last resort, return the key path itself
        if current is None:
            return key_path

        if isinstance(current, str):
            try:
                return current.format(**kwargs)
            except KeyError:
                return current

        # Non-str values: stringify
        return str(current)

    def get_available_languages(self) -> list:
        return sorted(list(self.locales.keys()))

    def has_language(self, language: str) -> bool:
        return language in self.locales


def create_embed(
    language: str,
    title_key: str,
    description_key: str,
    fields: List[Dict[str, str]] | None = None,
    footer_key: str | None = None,
    author_key: str | None = None,
    thumbnail: str | None = None,
    image: str | None = None,
    color: int = 0x9172D8,
    **format_vars,
) -> discord.Embed:
    locale_manager = LocaleManager.get_instance()

    embed = discord.Embed(color=color)

    if title_key:
        title = locale_manager.get_text(language, title_key, **format_vars)
        embed.title = title

    if description_key:
        description = locale_manager.get_text(language, description_key, **format_vars)
        embed.description = description

    if fields:
        for field in fields:
            name = locale_manager.get_text(language, field['name_key'], **format_vars)
            value = locale_manager.get_text(language, field['value_key'], **format_vars)
            inline = bool(field.get('inline', False))
            embed.add_field(name=name, value=value, inline=inline)

    if footer_key:
        footer_text = locale_manager.get_text(language, footer_key, **format_vars)
        embed.set_footer(text=footer_text)

    if author_key:
        author_name = locale_manager.get_text(language, author_key, **format_vars)
        embed.set_author(name=author_name)

    if thumbnail:
        embed.set_thumbnail(url=thumbnail)

    if image:
        embed.set_image(url=image)

    return embed


def get_text(language: str, key_path: str, **kwargs) -> str:
    locale_manager = LocaleManager.get_instance()
    return locale_manager.get_text(language, key_path, **kwargs)


def get_available_languages() -> list:
    locale_manager = LocaleManager.get_instance()
    return locale_manager.get_available_languages()


def has_language(language: str) -> bool:
    locale_manager = LocaleManager.get_instance()
    return locale_manager.has_language(language)


# Initialize on import
LocaleManager()
