import discord
from discord.ui import Modal, TextInput

from utils.modules.core.db.models import DevAlerts
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from main import Bot


class DeveloperAnnouncement(Modal):
    def __init__(self, bot):
        super().__init__(title='Input Required', timeout=300)
        self.bot: 'Bot' = bot

        self._setup_title_input()
        self._setup_description_input()

    def _setup_title_input(self):
        self.title_input: TextInput = TextInput(
            label='Title',
            placeholder='Lorem Ipsum',
            max_length=100,
            required=True,
        )
        self.add_item(self.title_input)

    def _setup_description_input(self):
        self.description_input: TextInput = TextInput(
            label='Description',
            placeholder='...',
            style=discord.TextStyle.paragraph,
            required=True,
        )
        self.add_item(self.description_input)

    async def on_submit(self, interaction: discord.Interaction['Bot']):  # type: ignore[override]
        await interaction.response.defer()
        async with self.bot.db.get_session() as session:
            alert = DevAlerts(title=self.title_input.value, content=self.description_input.value)
            session.add(alert)
            await session.commit()

        embed = discord.Embed(
            title='Success!',
            description=f'{self.bot.emotes.tick} Announcement queued.',
            color=discord.Color.green(),
        )
        await interaction.followup.send(embed=embed, ephemeral=True)
