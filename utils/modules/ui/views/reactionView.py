import discord
from discord.ui import <PERSON><PERSON>, View
from typing import TYPE_CHECKING, Union, Set


if TYPE_CHECKING:
    from main import Bot


class ReactionView(View):
    def __init__(self, bot, message, emoji):
        super().__init__(timeout=1)
        self.bot: 'Bot' = bot
        self.message: discord.Message = message
        self.emoji: Union[str, discord.Emoji] = emoji
        self.reacted_users: Set[int] = set()
        self._initialized = False

        self.reaction_button = Button(
            emoji=emoji,
            label='1',
            style=discord.ButtonStyle.grey,
            custom_id=f'reaction_{message.id}_{emoji if isinstance(emoji, str) else emoji.id}',
        )
        self.add_item(self.reaction_button)
