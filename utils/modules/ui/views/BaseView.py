from __future__ import annotations

from typing import TYPE_CHECKING, Optional, Callable, List, Dict
import discord

if TYPE_CHECKING:
    from main import Bot


class BaseView(discord.ui.View):
    """Base view class with common functionality for all views."""

    def __init__(
        self,
        bot: 'Bot',
        user: discord.User | discord.Member,
        timeout: int = 300,
    ):
        super().__init__(timeout=timeout)
        self.bot = bot
        self.user = user
        self.constants = bot.constants

    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        if interaction.user.id != self.user.id:
            await interaction.response.send_message(
                "You don't have permission to interact with this interface.", ephemeral=True
            )
            return False
        return True

    def add_back_button(
        self,
        parent_view: Optional[discord.ui.View] = None,
        parent_view_factory: Optional[Callable[[], discord.ui.View]] = None,
        embed_title: str = 'Main Menu',
        embed_description: str = 'Select an option below:',
        fields: Optional[List[Dict]] = None,
        row: int = 2,
    ):
        """Add a standardized back button with customizable embed and optional fields."""
        if not parent_view and not parent_view_factory:
            return

        back_button = discord.ui.Button(
            label='Back',
            style=discord.ButtonStyle.secondary,
            row=row,
        )

        if parent_view_factory:
            back_button.callback = self._create_dynamic_back_callback(
                parent_view_factory, embed_title, embed_description, fields
            )
        elif parent_view:
            back_button.callback = self._create_back_callback(
                parent_view, embed_title, embed_description, fields
            )

        self.add_item(back_button)

    def _create_back_callback(
        self,
        parent_view: discord.ui.View,
        embed_title: str,
        embed_description: str,
        fields: Optional[List[Dict]] = None,
    ):
        async def back_callback(interaction: discord.Interaction['Bot']):
            if not await self.interaction_check(interaction):
                return

            embed = discord.Embed(
                color=self.constants.color,
                title=embed_title,
                description=embed_description,
            )

            if fields:
                for field in fields:
                    embed.add_field(
                        name=field.get('name', 'No Name'),
                        value=field.get('value', 'No Value'),
                        inline=field.get('inline', False),
                    )

            await interaction.response.edit_message(embed=embed, view=parent_view)

        return back_callback

    def _create_dynamic_back_callback(
        self,
        parent_view_factory: Callable[[], discord.ui.View],
        embed_title: str,
        embed_description: str,
        fields: Optional[List[Dict]] = None,
    ):
        async def back_callback(interaction: discord.Interaction['Bot']):
            if not await self.interaction_check(interaction):
                return

            parent_view = parent_view_factory()
            embed = discord.Embed(
                color=self.constants.color,
                title=embed_title,
                description=embed_description,
            )

            if fields:
                for field in fields:
                    embed.add_field(
                        name=field.get('name', 'No Name'),
                        value=field.get('value', 'No Value'),
                        inline=field.get('inline', False),
                    )

            await interaction.response.edit_message(embed=embed, view=parent_view)

        return back_callback

    def add_close_button(self, row: int = 2):
        """Add a standardized close button."""
        close_button = discord.ui.Button(
            label='Close',
            style=discord.ButtonStyle.danger,
            row=row,
        )
        close_button.callback = self._create_close_callback()
        self.add_item(close_button)

    def _create_close_callback(self):
        """Create a close button callback."""

        async def close_callback(interaction: discord.Interaction['Bot']):
            if not await self.interaction_check(interaction):
                return

            embed = discord.Embed(
                color=self.constants.color,
                title='Interface Closed',
                description='This interface has been closed.',
            )
            await interaction.response.edit_message(embed=embed, view=None)

        return close_callback

    def add_refresh_button(self, refresh_callback, row: int = 2):
        """Add a standardized refresh button with custom callback."""
        refresh_button = discord.ui.Button(
            emoji='🔄',
            label='Refresh',
            style=discord.ButtonStyle.secondary,
            row=row,
        )
        refresh_button.callback = self._wrap_callback(refresh_callback)
        self.add_item(refresh_button)

    def _wrap_callback(self, callback):
        """Wrap a callback with interaction check."""

        async def wrapped_callback(interaction: discord.Interaction['Bot']):
            if not await self.interaction_check(interaction):
                return
            await callback(interaction)

        return wrapped_callback
