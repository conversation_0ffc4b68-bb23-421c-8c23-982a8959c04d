# !! TODO: NEEDS TO BE LOCALISED

from datetime import datetime
from typing import TYPE_CHECKING, Optional

import discord
from discord.ui import Button, Modal, TextInput, View, button

from utils.modules.broadcast.announce import broadcast_announcement
from utils.modules.core.db.models import Hub, HubAnnouncement
from utils.utils import ms_to_datetime, ms_to_human, parse_duration

if TYPE_CHECKING:
    from main import Bot


class OpenView(View):
    def __init__(self, bot: 'Bot', user, hub: Hub):
        super().__init__(timeout=300)
        self.bot: 'Bot' = bot
        self.user: discord.User | discord.Member = user
        self.constants = bot.constants
        self.hub: Hub = hub

        self.setup_button()

    def setup_button(self):
        self.createModal.emoji = self.bot.emotes.megaphone_icon
        self.createModal.disabled = False

    @button(emoji=None, label='Create', style=discord.ButtonStyle.grey, disabled=True)
    async def createModal(self, interaction: discord.Interaction['Bot'], button: But<PERSON>):
        await interaction.response.send_modal(CreateModal(self.bot, self.user, self.hub, False))


class CreateModal(Modal):
    def __init__(self, bot, user, hub: Hub, frequency, parent_view=None, announcement=None):
        super().__init__(title='Input Required', timeout=300)
        self.bot: 'Bot' = bot
        self.user: discord.User | discord.Member = user
        self.constants = bot.constants
        self.hub = hub
        self.frequency: bool = frequency
        self.parent_view = parent_view
        self.announcement: Optional[HubAnnouncement] = announcement

        self._setup_content_input()
        if self.frequency:
            self._setup_name_input()
            self._setup_frequency_input()

    def _setup_name_input(self):
        self.name_input: TextInput = TextInput(
            label='Identifier',
            placeholder='My cool announcement!',
            default=self.announcement.title if self.announcement is not None else '',
            max_length=100,
            required=True,
        )
        self.add_item(self.name_input)

    def _setup_content_input(self):
        self.content_input: TextInput = TextInput(
            label='Content',
            placeholder='Hey! Welcome to our hub...',
            default=self.announcement.content if self.announcement is not None else '',
            style=discord.TextStyle.paragraph,
            max_length=3000,
            min_length=5,
            required=True,
        )
        self.add_item(self.content_input)

    def _setup_frequency_input(self):
        self.frequency_input: TextInput = TextInput(
            label='Frequency',
            default=ms_to_human(self.announcement.frequencyMs)
            if self.announcement is not None
            else '',
            placeholder='1d',
            required=True,
        )
        self.add_item(self.frequency_input)

    async def on_submit(self, interaction: discord.Interaction['Bot']):  # type: ignore[override]
        await interaction.response.defer()

        if not self.frequency:
            announceEmbed = discord.Embed(
                title=f'{self.hub.name} | Official Hub Announcement',
                description=self.content_input.value,
                color=self.constants.color,
            )
            announceEmbed.set_footer(
                text='This announcement was sent by hub officials - not InterChat staff'
            )
            await broadcast_announcement(self.bot, self.hub, announceEmbed)

            sembed = discord.Embed(
                title='Success!',
                description=f'{self.bot.emotes.tick} Your broadcast has been queued and will be sent to all connected channels.',
                color=discord.Color.green(),
            )
            await interaction.edit_original_response(embed=sembed, view=None)

        else:
            freq_ms = parse_duration(self.frequency_input.value)
            next_announcement = ms_to_datetime(freq_ms)

            async with self.bot.db.get_session() as session:
                if self.announcement:
                    self.announcement.title = self.name_input.value
                    self.announcement.content = self.content_input.value
                    self.announcement.frequencyMs = freq_ms
                    self.announcement.previousAnnouncement = datetime.now()
                    self.announcement.nextAnnouncement = next_announcement
                    await session.commit()
                else:
                    announcement = HubAnnouncement(
                        hubId=self.hub['id'] if isinstance(self.hub, dict) else self.hub.id,
                        title=self.name_input.value,
                        content=self.content_input.value,
                        frequencyMs=freq_ms,
                        previousAnnouncement=datetime.now(),
                        nextAnnouncement=next_announcement,
                    )
                    session.add(announcement)
                    await session.commit()

            epoch = int(next_announcement.timestamp())
            embed = discord.Embed(
                title='Success!',
                description=(
                    f'{self.bot.emotes.tick} Your announcement has been **{"updated" if self.announcement else "saved"}** and will be **automatically sent**.'
                    f' I will next send it in <t:{epoch}:R>.',
                ),
                color=discord.Color.green(),
            )
            await interaction.followup.send(embed=embed, ephemeral=True)

        if self.parent_view:
            await self.parent_view.load_select(interaction)
