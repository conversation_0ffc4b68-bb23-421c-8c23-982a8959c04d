import discord
from discord.ui import Select, Modal, TextInput
from typing import Optional, TYPE_CHECKING


from utils.modules.core.db.models import (
    Hub,
)
from utils.modules.core.checks import is_interchat_staff_direct
from utils.modules.services.moderation.actionHandler import ModerationActionHandler

from utils.modules.services.moderation.types import ActionType, ModerationTarget
from utils.modules.core.i18n import t
from utils.utils import parse_duration
from utils.modules.common.error_utils import handle_interaction_error
from .utils import (
    DEFAULT_VIEW_TIMEOUT,
    MAX_DURATION_LENGTH,
    MAX_REASON_LENGTH,
    MIN_DURATION_LENGTH,
    MIN_REASON_LENGTH,
    BaseModerationView,
)

if TYPE_CHECKING:
    from main import Bot


class ModPanelView(BaseModerationView):
    def __init__(
        self,
        bot: 'Bot',
        moderator: discord.User | discord.Member,
        target_user: Optional[discord.User | discord.Member],
        target_server: Optional[discord.Guild],
        target_message: Optional[discord.Message],
        selected_hub: Hub,
        locale: str,
    ):
        super().__init__(bot, moderator, locale)
        self.target_user = target_user
        self.target_server = target_server
        self.target_message = target_message
        self.selected_hub = selected_hub

        self.add_item(ModActionSelect(self))

    async def handle_action_selection(self, interaction: discord.Interaction, action: str):
        """Handle when a moderation action is selected."""
        # Special case: message deletion should happen immediately without target selection or reason modal
        if action == 'delete' and self.target_message:
            handler = ModerationActionHandler(
                self.bot,
                self.moderator,
                self.selected_hub,
                self.locale,
            )

            await handler.handle_delete_message(interaction, self.target_message)
            return

        # Check if we need target selection (both user and server available)
        if self._needs_target_selection():
            await self._show_target_selection(interaction, action)
        else:
            # Open reason modal directly
            await self._open_reason_modal(interaction, action)

    def _needs_target_selection(self) -> bool:
        return self.target_user is not None and self.target_server is not None

    async def _show_target_selection(self, interaction: discord.Interaction, action: str):
        """Show the target selection view."""
        # Type guards: At this point we know both are not None
        assert self.target_user is not None
        assert self.target_server is not None

        from .targetSelection import TargetSelectionView

        view = TargetSelectionView(
            self.bot,
            self.moderator,
            self.target_user,
            self.target_server,
            self.target_message,
            self.selected_hub,
            action,
            self.locale,
        )

        embed = discord.Embed(
            title=t('ui.moderation.targetSelection.title', locale=self.locale),
            description=t(
                'ui.moderation.targetSelection.description', locale=self.locale, action=action
            ),
            color=discord.Color.blue(),
        )
        embed.add_field(
            name=t('ui.moderation.targetSelection.userField', locale=self.locale),
            value=f'{self.target_user.mention} (`{self.target_user.id}`)',
            inline=True,
        )
        embed.add_field(
            name=t('ui.moderation.targetSelection.serverField', locale=self.locale),
            value=f'**{self.target_server.name}** (`{self.target_server.id}`)',
            inline=True,
        )

        await interaction.response.send_message(embed=embed, view=view, ephemeral=True)

    async def _open_reason_modal(
        self, interaction: discord.Interaction, action: str, target_type: Optional[str] = None
    ):
        """Open the reason capture modal before executing moderation action."""
        modal = ReasonModal(self, action, target_type)
        await interaction.response.send_modal(modal)


class ModActionSelect(Select):
    """Select dropdown for moderation actions, updated for unified handler & added revoke actions."""

    def __init__(self, parent_view: ModPanelView):
        self.parent_view = parent_view
        self.locale = parent_view.locale
        options = self._build_action_options()

        super().__init__(
            placeholder=t('ui.moderation.actionSelect.placeholder', locale=self.locale),
            options=options,
            min_values=1,
            max_values=1,
        )

    def _build_action_options(self) -> list[discord.SelectOption]:
        options = []
        emotes = self.parent_view.bot.emotes

        # Add delete option if message is provided
        if self.parent_view.target_message:
            options.append(
                discord.SelectOption(
                    emoji=emotes.delete_icon,
                    label=t('ui.moderation.actions.delete.label', locale=self.locale),
                    description=t('ui.moderation.actions.delete.description', locale=self.locale),
                    value='delete',
                )
            )

        # Standard punitive actions
        options.extend(
            [
                discord.SelectOption(
                    emoji=emotes.alert_icon,
                    label=t('ui.moderation.actions.warn.label', locale=self.locale),
                    description=t('ui.moderation.actions.warn.description', locale=self.locale),
                    value='warn',
                ),
                discord.SelectOption(
                    emoji=emotes.clock_icon,
                    label=t('ui.moderation.actions.mute.label', locale=self.locale),
                    description=t('ui.moderation.actions.mute.description', locale=self.locale),
                    value='mute',
                ),
                discord.SelectOption(
                    emoji=emotes.hammer_icon,
                    label=t('ui.moderation.actions.ban.label', locale=self.locale),
                    description=t('ui.moderation.actions.ban.description', locale=self.locale),
                    value='ban',
                ),
                # Revoke actions
                discord.SelectOption(
                    emoji=getattr(emotes, 'unmute_icon', None) or '🔊',
                    label=t('ui.moderation.actions.unmute.label', locale=self.locale),
                    description=t('ui.moderation.actions.unmute.description', locale=self.locale),
                    value='unmute',
                ),
                discord.SelectOption(
                    emoji=getattr(emotes, 'unban_icon', None) or '♻️',
                    label=t('ui.moderation.actions.unban.label', locale=self.locale),
                    description=t('ui.moderation.actions.unban.description', locale=self.locale),
                    value='unban',
                ),
            ]
        )

        # Add blacklist option for staff
        if is_interchat_staff_direct(self.parent_view.bot, self.parent_view.moderator.id):
            options.append(
                discord.SelectOption(
                    emoji=emotes.hammer_icon,
                    label=t('ui.moderation.actions.blacklist.label', locale=self.locale),
                    description=t(
                        'ui.moderation.actions.blacklist.description',
                        locale=self.locale,
                    ),
                    value='blacklist',
                )
            )

        return options

    async def callback(self, interaction: discord.Interaction):
        if not await self.parent_view.validate_interaction(interaction):
            return
        action = self.values[0]
        # All actions including blacklist proceed to modal (staff gating handled when option added)
        await self.parent_view.handle_action_selection(interaction, action)


class ReasonModal(Modal):
    """Collect reason (optional for unmute/unban) and optional duration for mute."""

    def __init__(self, parent_view: ModPanelView, action: str, target_type: Optional[str]):
        super().__init__(
            title=t('ui.moderation.modal.title', parent_view.locale), timeout=DEFAULT_VIEW_TIMEOUT
        )
        self.parent_view = parent_view
        self.action = action
        self.target_type = target_type
        self.locale = parent_view.locale

        self._setup_reason_input()
        self._setup_duration_input()

    def _setup_reason_input(self):
        """Setup the reason input field."""
        reason_required = self.action not in {'unmute', 'unban'}
        optional_suffix = '' if reason_required else ' (optional)'
        placeholder = t(
            'ui.moderation.modal.reason.placeholder',
            locale=self.locale,
            optional=optional_suffix,
        )

        self.reason_input: TextInput = TextInput(
            label=t('ui.moderation.modal.reason.label', locale=self.locale),
            placeholder=placeholder,
            style=discord.TextStyle.paragraph,
            min_length=0 if not reason_required else MIN_REASON_LENGTH,
            max_length=MAX_REASON_LENGTH,
            required=reason_required,
        )
        self.add_item(self.reason_input)

    def _setup_duration_input(self):
        """Setup the duration input field if needed."""
        if self.action == 'mute':
            self.duration_input: TextInput = TextInput(
                label=t('ui.moderation.modal.duration.label', locale=self.locale),
                placeholder=t('ui.moderation.modal.duration.placeholder', locale=self.locale),
                style=discord.TextStyle.short,
                required=True,
                min_length=MIN_DURATION_LENGTH,
                max_length=MAX_DURATION_LENGTH,
            )
            self.add_item(self.duration_input)
        else:
            self.duration_input = None  # type: ignore

    async def on_submit(self, interaction: discord.Interaction['Bot']):  # type: ignore[override]
        """Handle form submission."""
        try:
            reason = self.reason_input.value.strip()
            duration_ms = await self._parse_duration_if_needed()

            if duration_ms is None and self.action == 'mute':
                return  # Error already sent

            await self._execute_moderation_action(interaction, reason, duration_ms)

        except Exception as e:
            await handle_interaction_error(
                interaction,
                e,
                user_message=t(
                    'responses.moderation.errors.processingFailed',
                    locale=self.locale,
                    error=str(e),
                ),
            )

    async def _parse_duration_if_needed(self) -> Optional[int]:
        """Parse duration if this is a mute action."""
        if self.action != 'mute' or not self.duration_input:
            return None

        return parse_duration(self.duration_input.value.strip().lower())

    async def _execute_moderation_action(
        self, interaction: discord.Interaction['Bot'], reason: str, duration_ms: Optional[int]
    ):
        """Execute the moderation action."""

        try:
            action_enum = ActionType(self.action)
        except ValueError:
            await self.parent_view.send_error(
                interaction,
                t('responses.moderation.errors.unknownAction', locale=self.locale),
            )
            return

        user_target, server_target = self._resolve_targets()
        target = ModerationTarget(user=user_target, server=server_target)

        handler = ModerationActionHandler(
            self.parent_view.bot,
            self.parent_view.moderator,
            self.parent_view.selected_hub,
            self.locale,
        )

        # Route to appropriate handler based on action type
        if action_enum == ActionType.DELETE and self.parent_view.target_message:
            await handler.handle_delete_message(
                interaction, self.parent_view.target_message, reason=reason or None
            )
        elif action_enum in {ActionType.WARN, ActionType.MUTE, ActionType.BAN}:
            await handler.handle_punitive_action(
                interaction, action_enum, target, reason or None, duration_ms=duration_ms
            )
        elif action_enum in {ActionType.UNMUTE, ActionType.UNBAN}:
            await handler.handle_revoke_action(interaction, action_enum, target)
        elif action_enum == ActionType.BLACKLIST:
            await handler.handle_global_blacklist_action(
                interaction, target, reason or None, duration_ms=duration_ms
            )
        else:
            await self.parent_view.send_error(
                interaction,
                t('responses.moderation.errors.unsupportedAction', locale=self.locale),
            )

    def _resolve_targets(
        self,
    ) -> tuple[Optional[discord.User | discord.Member], Optional[discord.Guild]]:
        """Resolve user and server targets based on target_type."""
        if self.target_type == 'user':
            return self.parent_view.target_user, None
        elif self.target_type == 'server':
            return None, self.parent_view.target_server
        else:
            # No target_type specified, use what's available
            if self.parent_view.target_user and not self.parent_view.target_server:
                return self.parent_view.target_user, None
            elif self.parent_view.target_server and not self.parent_view.target_user:
                return None, self.parent_view.target_server
            else:
                return self.parent_view.target_user, self.parent_view.target_server

    async def on_error(self, interaction: discord.Interaction, error: Exception) -> None:
        """Handle modal errors."""
        raise Exception(f'Error in ReasonModal: {error}', error)
