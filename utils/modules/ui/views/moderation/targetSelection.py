from .modPanel import ModPanelView
import discord
from discord.ui import button, But<PERSON>
from typing import Optional, TYPE_CHECKING


from utils.modules.core.db.models import (
    Hub,
)
from .utils import BaseModerationView

if TYPE_CHECKING:
    from main import Bot


class TargetSelectionView(BaseModerationView):  # noqa: F821
    """View for selecting between user and server targets after action selection."""

    def __init__(
        self,
        bot: 'Bot',
        moderator: discord.User | discord.Member,
        target_user: discord.User | discord.Member,
        target_server: discord.Guild,
        target_message: Optional[discord.Message],
        selected_hub: Hub,
        selected_action: str,
        locale: str,
    ):
        super().__init__(bot, moderator, locale)
        self.target_user = target_user
        self.target_server = target_server
        self.target_message = target_message
        self.selected_hub = selected_hub
        self.selected_action = selected_action

    @button(label='Act on User', style=discord.ButtonStyle.primary, emoji='👤')
    async def select_user(self, interaction: discord.Interaction['Bot'], button: But<PERSON>):
        """Select user as the target."""
        if not await self.validate_interaction(interaction):
            return
        # Always open reason modal
        panel_wrapper = ModPanelView(
            self.bot,
            self.moderator,
            self.target_user,
            None,
            self.target_message,
            self.selected_hub,
            self.locale,
        )
        await panel_wrapper._open_reason_modal(
            interaction, self.selected_action, target_type='user'
        )

    @button(label='Act on Server', style=discord.ButtonStyle.secondary, emoji='🏢')
    async def select_server(self, interaction: discord.Interaction['Bot'], button: Button):
        """Select server as the target."""
        if not await self.validate_interaction(interaction):
            return
        panel_wrapper = ModPanelView(
            self.bot,
            self.moderator,
            None,
            self.target_server,
            self.target_message,
            self.selected_hub,
            self.locale,
        )
        await panel_wrapper._open_reason_modal(
            interaction, self.selected_action, target_type='server'
        )
