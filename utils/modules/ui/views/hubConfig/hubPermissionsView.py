from typing import TYPE_CHECKING, List, Optional

import discord

from utils.constants import logger
from utils.modules.core.db.models import <PERSON><PERSON>, HubModerator, HubModeratorRole
from utils.modules.core.i18n import t
from utils.modules.hub.constants import <PERSON>bPermissionLevel
from utils.modules.services.permission_service import PermissionService
from utils.modules.ui.views.hubConfig.utils import (
    BaseHubView,
    DatabaseService,
    EmbedFactory,
    Messages,
)

if TYPE_CHECKING:
    from main import Bot
    from utils.modules.ui.views.hubConfig.hubConfigurationView import ConfigurationView


class HubPermissions(BaseHubView):
    """View for managing hub permissions"""

    def __init__(
        self,
        bot: 'Bot',
        user: discord.User | discord.Member,
        hub: Hub,
        locale: str,
        parent_view: 'ConfigurationView',
    ):
        super().__init__(bot, user, hub, parent_view.permission, locale)
        self.bot = bot
        self.constants = self.bot.constants
        self.parent_view = parent_view
        self.selected_user: Optional[discord.User | discord.Member] = None
        self.selected_role: Optional[str] = None

        if self.parent_view:
            self.add_back_button(self.parent_view)

    async def _setup_role_options(self) -> List[discord.SelectOption]:
        hub = await DatabaseService.get_hub_with_moderators(self.hub.id, self.bot.db)
        if not hub or not self.selected_user:
            return []

        user_id = str(self.selected_user.id)
        async with self.bot.db.get_session() as session:
            perm_service = PermissionService(session)
            user_level = await perm_service.get_user_permission_from_hub(hub, user_id)
        invoker_level = self.permission

        options = []

        # Remove option if user has permissions
        if user_level > HubPermissionLevel.NONE:
            options.append(
                discord.SelectOption(
                    emoji=self.bot.emotes.delete_icon,
                    label=t('ui.hubConfig.permissions.remove.label', locale='en'),
                    value='remove',
                )
            )

        # Add promotion options based on invoker permissions
        if invoker_level >= HubPermissionLevel.OWNER:
            options.extend(self._get_owner_options(user_level))
        elif invoker_level >= HubPermissionLevel.MANAGER:
            options.extend(self._get_manager_options(user_level))

        return options

    def _get_owner_options(self, user_level: HubPermissionLevel) -> List[discord.SelectOption]:
        """Get options available to owners"""
        options = []

        if user_level == HubPermissionLevel.NONE:
            options.extend(
                [
                    discord.SelectOption(
                        emoji=self.bot.emotes.hammer_icon,
                        label=t('ui.hubConfig.permissions.moderator.label', locale='en'),
                        description=t(
                            'ui.hubConfig.permissions.moderator.description', locale='en'
                        ),
                        value='moderator',
                    ),
                    discord.SelectOption(
                        emoji=self.bot.emotes.gear_icon,
                        label=t('ui.hubConfig.permissions.manager.label', locale='en'),
                        description=t('ui.hubConfig.permissions.manager.description', locale='en'),
                        value='manager',
                    ),
                ]
            )
        elif user_level == HubPermissionLevel.MODERATOR:
            options.append(
                discord.SelectOption(
                    emoji=self.bot.emotes.gear_icon,
                    label=t('ui.hubConfig.permissions.manager.label', locale='en'),
                    description=t('ui.hubConfig.permissions.managerOnly.description', locale='en'),
                    value='manager',
                )
            )
        elif user_level == HubPermissionLevel.MANAGER:
            options.append(
                discord.SelectOption(
                    emoji=self.bot.emotes.hammer_icon,
                    label='Moderator',
                    description='Manage messages, moderate users, handle reports & appeals',
                    value='moderator',
                )
            )

        return options

    def _get_manager_options(self, user_level: HubPermissionLevel) -> List[discord.SelectOption]:
        """Get options available to managers"""
        if user_level == HubPermissionLevel.NONE:
            return [
                discord.SelectOption(
                    emoji=self.bot.emotes.hammer_icon,
                    label='Moderator',
                    value='moderator',
                )
            ]
        return []

    @discord.ui.select(
        placeholder='Add or manage member',
        cls=discord.ui.UserSelect,
        max_values=1,
        min_values=1,
    )
    async def user_select(
        self, interaction: discord.Interaction['Bot'], select: discord.ui.UserSelect
    ):
        """Handle user selection"""
        if not await self.interaction_check(interaction):
            return

        await interaction.response.defer()

        if select.values[0] == interaction.user:
            embed = EmbedFactory.error(
                self.bot,
                'Nice Try :D',
                "You can't change your own permissions silly! Pick someone else to manage.",
            )
            return await interaction.followup.send(embed=embed, ephemeral=True)

        self.selected_user = select.values[0]

        # Update role selection options
        options = await self._setup_role_options()

        # Find and update role select menu
        for item in self.children:
            if (
                isinstance(item, discord.ui.Select)
                and item.placeholder is not None
                and 'role' in item.placeholder.lower()
            ):
                if options:
                    item.options = options
                    item.disabled = False
                    item.placeholder = 'Choose an action'
                else:
                    item.options = [
                        discord.SelectOption(label='No actions available', value='none')
                    ]
                    item.disabled = True
                    item.placeholder = 'No actions available'
                break

        await interaction.edit_original_response(view=self)

    @discord.ui.select(
        placeholder='Assign a role',
        options=[discord.SelectOption(label='Loading...', value='loading')],
        disabled=True,
        max_values=1,
        min_values=1,
    )
    async def role_select(self, interaction: discord.Interaction['Bot'], select: discord.ui.Select):
        """Handle role selection"""
        if not await self.interaction_check(interaction):
            return

        await interaction.response.defer()

        self.selected_role = select.values[0]

        # Update UI to show selection
        for option in select.options:
            option.default = option.value == select.values[0]

        # Enable confirm button
        for item in self.children:
            if isinstance(item, discord.ui.Button) and item.label == 'Confirm':
                item.disabled = False
                break

        await interaction.edit_original_response(view=self)

    @discord.ui.button(label='Confirm', style=discord.ButtonStyle.green, disabled=True)
    async def confirm_button(
        self, interaction: discord.Interaction['Bot'], button: discord.ui.Button
    ):
        """Confirm the permission change"""
        if not await self.interaction_check(interaction):
            return

        await interaction.response.defer()

        if not self.selected_user or not self.selected_role:
            await interaction.followup.send('Please select a user and role first.', ephemeral=True)
            return

        success, message = await self._apply_permission_change()

        if success:
            embed = EmbedFactory.success(self.bot, 'Permission Updated!', message)
        else:
            embed = EmbedFactory.error(self.bot, 'Failed', message)

        embed.set_footer(text=f'Hub: {self.hub.name}')
        await interaction.followup.send(embed=embed, ephemeral=True)

    async def _apply_permission_change(self) -> tuple[bool, str]:
        """Apply the selected permission change"""
        if not self.selected_user or not self.selected_role:
            return False, 'Please select a user and role first.'
        try:
            async with self.bot.db.get_session() as session:
                hub = await DatabaseService.get_hub_with_moderators(self.hub.id, self.bot.db)

                if not hub:
                    return False, Messages.HUB_NOT_FOUND

                user_id = str(self.selected_user.id)
                existing_moderator = next(
                    (mod for mod in hub.moderators if mod.userId == user_id), None
                )

                if self.selected_role == 'remove':
                    return await self._remove_moderator(session, existing_moderator)
                elif self.selected_role in ['moderator', 'manager']:
                    return await self._add_or_update_moderator(session, existing_moderator, user_id)

                return False, 'Invalid role selection'

        except Exception as e:
            logger.error(f'Permission change error: {e}')
            return False, 'An error occurred while updating permissions'

    async def _remove_moderator(self, session, existing_moderator) -> tuple[bool, str]:
        """Remove a moderator"""
        if not self.selected_user:
            return False, 'No user selected'

        if existing_moderator:
            await session.delete(existing_moderator)
            await session.commit()
            return True, f'**{self.selected_user.name}** is no longer part of your hub staff.'
        else:
            return False, f"**{self.selected_user.name}** isn't part of your hub staff team yet."

    async def _add_or_update_moderator(
        self, session, existing_moderator, user_id: str
    ) -> tuple[bool, str]:
        """Add or update a moderator"""
        if not self.selected_user:
            return False, 'No user selected'

        role_value = (
            HubModeratorRole.MODERATOR
            if self.selected_role == 'moderator'
            else HubModeratorRole.MANAGER
        )
        role_name = 'Moderator' if self.selected_role == 'moderator' else 'Manager'

        if existing_moderator:
            existing_moderator.role = role_value
            message = f'**{self.selected_user.name}** has been promoted to **{role_name}**!'
        else:
            new_moderator = HubModerator(hubId=self.hub.id, userId=user_id, role=role_value)
            session.add(new_moderator)
            message = f'**{self.selected_user.name}** is now a **{role_name}**! Your hub just got even more awesome!'

        await session.commit()
        return True, message
