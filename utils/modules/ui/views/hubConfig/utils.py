from datetime import datetime
from typing import TYPE_CHECKING, Callable, Dict, List, Optional, TypedDict

import discord
from sqlalchemy import select
from sqlalchemy.orm import selectinload

from utils.modules.common.database import DatabaseUtils
from utils.modules.core.db.models import Hub, <PERSON>bActivityLevel, HubModerator
from utils.modules.core.i18n import t
from utils.modules.hub.constants import Hub<PERSON>er<PERSON>Level
from utils.modules.ui.CustomModal import CustomModal
from utils.modules.ui.views.BaseView import BaseView

if TYPE_CHECKING:
    from main import Bot
    from utils.modules.core.db.database import Database

# ===================== CONSTANTS =====================


class Messages:
    ERROR_TITLE = t('ui.common.titles.error', locale='en')
    NOT_IMPLEMENTED = t('ui.common.messages.notImplemented', locale='en')
    HUB_NOT_FOUND = t('ui.common.messages.hubNotFound', locale='en')
    INTERFACE_PERMISSION = t('ui.common.messages.interfacePermission', locale='en')
    HUB_UPDATE_FAILED = t('ui.common.messages.hubUpdateFailed', locale='en')
    HUB_CONFIG_DESCRIPTION = t('ui.common.messages.hubConfigDescription', locale='en')


# ===================== UTILITY FUNCTIONS =====================


class HubUpdateFields(TypedDict, total=False):
    name: str
    description: str
    iconUrl: str
    shortDescription: Optional[str]
    bannerUrl: Optional[str]
    welcomeMessage: Optional[str]
    language: Optional[str]
    region: Optional[str]
    settings: int
    appealCooldownHours: int
    lastNameChange: datetime
    weeklyMessageCount: int
    private: bool
    locked: bool
    nsfw: bool
    verified: bool
    partnered: bool
    featured: bool
    rules: list[str]
    activityLevel: 'HubActivityLevel'


class DatabaseService:
    @staticmethod
    async def update_hub(bot: 'Bot', hub_id: str, updates: 'HubUpdateFields') -> bool:
        async with bot.db.get_session() as session:
            stmt = select(Hub).where(Hub.id == hub_id)
            hub = (await session.execute(stmt)).scalar()

            if not hub:
                return False

            for field, value in updates.items():
                if hasattr(hub, field):
                    if field == 'description' and (value is None or str(value).strip() == ''):
                        return False
                    setattr(hub, field, value)

            hub.updatedAt = datetime.now()

            await session.commit()
            return True

    @staticmethod
    async def get_hub_with_moderators(hub_id: str, db: 'Database') -> Optional[Hub]:
        """Get hub with moderators loaded."""
        async with db.get_session() as session:
            stmt = (
                select(Hub)
                .where(Hub.id == hub_id)
                .options(selectinload(Hub.moderators).selectinload(HubModerator.user))
            )
            return (await session.execute(stmt)).scalar()


class EmbedFactory:
    """Factory for creating standardized embeds"""

    @staticmethod
    def success(bot: 'Bot', title: str, description: str, **fields) -> discord.Embed:
        """Create a standardized success embed."""
        embed = discord.Embed(
            title=title,
            description=description,
            color=discord.Color.green(),
        )
        for field_name, field_value in fields.items():
            embed.add_field(name=field_name, value=field_value, inline=False)
        return embed

    @staticmethod
    def error(bot: 'Bot', title: str, description: str) -> discord.Embed:
        """Create a standardized error embed."""
        return discord.Embed(
            title=f'{bot.emotes.x_icon} {title}',
            description=description,
            color=discord.Color.red(),
        )


async def handle_modal_edit(
    interaction: discord.Interaction['Bot'],
    bot: 'Bot',
    hub_id: str,
    modal_title: str,
    modal_config: list,
    update_callback: Callable,
    success_title: str,
    success_description: str,
    validation_callback: Optional[Callable] = None,
):
    db_hub = await DatabaseUtils.get_hub(hub_id)
    modal = CustomModal(modal_title, modal_config)
    await interaction.response.send_modal(modal)
    timed_out = await modal.wait()
    if timed_out:
        return

    # Custom validation if provided
    if validation_callback:
        validation_result = await validation_callback(modal, db_hub)
        if validation_result is not True:
            return await interaction.followup.send(embed=validation_result, ephemeral=True)

    # Apply updates using callback
    success = await update_callback(modal, db_hub)
    if not success:
        return await interaction.followup.send('Failed to update hub.', ephemeral=True)

    # Send success confirmation
    success_embed = EmbedFactory.success(bot, success_title, success_description)
    await interaction.followup.send(embed=success_embed, ephemeral=True)


# ===================== BASE CLASSES =====================


class BaseHubView(BaseView):
    """Hub-specific view that extends BaseView."""

    def __init__(
        self,
        bot: 'Bot',
        user: discord.User | discord.Member,
        hub: 'Hub',
        permission: 'HubPermissionLevel',
        locale: str,
        timeout: int = 300,
    ):
        super().__init__(bot, user, timeout)
        self.hub = hub
        self.permission = permission
        self.locale = locale

    def add_dashboard_button(self, row: int = 2):
        """Add a standardized dashboard button for hubs."""
        dashboard_button = discord.ui.Button(
            emoji=self.bot.emotes.globe_icon,
            label='Web Dashboard',
            style=discord.ButtonStyle.link,
            url=f'https://www.interchat.tech/dashboard/hubs/{self.hub.id}',
            row=row,
        )
        self.add_item(dashboard_button)

    def add_back_button(
        self,
        parent_view: Optional[discord.ui.View] = None,
        parent_view_factory: Optional[Callable[[], discord.ui.View]] = None,
        embed_title: Optional[str] = None,
        embed_description: str = Messages.HUB_CONFIG_DESCRIPTION,
        fields: Optional[List[Dict]] = None,
        row: int = 2,
    ):
        """Override to provide hub-specific back button behavior."""
        if embed_title is None:
            embed_title = f'{self.bot.emotes.gear_icon} Hub Configuration'

        super().add_back_button(
            parent_view, parent_view_factory, embed_title, embed_description, fields, row
        )
