import discord
from discord.ui import <PERSON><PERSON>, button, TextInput

import textwrap
from utils.modules.core.i18n import t
from utils.modules.ui.views.BaseView import BaseView
from utils.modules.ui.CustomModal import CustomModal

from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from main import Bot


class Paginator(BaseView):
    PAGE_SIZE = 5

    def __init__(self, bot, user, data, locale):
        super().__init__(bot, user, timeout=300)
        self.bot: 'Bot' = bot
        self.user: discord.User | discord.Member = user
        self.original_data = data  # Keep original data
        self.data = data  # Current filtered data
        self.locale = locale
        self.current = 0
        self.current_filter = ''  # Track current filter
        self.setup_buttons()

    def setup_buttons(self):
        self.left.emoji = self.bot.emotes.arrow_left
        self.search.emoji = self.bot.emotes.search_icon
        self.right.emoji = self.bot.emotes.arrow_right
        self.update_buttons()

    def filter_data(self, search_term: str):
        if not search_term.strip():
            return self.original_data

        search_term = search_term.lower().strip()
        filtered_data = []

        for hub in self.original_data:
            searchable_text = ' '.join(
                [
                    str(hub.get('hubName', '')),
                    str(hub.get('long', '')),
                    str(hub.get('short', '')),
                ]
            ).lower()

            if search_term in searchable_text:
                filtered_data.append(hub)

        return filtered_data

    def get_embed(self) -> discord.Embed:
        if not self.data:
            title = t('ui.common.titles.error', self.locale)
            if self.current_filter:
                description = t('ui.hub.all.errors.invalidFilter', self.locale)
            else:
                description = t(
                    'ui.hub.all.errors.notFound',
                    self.locale,
                    supportServer=self.bot.constants.support_invite,
                )

            return discord.Embed(title=title, description=description, color=discord.Color.red())

        hubs = self.data[self.current : self.current + self.PAGE_SIZE]

        embed = discord.Embed(
            title=t('ui.hub.all.title', self.locale),
            description=t('ui.hub.all.description', self.locale),
            color=self.bot.constants.color,
        )

        if self.current_filter:
            embed.set_footer(
                text=t('ui.hub.all.filteredBy', self.locale, filter=self.current_filter)
            )

        for hub in hubs:
            name = hub.get('hubName') or 'Unnamed Hub'
            last_active = (
                f'<t:{int(hub["lastActive"].timestamp())}:R>'
                if hub.get('lastActive')
                else 'Unknown'
            )
            short_desc = textwrap.shorten(
                hub.get('short') or 'No short description', 50, placeholder='...'
            )
            long_desc = textwrap.shorten(hub.get('long') or 'No description', 50, placeholder='...')

            embed.add_field(
                name=name,
                value=(
                    f'> **Last Active:** {last_active}\n'
                    f'> **Short Description:** {short_desc}\n'
                    f'> **Long Description:** {long_desc}'
                ),
                inline=False,
            )

        return embed

    def update_buttons(self):
        if len(self.data) > 0:
            self.left.disabled = self.current <= 0
            self.right.disabled = (self.current + self.PAGE_SIZE) >= len(self.data)
        else:
            self.left.disabled = True
            self.right.disabled = True

        self.search.disabled = False

    @button(style=discord.ButtonStyle.gray)
    async def left(self, interaction: discord.Interaction, button: Button):
        if self.current > 0:
            self.current = max(0, self.current - self.PAGE_SIZE)
            self.update_buttons()
            await interaction.response.edit_message(embed=self.get_embed(), view=self)

    @button(style=discord.ButtonStyle.blurple)
    async def search(self, interaction: discord.Interaction, button: Button):
        modal = CustomModal(
            t('ui.hub.all.modal.title', self.locale),
            [
                (
                    'filter',
                    TextInput(
                        label=t('ui.hub.all.modal.searchFilter.label', self.locale),
                        placeholder=t('ui.hub.all.modal.searchFilter.placeholder', self.locale),
                        required=False,
                        default=self.current_filter,
                    ),
                ),
            ],
        )
        await interaction.response.send_modal(modal)
        await modal.wait()

        filter_value = modal.saved_items['filter'].value

        self.current_filter = filter_value
        self.data = self.filter_data(filter_value)
        self.current = 0
        self.update_buttons()

        await interaction.edit_original_response(embed=self.get_embed(), view=self)

    @button(emoji='▶️', style=discord.ButtonStyle.gray)
    async def right(self, interaction: discord.Interaction, button: Button):
        if self.current + self.PAGE_SIZE < len(self.data):
            self.current += self.PAGE_SIZE
            self.update_buttons()
            await interaction.response.edit_message(embed=self.get_embed(), view=self)
