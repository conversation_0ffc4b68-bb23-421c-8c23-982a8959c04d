from typing import TYPE_CHECKING, Optional

import discord
from sqlalchemy import func, or_, select
from sqlalchemy.orm import aliased

from utils.modules.core.db.models import Broadcast, HubReport, Message
from utils.modules.events.eventDispatcher import HubEvent

if TYPE_CHECKING:
    from main import Bot


def _is_image(attachment: dict) -> bool:
    content_type = (attachment.get('content_type') or '').lower()
    return content_type.startswith('image/')


async def get_report_jump_url(bot: 'Bot', report_id: str, target_server_id: str) -> Optional[str]:
    """Fetches the Discord jump URL for a given report ID, targeting a specific server."""
    async with bot.db.get_session() as session:
        ReportMessage = aliased(Message, name='report_message')
        stmt = (
            select(
                func.coalesce(Broadcast.guildId, ReportMessage.guildId),
                func.coalesce(Broadcast.channelId, ReportMessage.channelId),
                func.coalesce(Broadcast.id, ReportMessage.id),
            )
            .select_from(HubReport)
            .join(ReportMessage, HubReport.messageId == ReportMessage.id)
            .outerjoin(Broadcast, HubReport.reportedServerId == Broadcast.guildId)
            .where(
                HubReport.id == report_id,
                or_(
                    Broadcast.guildId == target_server_id, ReportMessage.guildId == target_server_id
                ),
            )
        )

        record = (await session.execute(stmt)).tuples().first()

        if not record:
            return None

        guild_id, channel_id, message_id = record
        return f'https://discord.com/channels/{guild_id}/{channel_id}/{message_id}'


def build_report_embed(bot: 'Bot', event: HubEvent) -> discord.Embed:
    if not event.report_id:
        raise ValueError('Report ID is required to build report embed')

    # Title and color
    embed = discord.Embed(
        title=f'{bot.emotes.report_icon} Message Report',
        color=discord.Color.magenta(),
        timestamp=event.timestamp,
    )

    # Evidence: original content
    if event.original_content:
        display_content = (
            event.original_content[:1024] + '…'
            if len(event.original_content) > 1024
            else event.original_content
        )
        embed.description = f'```{display_content}```' or '*none*'

    # Target context
    if event.target_user_id:
        embed.add_field(
            name='Reported User',
            value=f'<@{event.target_user_id}> (`{event.target_user_id}`)',
            inline=True,
        )
    if event.target_server_id:
        embed.add_field(
            name='Reported Server',
            value=f'{event.target_server_name or "Unknown"} (`{event.target_server_id}`)',
            inline=True,
        )

    # Reason
    if event.reason:
        embed.add_field(name='Reason', value=event.reason[:1024], inline=False)

    # Evidence: attachments
    attachments: list[dict] = (event.extra_data or {}).get('attachments') or []
    if attachments:
        # Show first image inline if available
        first_image = next((a for a in attachments if _is_image(a)), None)
        if first_image:
            embed.set_image(url=first_image.get('url'))

        # Add attachment links
        links = []
        for idx, a in enumerate(attachments[:10], start=1):
            url = a.get('url')
            filename = a.get('filename') or f'attachment_{idx}'
            if not url:
                continue
            links.append(f'[`{filename}`]({url})')
        if links:
            embed.add_field(name='Attachments', value=' '.join(links), inline=False)

    embed.set_footer(
        text=f'Reporter: {event.moderator_name} (`{event.moderator_id}`)  Hub: {event.hub_name}'
    )

    return embed
