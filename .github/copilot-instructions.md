# InterChat Discord Bot

InterChat is a Python Discord bot that enables cross-server communication through "hubs" - channels that broadcast messages across multiple Discord servers. The bot is built with discord.py, uses PostgreSQL for data storage, Dragonfly/Redis for caching, and supports internationalization with multiple locales.

Always reference these instructions first and fallback to search or bash commands only when you encounter unexpected information that does not match the info here.

## Working Effectively

### Bootstrap, Build, and Test the Repository

**Environment Setup (Required):**
```bash
# Clone and navigate to repository
git clone https://github.com/interchatapp/InterChat.py.git
cd InterChat.py

# Install uv package manager (if not installed)
pip install uv

# Install dependencies - NEVER CANCEL: Takes ~30 seconds
uv sync --frozen

# Create environment configuration
cp .env.example .env
# Edit .env with your Discord bot token, database URL, and Redis URL
```

**Linting and Code Quality:**
```bash
# Install development dependencies (includes ruff)
uv add ruff --dev

# Format code - Takes ~1 second, formats ~2-5 files typically
uv run ruff format .

# Lint code - Takes ~5-10 seconds, currently shows 224 errors
uv run ruff check .

# Fix auto-fixable lint issues
uv run ruff check . --fix
```

**Development Services Setup:**
```bash
# Start development services (Dragonfly cache, NSFW detector) - NEVER CANCEL: Takes 5-10 minutes for initial Docker pulls
docker compose -f dev/docker-compose.yml up -d dragonfly safe-content-ai

# Start PostgreSQL database - NEVER CANCEL: Takes 2-5 minutes for initial pull
docker compose -f dev/docker-compose.postgres.yml up -d

# Run database migrations (requires PostgreSQL connection)
cp alembic.example.ini alembic.ini
uv run alembic upgrade head
```

**Optional: Browser Automation Setup:**
```bash
# Install Playwright browsers - NEVER CANCEL: Takes 10-15 minutes, may fail due to network issues
uv run playwright install chromium --with-deps
```

### Running the Application

**Development Mode:**
```bash
# CRITICAL: Bot requires valid Discord credentials and database connection
# Start in development mode with hot reloading
uv run python main.py

# Bot will:
# - Load all cogs automatically
# - Enable cogwatch for hot-reloading during development  
# - Set presence to "In development"
# - Skip Sentry initialization (unless ENVIRONMENT=PRODUCTION)
```

**Common Startup Issues:**
- **Missing locale files**: Bot fails if `locales/` directory is empty. Create basic locale files or use production locale data.
- **Invalid Discord token**: Bot will fail to connect. Use valid bot token from Discord Developer Portal.
- **Database connection**: Requires PostgreSQL with proper connection string in DATABASE_URL.
- **Redis connection**: Requires Dragonfly/Redis instance for caching and rate limiting.

## Validation

**Always run these validation steps after making changes:**

1. **Lint and format code** - CI will fail with lint errors:
   ```bash
   uv run ruff format .
   uv run ruff check . --fix
   ```

2. **Test imports and basic startup**:
   ```bash
   # Test module imports (should show locale error, not import errors)
   timeout 10 uv run python main.py
   ```

3. **Database migrations** (if models changed):
   ```bash
   uv run alembic revision --autogenerate -m "Description of changes"
   uv run alembic upgrade head
   ```

**Manual Testing Scenarios:**
- **Hub Creation**: Test creating a new hub with `/hub create`
- **Message Broadcasting**: Send message in connected channel, verify it appears in other connected channels
- **Command Registration**: Verify slash commands appear and respond correctly
- **Moderation Features**: Test ban/timeout functionality across hub-connected servers

## Database & Migrations

**Database Setup:**
```bash
# Using Docker (recommended for development)
docker compose -f dev/docker-compose.postgres.yml up -d

# Create development database (if using local PostgreSQL)
createdb interchat_dev

# Run all migrations - NEVER CANCEL: May take 2-5 minutes for complex schemas
uv run alembic upgrade head
```

**Creating New Migrations:**
```bash
# Auto-generate migration from model changes
uv run alembic revision --autogenerate -m "Description of changes"

# Review generated migration in alembic/versions/ before applying
# Apply migration
uv run alembic upgrade head
```

## Key Architecture Components

### Project Structure
```
├── main.py                 # Bot entry point and main Bot class
├── cogs/                   # Discord bot commands and events
│   ├── app_commands/       # Slash commands
│   ├── modules/           # Core bot features (hubs, moderation, etc.)
│   └── tasks/             # Background tasks
├── utils/                 # Utility modules
│   ├── modules/core/      # Database, i18n, rate limiting
│   ├── modules/services/  # Business logic services
│   └── modules/ui/        # Discord UI components and views
├── alembic/              # Database migrations
├── locales/              # Internationalization files (currently empty)
├── data/                 # Static data (emoji links, locale mappings)
└── dev/                  # Docker compose files for development
```

### Important Files to Monitor
- **Database Models**: `utils/modules/core/db/models/*.py` - Changes require migrations
- **Services**: `utils/modules/services/*.py` - Core business logic
- **Bot Commands**: `cogs/` subdirectories - Discord command implementations
- **Configuration**: `.env` file - Never commit this file
- **Dependencies**: `pyproject.toml` and `uv.lock` - Managed by uv

## Development Workflow

**Making Changes:**
1. Create feature branch from main
2. Set up development environment (see Bootstrap section)
3. Make focused changes to specific files
4. Run linting and formatting: `uv run ruff format . && uv run ruff check . --fix`
5. Test bot startup and basic functionality
6. Create/update database migrations if models changed
7. Commit changes and create pull request

**Hot Reloading:**
The bot uses `cogwatch` for automatic cog reloading during development:
```python
@watch(path='cogs', preload=False)
async def on_ready(self):
    # Bot ready event with hot reloading
```

**Debugging Tools:**
- Use `jishaku` cog for live debugging (`>jsk` commands) when bot is running
- Check console logs for errors and performance info
- Enable Discord Developer Mode to copy IDs easily

## Common Issues & Solutions

**Bot won't start:**
- Verify Discord token in `.env` file
- Check database connection string
- Ensure PostgreSQL and Dragonfly services are running
- Create basic locale files in `locales/` directory

**Database errors:**
- Confirm PostgreSQL is running: `docker compose -f dev/docker-compose.postgres.yml up -d`
- Check migrations are up to date: `uv run alembic current`
- Verify DATABASE_URL format: `postgresql+asyncpg://user:pass@host:port/dbname`

**Import/module errors:**
- Ensure virtual environment is activated
- Reinstall dependencies: `uv sync --frozen`
- Check Python version (requires 3.11+)

**Commands not appearing:**
- Bot needs `applications.commands` scope in Discord
- Try `>jsk sync` to force command sync if bot is running
- Verify bot permissions in test server

## Development Requirements

**System Dependencies:**
- Python 3.11+ (project uses 3.12)
- Docker and Docker Compose
- PostgreSQL 14+ (via Docker or local install)
- Git for version control

**Python Dependencies (managed by uv):**
- discord.py for Discord API
- asyncpg for async PostgreSQL
- alembic for database migrations  
- ruff for linting and formatting
- playwright for browser automation (optional)
- redis for caching
- jishaku for debugging

**Discord Application Setup:**
1. Create Discord application at https://discord.com/developers/applications
2. Create bot user and copy token to `.env` file
3. Enable Privileged Gateway Intents: Message Content, Server Members, Presence
4. Generate OAuth2 invite URL with `bot` and `applications.commands` scopes

## Performance & Timing Expectations

- **Dependency installation**: 30 seconds (uv sync)
- **Code formatting**: 1 second (ruff format)
- **Code linting**: 5-10 seconds (ruff check)
- **Docker service startup**: 5-10 minutes (initial pulls)
- **Database migrations**: 2-5 minutes (complex schemas)
- **Playwright browser install**: 10-15 minutes (may fail due to network issues)

**NEVER CANCEL these operations** - they are expected to take significant time and canceling can leave the environment in an inconsistent state.

## Testing & Quality Assurance

**No automated test suite is present** - validation relies on manual testing and linting.

**Required validation before committing:**
1. All code must pass ruff formatting and linting
2. Bot must start without import errors
3. Database migrations must apply successfully
4. Core functionality must be manually tested

**Manual Test Scenarios:**
1. **Hub Management**: Create hub, configure settings, invite users
2. **Message Broadcasting**: Send messages across connected servers
3. **Moderation**: Test ban, timeout, and appeal systems
4. **Internationalization**: Test commands in different locales (if locale files exist)
5. **Error Handling**: Test invalid inputs and edge cases

Always test changes in a development Discord server before deploying to production.